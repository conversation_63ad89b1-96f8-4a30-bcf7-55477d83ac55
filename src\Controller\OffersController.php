<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use Cake\Core\Configure;
use Cake\I18n\FrozenTime;

/**
 * Offers Controller
 *
 * @property \App\Model\Table\OffersTable $Offers
 */
class OffersController extends AppController
{
    /**
     * Index method 
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    //protected \App\Model\Table\ShowroomsTable $Showrooms;
    protected \App\Model\Table\CategoriesTable $Categories;
    protected \App\Model\Table\ProductsTable $Products;
    protected \App\Model\Table\CustomerGroupsTable $CustomerGroups;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        //$this->Showrooms = $this->fetchTable('Showrooms');
        $this->Categories = $this->fetchTable('Categories');
        $this->Products = $this->fetchTable('Products');
        $this->CustomerGroups = $this->fetchTable('CustomerGroups');
        $this->Countries = $this->fetchTable('Countries');
    }

    public function index()
    {
        $ShowroomManagerRoleId = Configure::read('Constants.SHOWROOM_MANAGER_ROLE_ID');
        $ShowroomSupervisorRoleId = Configure::read('Constants.SHOWROOM_SUPERVISOR_ROLE_ID');

        $user = $this->Authentication->getIdentity();
        $this->storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');

        $query = $this->Offers->find()
            ->contain([
                'OfferCategories' => function ($q) {
                    return $q->where(['OfferCategories.status !=' => 'D'])
                        ->contain(['Categories']);
                },
                // 'OfferProducts' => function ($q) {
                //     return $q->where(['OfferProducts.status !=' => 'D'])
                //         ->contain(['Products']);
                // }
            ])
            ->where(['Offers.status !=' => 'D'])
            ->groupBy('Offers.Id')
            ->order(['Offers.offer_name' => 'ASC']);

        if (!empty($user)) {
            if ($user->role_id == $ShowroomManagerRoleId && $this->storeBased) {
               
            } elseif ($user->role_id == $ShowroomSupervisorRoleId && $this->storeBased) {
               
            }
        }

        $offers = $query->all();
        $redeemmodes = Configure::read('Constants.REDEEM_MODE');
        $offerType = Configure::read('Constants.OFFER_TYPE');
        $customerGroup = $this->CustomerGroups->find('list', [
            'keyField' => 'name',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->all()->toArray();

        if (empty($customerGroup)) {
            $customerGroup = ['' => 'No Customer Group available'];
        }
        // $customerGroup = Configure::read('Constants.CUSTOMER_GROUP');
        $status = Configure::read('Constants.STATUS');
        $statusMap = Configure::read('Constants.STATUS_MAP');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $this->set(compact('offers', 'redeemmodes', 'offerType', 'customerGroup', 'status', 'statusMap', 'dateFormat', 'timeFormat'));
    }

    
    public function view($id = null)
    {
        $offer = $this->Offers->find()
            // ->contain([
            //     'OfferCategories' => function ($q) {
            //         return $q->where(['OfferCategories.status !=' => 'D'])
            //             ->contain(['Categories']);
            //     },
            //     'OfferCustomerGroups' => function ($q) {
            //         return $q->where(['OfferCustomerGroups.status !=' => 'D'])
            //             ->contain(['CustomerGroups']);
            //     },
            // ])
            ->where(['Offers.id' => $id])
            ->firstOrFail();

        $selectedShowrooms = [];
        

        // $selectedCustomerGroups = [];
        // foreach ($offer->offer_customer_groups as $offerCustomerGroup) {
        //     $selectedCustomerGroups[] = $offerCustomerGroup->customer_group->name;
        // }
        // $selectedCustomerGroups = implode(', ', $selectedCustomerGroups);

        // $selectedCategories = [];
        // foreach ($offer->offer_categories as $offerCategory) {
        //     $selectedCategories[] = $offerCategory->category->name;
        // }
        // $selectedCategories = implode(', ', $selectedCategories);

        $web_image = $this->Media->getCloudFrontURL($offer->web_image);
        $mobile_image = $this->Media->getCloudFrontURL($offer->mobile_image);
        $statuses = Configure::read('Constants.STATUS');
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $redeemmodes = Configure::read('Constants.REDEEM_MODE');
        $offerType = Configure::read('Constants.OFFER_TYPE');
        $customerGroup = Configure::read('Constants.CUSTOMER_GROUP');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        // Get countries list for display
        $countries = $this->Countries->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->order(['name' => 'ASC'])->toArray();

        // Get selected country name if country_id exists
        $selectedCountryName = '';
        if (!empty($offer->country_id) && isset($countries[$offer->country_id])) {
            $selectedCountryName = $countries[$offer->country_id];
        }

        // $this->set(compact('offer', 'selectedShowrooms', 'web_image', 'mobile_image', 'statuses', 'dateFormat', 'timeFormat', 'redeemmodes', 'offerType', 'customerGroup', 'currencySymbol', 'selectedCategories', 'decimalSeparator', 'thousandSeparator', 'selectedCustomerGroups'));
        $this->set(compact('offer', 'web_image', 'mobile_image', 'statuses', 'dateFormat', 'timeFormat', 'offerType', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'countries', 'selectedCountryName'));
    }

   
    public function add()
    {
        $offer = $this->Offers->newEmptyEntity();
        $redeemmodes = Configure::read('Constants.REDEEM_MODE');
        $offerType = Configure::read('Constants.OFFER_TYPE');
        // $customerGroup = Configure::read('Constants.CUSTOMER_GROUP');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';

        $this->set([
            'webImageSize' => Configure::read('Constants.COUPON_WEB_IMAGE_SIZE'),
            'mobImageSize' => Configure::read('Constants.COUPON_MOB_IMAGE_SIZE'),
            'webImageType' => Configure::read('Constants.COUPON_WEB_IMAGE_JS_TYPE'),
            'webImageTypedisp' => Configure::read('Constants.COUPON_WEB_IMAGE_TYPE_DISP'),
            'mobImageType' => Configure::read('Constants.COUPON_MOB_IMAGE_JS_TYPE'),
            'mobImageTypedisp' => Configure::read('Constants.COUPON_MOB_IMAGE_TYPE_DISP'),
            'webImageMinWidth' => Configure::read('Constants.COUPON_WEB_IMAGE_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.COUPON_WEB_IMAGE_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.COUPON_WEB_IMAGE_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.COUPON_WEB_IMAGE_MAX_HEIGHT'),
            'mobImageMinWidth' => Configure::read('Constants.COUPON_MOB_IMAGE_MIN_WIDTH'),
            'mobImageMaxWidth' => Configure::read('Constants.COUPON_MOB_IMAGE_MAX_WIDTH'),
            'mobImageMinHeight' => Configure::read('Constants.COUPON_MOB_IMAGE_MIN_HEIGHT'),
            'mobImageMaxHeight' => Configure::read('Constants.COUPON_MOB_IMAGE_MAX_HEIGHT'),
        ]);

        $categories = $this->Categories->find('all')->where(['status' => 'A'])
            ->order(['parent_id' => 'ASC', 'name' => 'ASC'])
            ->toArray();

        $formattedCategories = $this->formatCategories($categories);

        // Get countries list for dropdown
        $countries = $this->Countries->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->order(['name' => 'ASC'])->toArray();

        if (empty($countries)) {
            $countries = ['' => 'No Countries available'];
        }

        // Get products list for dropdown
        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->order(['name' => 'ASC'])->toArray();

        if (empty($products)) {
            $products = ['' => 'No Products available'];
        }

        $this->set(compact('offer', 'redeemmodes', 'offerType', 'currencySymbol', 'formattedCategories', 'countries', 'products'));
        // $this->set(compact('offer', 'redeemmodes', 'offerType', 'customerGroup', 'currencySymbol', 'formattedCategories'));
        if ($this->request->is('post')) {
          
            $offerData = $this->request->getData();
            $existingOffer = $this->Offers->find()
                ->where([
                    'offer_code' => $offerData['offer_code'],
                    'status !=' => 'D'
                ])->first();

            if ($existingOffer) {
                $this->Flash->error(__('The offer code already exists. Please, use a different code.'));
            } else {
                $offerData = $this->request->getData();
                


                $offerData['free_shipping'] = !empty($offerData['free_shipping']) ? 1 : 0;
                $offer = $this->Offers->patchEntity($offer, $offerData);

                if ($this->Offers->save($offer)) {
                    $offerId = $offer->id;
                    $categoryIds = $this->request->getData('offer_categories._ids');
                    $productIds = $this->request->getData('offer_products._ids');

                    $this->saveOfferCategories($offerId, $categoryIds, $formattedCategories);
                    $this->saveOfferProducts($offerId, $productIds);
                   

                    $this->Flash->success(__('The offer has been saved.'));
                    return $this->redirect(['action' => 'index']);
                } else {
                    // Display specific validation errors
                    $errors = $offer->getErrors();
                    if (!empty($errors)) {
                        $errorMessages = [];
                        foreach ($errors as $field => $fieldErrors) {
                            foreach ($fieldErrors as $error) {
                                $errorMessages[] = ucfirst($field) . ': ' . $error;
                            }
                        }
                        $this->Flash->error(__('Validation errors: ') . implode(', ', $errorMessages));
                    } else {
                        $this->Flash->error(__('The offer could not be saved. Please, try again.'));
                    }
                }
            }
        }
    }

    protected function formatCategories($categories, $parentId = null, $level = 0)
    {
        $result = [];
        foreach ($categories as $category) {
            if ($category->parent_id == $parentId) {
                $result[$category->id] = [
                    'text' => str_repeat('--', $level) . ' ' . $category->name,
                    'level' => $level
                ];
                $result += $this->formatCategories($categories, $category->id, $level + 1);
            }
        }
        return $result;
    }

   
    public function edit($id = null)
    {
        $offer = $this->Offers->find()
            ->contain([
                'OfferCategories' => function ($q) {
                    return $q->where(['OfferCategories.status !=' => 'D']);
                },
                'OfferProducts' => function ($q) {
                    return $q->where(['OfferProducts.status !=' => 'D']);
                },
            ])
            ->where(['Offers.id' => $id])
            ->firstOrFail();


        $redeemmodes = Configure::read('Constants.REDEEM_MODE');
        $offerType = Configure::read('Constants.OFFER_TYPE');
        $statuses = Configure::read('Constants.STATUS');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $web_image = $this->Media->getCloudFrontURL($offer->web_image);
        $mobile_image = $this->Media->getCloudFrontURL($offer->mobile_image);

        $categories = $this->Categories->find('all')->where(['status' => 'A'])
            ->order(['parent_id' => 'ASC', 'name' => 'ASC'])
            ->toArray();

        $formattedCategories = $this->formatCategories($categories);
        $countries = $this->Countries->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->order(['name' => 'ASC'])->toArray();

        if (empty($countries)) {
            $countries = ['' => 'No Countries available'];
        }

        // Get products list for dropdown
        $products = $this->Products->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])->where(['status' => 'A'])->order(['name' => 'ASC'])->toArray();

        if (empty($products)) {
            $products = ['' => 'No Products available'];
        }

        $this->set(compact('offer', 'redeemmodes', 'offerType', 'web_image', 'mobile_image', 'statuses', 'currencySymbol', 'formattedCategories', 'countries', 'products'));

        if ($this->request->is(['patch', 'post', 'put'])) {
            $offerData = $this->request->getData();
            $settingStatus = false;
            $associatedOrder = false;
            if ($offerData['status'] != $offer['status']) {
                $settingStatus = true;
            }

            if ($this->Offers->isOfferAssociatedWithOrders($id)) {
                $associatedOrder = true;
            }

            if (!($settingStatus) && $associatedOrder) {
                $this->Flash->error(__('This offer is associated with existing orders and cannot be edited.'));
                return $this->redirect(['action' => 'index']);
            }

            $existingOffer = $this->Offers->find()
                ->where([
                    'offer_code' => $offerData['offer_code'],
                    'status !=' => 'D',
                    'id !=' => $id
                ])->first();

            if ($existingOffer) {
                $this->Flash->error(__('The offer code already exists. Please, use a different code.'));
            } else {
    
                $offerData['free_shipping'] = !empty($offerData['free_shipping']) ? 1 : 0;
                if ($settingStatus && $associatedOrder) {
                    $offer['status'] = $offerData['status'];
                } else {
                    $offer = $this->Offers->patchEntity($offer, $offerData);
                }
                if ($this->Offers->save($offer)) {
                    $offerId = $offer->id;
                    $categoryIds = $this->request->getData('offer_categories._ids');
                    $productIds = $this->request->getData('offer_products._ids');
                 
                    $this->saveOfferCategories($offerId, $categoryIds, $formattedCategories);
                    $this->saveOfferProducts($offerId, $productIds);
                  
                    $this->Flash->success(__('The offer has been saved.'));

                    return $this->redirect(['action' => 'index']);
                } else {
                    // Display specific validation errors
                    $errors = $offer->getErrors();
                    if (!empty($errors)) {
                        $errorMessages = [];
                        foreach ($errors as $field => $fieldErrors) {
                            foreach ($fieldErrors as $error) {
                                $errorMessages[] = ucfirst($field) . ': ' . $error;
                            }
                        }
                        $this->Flash->error(__('Validation errors: ') . implode(', ', $errorMessages));
                    } else {
                        $this->Flash->error(__('The offer could not be saved. Please, try again.'));
                    }
                }
            }
        }
    }


    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $offer = $this->Offers->get($id);
        $response = ['success' => false, 'message' => 'The Offer could not be deleted. Please, try again.'];
        if ($offer) {
            if ($this->Offers->delete($offer)) {
                $response = ['success' => true, 'message' => 'The Offer has been deleted.'];
            } else {
                $response = ['success' => false, 'message' => 'The Offer could not be deleted. Please, try again.'];
            }
        } else {
            $response = ['success' => false, 'message' => 'The Offer does not exist.'];
        }

        if ($this->request->is('ajax')) {
            $this->response = $this->response->withType('application/json');
            $this->response = $this->response->withStringBody(json_encode($response));
            return $this->response;
        } else {
            if ($response['success']) {
                $this->Flash->success($response['message']);
            } else {
                $this->Flash->error($response['message']);
            }
            return $this->redirect(['action' => 'index']);
        }
    }


    public function getUniqueCode()
    {
        $Coupon_code_length = Configure::read('Constants.COUPON_CODE_LENGTH');
        do {
            $Coupon_code = $this->generateRandomCode($Coupon_code_length);
        } while ($this->isCouponCodeExists($Coupon_code));
        $response = [
            'status' => 'success',
            'message' => __('Coupon not found.'),
            'code' => $Coupon_code
        ];
        $this->response = $this->response->withType('application/json');
        $this->response = $this->response->withStringBody(json_encode($response));
        return $this->response;
    }


    private function generateRandomCode($length)
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomCode = '';

        for ($i = 0; $i < $length; $i++) {
            $randomCode .= $characters[rand(0, $charactersLength - 1)];
        }

        return $randomCode;
    }

    
    private function isCouponCodeExists($code)
    {
        return $this->Offers->exists(['offer_code' => $code]);
    }

    
    protected function saveOfferShowrooms($offerId, $showroomIds)
    {
        $offerShowroomsTable = $this->fetchTable('OfferShowrooms');

        $offerShowroomsTable->updateAll(
            ['status' => 'D'],
            ['offer_id' => $offerId]
        );
        $showroomIds = is_array($showroomIds) ? array_filter($showroomIds) : [];
        if (!empty($showroomIds)) {
            $data = [];
            foreach ($showroomIds as $showroomId) {
                $data[] = [
                    'offer_id' => $offerId,
                    'showroom_id' => $showroomId,
                    'status' => 'A'
                ];
            }

            $entities = $offerShowroomsTable->newEntities($data);
            if (!$offerShowroomsTable->saveMany($entities)) {
                // Display specific validation errors for showroom associations
                $errorMessages = [];
                foreach ($entities as $entity) {
                    $errors = $entity->getErrors();
                    if (!empty($errors)) {
                        foreach ($errors as $field => $fieldErrors) {
                            foreach ($fieldErrors as $error) {
                                $errorMessages[] = 'Showroom association - ' . ucfirst($field) . ': ' . $error;
                            }
                        }
                    }
                }
                if (!empty($errorMessages)) {
                    $this->Flash->error(__('Showroom association errors: ') . implode(', ', $errorMessages));
                } else {
                    $this->Flash->error(__('The showroom associations could not be saved. Please, try again.'));
                }
            }
        }
    }

  
    public function deleteImage()
    {
        $this->request->allowMethod(['post']);

        $imageType = $this->request->getData('image_type');
        $offerId = $this->request->getData('image_id');

        if (!$imageType || !$offerId) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Invalid request']));
        }

        $offer = $this->Offers->get($offerId);

        if (!$offer) {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Offer not found']));
        }

        if ($imageType === 'web') {
            $uploadFolder = Configure::read('Constants.COUPON_WEB_IMAGE');
        } else if ($imageType === 'mobile') {
            $uploadFolder = Configure::read('Constants.COUPON_MOB_IMAGE');
        }

        $imageField = $imageType === 'web' ? 'web_image' : 'mobile_image';
        $existingImagePath = $offer->{$imageField};

        if ($existingImagePath) {
            $filePath = WWW_ROOT . $existingImagePath;
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $offer->{$imageField} = null;
            if ($this->Offers->save($offer)) {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'success', 'message' => 'Image deleted successfully']));
            } else {
                $this->response = $this->response->withType('application/json');
                return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Failed to update offer']));
            }
        } else {
            $this->response = $this->response->withType('application/json');
            return $this->response->withStringBody(json_encode(['status' => 'failure', 'message' => 'Image not found']));
        }
    }

    public function getCouponClaim()
    {
        $this->request->allowMethod(['post']);

        $couponCode      = $this->request->getData('coupon_code');
        // $dateString = $this->request->getData('order_date'); // "2025-02-23T18:14"
        // $orderDate = FrozenTime::createFromFormat("yyyy-MM-dd'T'HH:mm", $dateString);

        // if (!$orderDate) {
        //     $errors = FrozenTime::getLastErrors();
        //     throw new \Exception('Invalid order date format: ' . json_encode($errors));
        // }

        // Convert to SQL datetime format:
        $sqlDate = date('Y-m-d H:i:s');
        $subTotal        = $this->request->getData('subTotal');
        $showroomId      = $this->request->getData('showroom_id');
        $productIds      = $this->request->getData('product_ids');
        $customerGroups  = $this->request->getData('customer_groups');
        if (!is_array($customerGroups)) {

            $decoded = json_decode($customerGroups, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $customer_groups = $decoded;
            } else {

                $customer_groups = [$customerGroups];
            }
        }

        $offer = $this->Offers->find()
            ->where(['offer_code' => $couponCode, 'redeem_mode IN' => ['Store', 'Both']])
            ->contain([
                'OfferCategories' => function ($q) {
                    return $q->where(['OfferCategories.status' => 'A']);
                },
                'OfferProducts' => function ($q) {
                    return $q->where(['OfferProducts.status' => 'A']);
                },
                'OfferCustomerGroups' => function ($q) {
                    return $q->where(['OfferCustomerGroups.status' => 'A']);
                }
            ])
            ->first();

        if (!$offer) {
            return $this->jsonResponse(false, 'Invalid coupon code or it doesnot apply to stores.');
        }
        if ($offer->status !== 'A') {
            return $this->jsonResponse(false, 'This coupon is not active.');
        }

        if ($offer->offer_start_date > $sqlDate) {
            return $this->jsonResponse(false, 'This coupon is not valid yet.');
        }
        if (!empty($offer->offer_end_date) && $offer->offer_end_date < $sqlDate) {
            return $this->jsonResponse(false, 'This coupon has expired.');
        }
        

        if ($subTotal < $offer->min_cart_value) {
            return $this->jsonResponse(false, 'Minimum cart value of ₹' . $offer->min_cart_value . ' is required.');
        }

        if (!empty($customer_groups) && !empty($offer->offer_customer_groups)) {
            $validCustomerGroup = false;
            foreach ($offer->offer_customer_groups as $offerCustomerGroup) {
                $this->log(json_encode($offerCustomerGroup->customer_group_id), 'debug');
                $this->log(json_encode($customer_groups), 'debug');
                if (in_array($offerCustomerGroup->customer_group_id, $customer_groups)) {

                    $validCustomerGroup = true;
                    break;
                }
            }
            if (!$validCustomerGroup) {
                return $this->jsonResponse(false, 'This coupon is not valid for your customer group.');
            }
        }

        $validProducts = [];
        if (!empty($productIds)) {
            // Check if coupon applies to specific products
            if (!empty($offer->offer_products)) {
                foreach ($productIds as $productId) {
                    foreach ($offer->offer_products as $offerProduct) {
                        if ($offerProduct->product_id == $productId) {
                            $validProducts[] = $productId;
                        }
                    }
                }
            }

            // Check if coupon applies to categories (if no specific products or additional category-based products)
            if (!empty($offer->offer_categories)) {
                foreach ($productIds as $productId) {
                    // Skip if already validated as specific product
                    if (in_array($productId, $validProducts)) {
                        continue;
                    }

                    foreach ($offer->offer_categories as $offerCategory) {
                        if ($this->Products->ProductCategories->exists([
                            'ProductCategories.product_id'  => $productId,
                            'ProductCategories.category_id' => $offerCategory->category_id,
                        ])) {
                            $validProducts[] = $productId;
                            break; // Found in category, no need to check other categories for this product
                        }
                    }
                }
            }

            // If coupon has both categories and products restrictions, but no products match
            if (((!empty($offer->offer_categories)) || (!empty($offer->offer_products))) && empty($validProducts)) {
                return $this->jsonResponse(false, 'Selected products do not match the coupon restrictions.');
            }
        }

        return $this->jsonResponse(true, 'Coupon applied successfully!', [
            'id'            => $offer->id,
            'offer_code'  => $offer->offer_code,
            'offer_amount'  => $offer->discount,
            'offer_type'    => $offer->offer_type,
            'min_cart_value' => $offer->min_cart_value,
            'cat_products'  => $validProducts,
            'max_allowed'   => $offer->max_amt_per_disc_value,
            'is_free_shipping'   => $offer->free_shipping
        ]);
    }

    private function jsonResponse($success, $message, $data = [])
    {
        $response = ['success' => $success, 'message' => $message] + $data;
        $this->response = $this->response->withType('application/json');
        return $this->response->withStringBody(json_encode($response));
    }


    protected function saveOfferCategories($offerId, $categoryIds, $formattedCategories)
    {
        $offerCategoriesTable = $this->fetchTable('OfferCategories');

        $offerCategoriesTable->updateAll(
            ['status' => 'D'],
            ['offer_id' => $offerId]
        );
        $categoryIds = is_array($categoryIds) ? array_filter($categoryIds) : [];

        if (!empty($categoryIds)) {

            $data = [];
            foreach ($categoryIds as $categoryId) {
                $categoryLevel = $formattedCategories[$categoryId]['level'] ?? 0;

                $data[] = [
                    'offer_id' => $offerId,
                    'category_id' => $categoryId,
                    'level' => $categoryLevel,
                    'status' => 'A'
                ];
            }

            $entities = $offerCategoriesTable->newEntities($data);
            if (!$offerCategoriesTable->saveMany($entities)) {
                // Display specific validation errors for category associations
                $errorMessages = [];
                foreach ($entities as $entity) {
                    $errors = $entity->getErrors();
                    if (!empty($errors)) {
                        foreach ($errors as $field => $fieldErrors) {
                            foreach ($fieldErrors as $error) {
                                $errorMessages[] = 'Category association - ' . ucfirst($field) . ': ' . $error;
                            }
                        }
                    }
                }
                if (!empty($errorMessages)) {
                    $this->Flash->error(__('Category association errors: ') . implode(', ', $errorMessages));
                } else {
                    $this->Flash->error(__('The category associations could not be saved. Please, try again.'));
                }
            }
        }
    }

    /**
     * Save the offer_products association.
     *
     * @param int $offerId The offer ID.
     * @param array $productIds Array of product IDs.
     * @return void
     */
    protected function saveOfferProducts($offerId, $productIds)
    {
        $offerProductsTable = $this->fetchTable('OfferProducts');

        $offerProductsTable->updateAll(
            ['status' => 'D'],
            ['offer_id' => $offerId]
        );
        $productIds = is_array($productIds) ? array_filter($productIds) : [];
        if (!empty($productIds)) {
            $data = [];
            foreach ($productIds as $productId) {
                $data[] = [
                    'offer_id' => $offerId,
                    'product_id' => $productId,
                    'status' => 'A'
                ];
            }

            $entities = $offerProductsTable->newEntities($data);
            if (!$offerProductsTable->saveMany($entities)) {
                // Display specific validation errors for product associations
                $errorMessages = [];
                foreach ($entities as $entity) {
                    $errors = $entity->getErrors();
                    if (!empty($errors)) {
                        foreach ($errors as $field => $fieldErrors) {
                            foreach ($fieldErrors as $error) {
                                $errorMessages[] = 'Product association - ' . ucfirst($field) . ': ' . $error;
                            }
                        }
                    }
                }
                if (!empty($errorMessages)) {
                    $this->Flash->error(__('Product association errors: ') . implode(', ', $errorMessages));
                } else {
                    $this->Flash->error(__('The product associations could not be saved. Please, try again.'));
                }
            }
        }
    }
}
