<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<style>
    /* Modern Admin Dashboard Design */

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    g.highcharts-exporting-group {
        display: none;
    }

    .admin-dashboard {
        background: transparent;
        padding: 0;
    }

    /* Header Section */
    .dashboard-header {
        background: linear-gradient(135deg, #66ea8c 0%, #4ba293 100%);
        border-radius: 16px;
        padding: 6px 24px;
        margin-bottom: 15px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateX(-100px) translateY(-100px); }
        100% { transform: translateX(100px) translateY(100px); }
    }

    .dashboard-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 6px;
        position: relative;
        z-index: 2;
    }

    .dashboard-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        position: relative;
        z-index: 2;
        margin: 0;
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
    }

    .stat-card {
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        border: none;
        background: linear-gradient(135deg, var(--card-bg-start), var(--card-bg-end));
        color: white;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
    }

    .stat-card.orders {
        --card-bg-start: #667eea;
        --card-bg-end: #764ba2;
    }

    .stat-card.users {
        --card-bg-start: #f093fb;
        --card-bg-end: #f5576c;
    }

    .stat-card.products {
        --card-bg-start: #4facfe;
        --card-bg-end: #00f2fe;
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        pointer-events: none;
    }

    .stat-card::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100px;
        height: 100px;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .stat-content {
        flex: 1;
        position: relative;
        z-index: 2;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
        position: relative;
        z-index: 2;
        flex-shrink: 0;
        margin-left: 15px;
    }

    .stat-number {
        font-size: 2.2rem;
        font-weight: 800;
        color: white;
        margin-bottom: 4px;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .stat-label {
        font-size: 0.95rem;
        color: rgba(255,255,255,0.9);
        font-weight: 600;
        margin-bottom: 8px;
        position: relative;
        z-index: 2;
    }

    .stat-details {
        font-size: 0.85rem;
        color: rgba(255,255,255,0.8);
        line-height: 1.4;
        position: relative;
        z-index: 2;
    }

    .stat-details strong {
        color: rgba(255,255,255,0.95);
        font-weight: 600;
    }

    /* Controls Section */
    .controls-section {
        background: white;
        border-radius: 16px;
        padding: 20px 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .controls-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 15px;
    }

    .control-group {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .control-item {
        display: flex;
        flex-direction: column;
        gap: 6px;
        min-width: 180px;
        flex: 0 0 auto;
    }

    .control-label {
        font-size: 0.85rem;
        font-weight: 500;
        color: #4a5568;
    }

    .form-select, .form-control {
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        padding: 10px 14px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: white;
        min-width: 180px;
        width: auto;
    }

    .form-select:focus, .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .btn-modern {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
        font-size: 0.9rem;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }

    /* Custom Date Range */
    .date-range-section {
        background: #f8fafc;
        border-radius: 12px;
        padding: 18px;
        margin-top: 15px;
        border: 2px dashed #cbd5e0;
    }

    /* Country-based statistics styles */
    .country-stats-section {
        margin-bottom: 30px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .stats-title {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-item {
        text-align: center;
        padding: 15px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #4facfe;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        color: #ccc;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Global Filter Section */
    .global-filter-section {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .filter-title {
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .filter-controls {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        align-items: end;
    }

    .filter-item {
        display: flex;
        flex-direction: column;
    }

    .filter-label {
        color: #ccc;
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-global-filter {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-global-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .custom-date-range {
        transition: all 0.3s ease;
    }

    /* Orders Table Styling */
    .table-dark {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        overflow: hidden;
    }

    .table-dark th {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #fff;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 0.5px;
    }

    .table-dark td {
        border-color: rgba(255, 255, 255, 0.1);
        color: #ccc;
        vertical-align: middle;
    }

    .table-dark tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.08);
    }

    .badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .badge-primary { background-color: #667eea; color: white; }
    .badge-secondary { background-color: #6c757d; color: white; }
    .badge-success { background-color: #28a745; color: white; }
    .badge-danger { background-color: #dc3545; color: white; }
    .badge-warning { background-color: #ffc107; color: #212529; }
    .badge-info { background-color: #17a2b8; color: white; }
    .badge-light { background-color: #f8f9fa; color: #212529; }
    .badge-dark { background-color: #343a40; color: white; }

    @media (max-width: 768px) {
        .filter-controls {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-value {
            font-size: 20px;
        }
    }

    .date-range-grid {
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        gap: 15px;
        align-items: end;
    }

    /* Chart Section */
    .chart-section {
        background: white;
        border-radius: 16px;
        padding: 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .chart-header {
        background: linear-gradient(135deg, #cbcdd8, #5c5c5c);
        color: white;
        padding: 20px 25px;
        border-bottom: none;
    }

    .chart-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0;
    }

    .chart-body {
        padding: 25px;
        min-height: 400px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-header {
            padding: 30px 20px;
            text-align: center;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .control-group {
            flex-direction: column;
            align-items: stretch;
        }

        .date-range-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .chart-body {
            padding: 20px;
        }
    }

    /* Fix dropdown visibility */
    #chartDuration {
        color: #2d3748 !important;
        background-color: #fff !important;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
        padding-right: 40px !important;
        min-width: 200px !important;
        width: auto !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height:45px !important
    }

    #chartDuration option {
        color: #2d3748 !important;
        background-color: #fff !important;
        padding: 8px 12px;
    }

    #chartDuration:focus {
        color: #2d3748 !important;
        background-color: #fff !important;
    }

    /* Ensure all form selects have proper visibility */
    .form-select {
        color: #2d3748 !important;
        background-color: #fff !important;
    }

    .form-select option {
        color: #2d3748 !important;
        background-color: #fff !important;
    }
</style>
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<!-- Highcharts for Country-based Order Trends -->
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://code.highcharts.com/modules/export-data.js"></script>
<?php $this->end(); ?>

<div class="admin-dashboard">
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title"><?= __('Admin Dashboard') ?></h1>
            <p class="dashboard-subtitle" id="dashboard-subtitle"><?= __('Welcome back! Here\'s an overview of your business performance - All Countries') ?></p>
        </div>

        <!-- Global Filter Section -->
        <div class="global-filter-section">
            <h3 class="filter-title"><?= __('Global Dashboard Filter') ?></h3>
            <div class="filter-controls">
                <div class="filter-item">
                    <label class="filter-label"><?= __('Country') ?></label>
                    <select id="globalCountryFilter" class="form-select" onchange="applyGlobalFilter()">
                        <option value="all" selected><?= __('All Countries') ?></option>
                        <?php foreach ($countries as $country): ?>
                            <option value="<?= h($country->id) ?>"><?= h($country->name) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label"><?= __('Duration') ?></label>
                    <select id="globalDurationFilter" class="form-select" onchange="applyGlobalFilter()">
                        <option value="current_month" selected><?= __('Current Month') ?></option>
                        <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                        <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                        <option value="current_year"><?= __('Current Year') ?></option>
                        <option value="custom"><?= __('Custom Range') ?></option>
                    </select>
                </div>
                <div class="filter-item custom-date-range" id="customDateRange" style="display: none;">
                    <label class="filter-label"><?= __('From Date') ?></label>
                    <input type="date" id="globalFromDate" class="form-control" onchange="applyGlobalFilter()">
                </div>
                <div class="filter-item custom-date-range" id="customDateRange2" style="display: none;">
                    <label class="filter-label"><?= __('To Date') ?></label>
                    <input type="date" id="globalToDate" class="form-control" onchange="applyGlobalFilter()">
                </div>
                <div class="filter-item">
                    <label class="filter-label">&nbsp;</label>
                    <button class="btn-global-filter" onclick="applyGlobalFilter()">
                        <i class="fas fa-filter me-2"></i><?= __('Apply Filter') ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-grid">
            <!-- Orders Card -->
            <div class="stat-card orders">
                <div class="stat-content">
                    <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'index']); ?>">
                    <div class="stat-number" id="globalTotalOrders"><?php echo !empty($totalOrders) ? h($totalOrders) : '0'; ?></div>
                    <div class="stat-label"><?= __('Total Orders') ?></div>
                    <div class="stat-details">
                        <strong><?= __('Online') ?>:</strong> <span id="globalOnlineOrders"><?php echo !empty($onlineOrders) ? h($onlineOrders) : '0'; ?></span> •
                        <strong><?= __('Revenue') ?>:</strong> <span id="globalTotalSalesAmount"><?php echo !empty($totalSalesAmount) ? h(number_format((float)$totalSalesAmount, 2, $decimalSeparator, $thousandSeparator)) . ' ' . h($currencySymbol) : '0.00 ' . h($currencySymbol); ?></span>
                    </div>
                    </a>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>

            <!-- Users Card -->
            <div class="stat-card users">
                <div class="stat-content">
                    <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index']); ?>">
                    <div class="stat-number" id="globalTotalUsers"><?php echo !empty($totalUsers) ? h($totalUsers) : '0'; ?></div>
                    <div class="stat-label"><?= __('Total Users') ?></div>
                    <div class="stat-details">
                        <strong><?= __('New Users') ?>:</strong> <span id="globalNewUsers"><?php echo !empty($newUsers) ? h($newUsers) : '0'; ?></span>
                    </div>
                    <!-- <div class="stat-details">
                        <strong><?= __('New Users') ?>:</strong> <span id="totalNewUsers"><?php echo !empty($newUsers) ? h($newUsers) : '0'; ?></span> •
                        <strong><?= __('Active') ?>:</strong> <?= __('This Month') ?>
                    </div> -->
                  </a>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>

            <!-- Products Card -->
            <div class="stat-card products">
                <div class="stat-content">
                    <a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'index']); ?>">
                    <div class="stat-number" id="globalTotalProducts"><?php echo !empty($totalActiveProducts) ? h($totalActiveProducts) : '0'; ?></div>
                    <div class="stat-label"><?= __('Total Products') ?></div>
                    <div class="stat-details">
                        <strong><?= __('New Products') ?>:</strong> <span id="globalNewProducts"><?php echo !empty($newProducts) ? h($newProducts) : '0'; ?></span>
                    </div>
                    <!-- <div class="stat-details">
                        <strong><?= __('New Products') ?>:</strong> <span id="totalNewProducts"><?php echo !empty($newProducts) ? h($newProducts) : '0'; ?></span> •
                        <strong><?= __('Active') ?>:</strong> <?= __('This Month') ?>
                    </div> -->
                    </a>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
            </div>
        </div>
                
              
            </div>

        <!-- Controls Section -->
        <!-- <div class="controls-section">
            <h3 class="controls-title"><?= __('Chart Controls') ?></h3>
            <div class="control-group">
                <div class="control-item">
                    <label class="control-label"><?= __('Duration') ?></label>
                    <select id="chartDuration" class="form-select" onchange="handleChartDurationChange(this)">
                        <option value="current_month" selected><?= __('Current Month') ?></option>
                        <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                        <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                        <option value="current_year"><?= __('Current Year') ?></option>
                        <option value="custom"><?= __('Custom Range') ?></option>
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label">&nbsp;</label>
                    <button class="btn-modern" onclick="refreshCharts()">
                        <i class="fas fa-sync-alt me-2"></i><?= __('Refresh Charts') ?>
                    </button>
                </div>
            </div>
        </div> -->

        <!-- Country-Based Order Trends Section -->
        <div class="controls-section">
            <h3 class="controls-title"><?= __('Country-Based Order Trends') ?></h3>
            <div class="control-group">
                <div class="control-item">
                    <label class="control-label"><?= __('Select Country') ?></label>
                    <select id="countrySelect" class="form-select" onchange="updateCountryCharts()" style="height: 44px;">
                        <option value="all" selected><?= __('All Countries') ?></option>
                        <?php foreach ($countries as $country): ?>
                            <option value="<?= h($country->id) ?>"><?= h($country->name) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <!-- <div class="control-item">
                    <label class="control-label"><?= __('Chart Type') ?></label>
                    <select id="chartType" class="form-select" onchange="updateCountryCharts()" style="height: 44px;">
                        <option value="column" selected><?= __('Column Chart') ?></option>
                        <option value="bar"><?= __('Bar Chart') ?></option>
                        <option value="line"><?= __('Line Chart') ?></option>
                        <option value="area"><?= __('Area Chart') ?></option>
                    </select>
                </div> -->
                <div class="control-item">
                    <label class="control-label"><?= __('Duration') ?></label>
                    <select id="countryChartDuration" class="form-select" onchange="updateCountryCharts()" style="height: 44px;">
                        <option value="current_month" selected><?= __('Current Month') ?></option>
                        <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                        <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                        <option value="current_year"><?= __('Current Year') ?></option>
                        <!-- <option value="custom"><?= __('Custom Range') ?></option> -->
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label">&nbsp;</label>
                    <button class="btn-modern" onclick="updateCountryCharts()">
                        <i class="fas fa-chart-bar me-2"></i><?= __('Update Chart') ?>
                    </button>
                </div>
            </div>

            <!-- Custom Date Range -->
            <div class="date-range-section d-none" id="datesappear">
                <form id="dateRangeForm">
                    <div class="date-range-grid">
                        <div class="control-item">
                            <label class="control-label"><?= __('From Date') ?></label>
                            <input type="date" id="from-date" name="from-date" class="form-control" />
                        </div>
                        <div class="control-item">
                            <label class="control-label"><?= __('To Date') ?></label>
                            <input type="date" id="to-date" name="to-date" class="form-control" />
                        </div>
                        <div class="control-item">
                            <button class="btn-modern" type="submit">
                                <i class="fas fa-search me-2"></i><?= __('Apply Range') ?>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Country-Based Data Filtering Section -->
        <div class="controls-section">
            <h3 class="controls-title"><?= __('Country-Based Data Analysis') ?></h3>
            <div class="control-group">
                <div class="control-item">
                    <label class="control-label"><?= __('Select Country') ?></label>
                    <select id="dataCountrySelect" class="form-select" onchange="updateCountryData()">
                        <option value="all" selected><?= __('All Countries') ?></option>
                        <?php foreach ($countries as $country): ?>
                            <option value="<?= h($country->id) ?>"><?= h($country->name) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label"><?= __('Data Type') ?></label>
                    <select id="dataTypeSelect" class="form-select" onchange="updateCountryData()">
                        <option value="orders" selected><?= __('Orders') ?></option>
                        <option value="users"><?= __('Users') ?></option>
                        <option value="products"><?= __('Products') ?></option>
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label"><?= __('Duration') ?></label>
                    <select id="dataChartDuration" class="form-select" onchange="updateCountryData()">
                        <option value="current_month" selected><?= __('Current Month') ?></option>
                        <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                        <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                        <option value="current_year"><?= __('Current Year') ?></option>
                        <option value="custom"><?= __('Custom Range') ?></option>
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label">&nbsp;</label>
                    <button class="btn-modern" onclick="updateCountryData()">
                        <i class="fas fa-filter me-2"></i><?= __('Filter Data') ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Orders Table Display -->
        <div class="chart-section mt-4">
            <div class="chart-header">
                <h4 class="chart-title" id="ordersTableTitle"><?= __('Recent Orders - All Countries') ?></h4>
            </div>
            <div class="chart-body">
                <div id="orders_table_container">
                    <div class="table-responsive">
                        <table class="table table-dark table-striped" id="ordersTable">
                            <thead>
                                <tr>
                                    <th><?= __('Order #') ?></th>
                                    <th><?= __('Date') ?></th>
                                    <th><?= __('Customer') ?></th>
                                    <th><?= __('Country') ?></th>
                                    <th><?= __('Amount') ?></th>
                                    <th><?= __('Type') ?></th>
                                    <th><?= __('Status') ?></th>
                                </tr>
                            </thead>
                            <tbody id="ordersTableBody">
                                <tr>
                                    <td colspan="7" class="text-center">
                                        <i class="fa fa-spinner fa-spin fa-2x text-primary"></i><br>
                                        <span class="text-muted">Loading orders...</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Country-Based Order Trends Chart -->
        <div class="chart-section mt-4">
            <div class="chart-header">
                <h4 class="chart-title" id="countryChartTitle"><?= __('Country-Based Order Trends - All Countries') ?></h4>
            </div>
            <div class="chart-body">
                <div id="country_order_trends_chart" style="min-height: 400px;"></div>
            </div>
        </div>


    </div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<!-- <script src="< ?= $this->Url->webroot('js/page/chart-apexcharts.js') ?>"></script> -->
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>



    // ==================== COUNTRY-BASED ORDER TRENDS ====================

    // Global variables for country charts
    let countryOrderTrendsChart = null;
    let countrySummaryChart = null;

    /**
     * Initialize country-based charts on page load
     */
    function initializeCountryCharts() {
        updateCountryCharts();
    }

    /**
     * Update country-based order trends chart
     */
    function updateCountryCharts() {
        const countryId = $('#countrySelect').val();
        // const chartType = $('#chartType').val();
        const chartType ='column';
        const duration = $('#countryChartDuration').val();
         if (duration === 'custom') {
        document.getElementById('datesappear').classList.remove('d-none');
    } else {
        document.getElementById('datesappear').classList.add('d-none');
        // Auto-refresh charts when duration changes (except custom)
        
    }

        // Show loading state
        $('#country_order_trends_chart').html('<div class="text-center p-4"><i class="fa fa-spinner fa-spin fa-2x text-primary"></i><br><span class="text-muted">Loading chart...</span></div>');

        // Build URL with parameters
        const url = '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'getCountryOrderTrends']) ?>';
        const params = new URLSearchParams({
            country_id: countryId,
            duration: duration
        });

        // Add custom date range if selected
        if (duration === 'custom') {
            const fromDate = $('#fromDate').val();
            const toDate = $('#toDate').val();
            if (fromDate && toDate) {
                params.append('fromDate', fromDate);
                params.append('toDate', toDate);
            }
        }

        $.ajax({
            url: url + '?' + params.toString(),
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderCountryOrderTrendsChart(response.data, chartType);

                    // Update chart title with additional info for multi-country view
                    let titleText = response.data.title;
                    if (response.data.multiCountry) {
                        // titleText += ' (Each Country in Different Color)';
                    }
                    $('#countryChartTitle').text(titleText);
                } else {
                    $('#country_order_trends_chart').html('<div class="text-center p-4 text-danger">Failed to load chart data</div>');
                }
            },
            error: function() {
                $('#country_order_trends_chart').html('<div class="text-center p-4 text-danger">Error loading chart data</div>');
            }
        });
    }

    /**
     * Render country order trends chart using Highcharts
     */
    function renderCountryOrderTrendsChart(data, chartType) {
        // Destroy existing chart
        if (countryOrderTrendsChart) {
            countryOrderTrendsChart.destroy();
        }

        // Configure chart based on whether it's multi-country or single country
        let chartConfig = {
            chart: {
                type: chartType,
                height: 400,
                backgroundColor: 'transparent'
            },
            title: {
                text: null
            },
            subtitle: {
                text: data.subtitle,
                style: {
                    color: '#666'
                }
            },
            xAxis: {
                categories: data.categories,
                title: {
                    text: 'Time Period'
                }
            },
            tooltip: {
                shared: true,
                formatter: function() {
                    let tooltip = '<b>' + this.x + '</b><br/>';
                    this.points.forEach(function(point) {
                        if (point.series.name.includes('Total Amount')) {
                            tooltip += '<span style="color:' + point.color + '">' + point.series.name + '</span>: <b>' +
                                      new Intl.NumberFormat('en-US', { minimumFractionDigits: 2 }).format(point.y) + ' QAR</b><br/>';
                        } else {
                            tooltip += '<span style="color:' + point.color + '">' + point.series.name + '</span>: <b>' + point.y + '</b><br/>';
                        }
                    });
                    return tooltip;
                }
            },
            legend: {
                enabled: true,
                layout: 'horizontal',
                align: 'center',
                verticalAlign: 'bottom'
            },
            credits: {
                enabled: false
            },
            exporting: {
                enabled: true,
                buttons: {
                    contextButton: {
                        menuItems: ['downloadPNG', 'downloadJPEG', 'downloadPDF', 'downloadSVG']
                    }
                }
            },
            series: []
        };

        if (data.multiCountry) {
            // Multi-country view - each country gets its own color
            chartConfig.yAxis = {
                title: {
                    text: 'Order Count',
                    style: {
                        color: '#333'
                    }
                },
                labels: {
                    style: {
                        color: '#333'
                    }
                }
            };

            // Use the series data directly from backend (each country has its own color)
            chartConfig.series = data.series.map(series => ({
                ...series,
                type: chartType
            }));

            // For multi-country, adjust legend position if many countries
            if (data.series.length > 5) {
                chartConfig.legend.layout = 'vertical';
                chartConfig.legend.align = 'right';
                chartConfig.legend.verticalAlign = 'middle';
                chartConfig.chart.marginRight = 200;
            }

        } else {
            // Single country view - dual axis with order count and total amount
            chartConfig.yAxis = [{
                title: {
                    text: 'Order Count',
                    style: {
                        color: '#667eea'
                    }
                },
                labels: {
                    style: {
                        color: '#667eea'
                    }
                }
            }, {
                title: {
                    text: 'Total Amount',
                    style: {
                        color: '#f093fb'
                    }
                },
                labels: {
                    style: {
                        color: '#f093fb'
                    }
                },
                opposite: true
            }];

            // Add series based on chart type for single country
            if (chartType === 'column' || chartType === 'bar') {
                chartConfig.series = [{
                    name: 'Order Count',
                    data: data.series[0].data,
                    color: '#667eea',
                    yAxis: 0
                }, {
                    name: 'Total Amount',
                    data: data.series[1].data,
                    color: '#f093fb',
                    yAxis: 1,
                    type: 'line'
                }];
            } else {
                chartConfig.series = data.series.map(series => ({
                    ...series,
                    type: chartType
                }));
            }
        }

        // Create the chart
        countryOrderTrendsChart = Highcharts.chart('country_order_trends_chart', chartConfig);
    }

   
    // Initialize country charts when document is ready
    $(document).ready(function() {
        // Initialize existing charts first, then country charts
        setTimeout(function() {
            initializeCountryCharts();
            // Load initial orders table
            applyGlobalFilter();
        }, 1000);

        // Handle custom date range visibility
        $('#globalDurationFilter').on('change', function() {
            if ($(this).val() === 'custom') {
                $('.custom-date-range').show();
            } else {
                $('.custom-date-range').hide();
            }
        });
    });

    // ==================== GLOBAL DASHBOARD FILTERING ====================

    /**
     * Apply global filter to entire dashboard
     */
    function applyGlobalFilter() {
        const countryId = $('#globalCountryFilter').val();
        const duration = $('#globalDurationFilter').val();
        const fromDate = $('#globalFromDate').val();
        const toDate = $('#globalToDate').val();

        // Show loading state for cards
        showGlobalLoadingState();

        // Build URL with parameters
        const url = '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'getGlobalDashboardData']) ?>';
        const params = new URLSearchParams({
            country_id: countryId,
            duration: duration
        });

        // Add custom date range if selected
        if (duration === 'custom' && fromDate && toDate) {
            params.append('fromDate', fromDate);
            params.append('toDate', toDate);
        }

        $.ajax({
            url: url + '?' + params.toString(),
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log('Global dashboard response:', response);
                if (response.success) {
                    updateGlobalDashboard(response.data);
                } else {
                    showGlobalError('Failed to load dashboard data: ' + (response.error || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Global dashboard AJAX Error:', { xhr, status, error });
                showGlobalError('Error loading dashboard data: ' + error);
            }
        });
    }

    /**
     * Show loading state for global dashboard
     */
    function showGlobalLoadingState() {
        // Update cards with loading state
        $('#globalTotalOrders').html('<i class="fa fa-spinner fa-spin"></i>');
        $('#globalOnlineOrders').html('<i class="fa fa-spinner fa-spin"></i>');
        $('#globalTotalSalesAmount').html('<i class="fa fa-spinner fa-spin"></i>');
        $('#globalTotalUsers').html('<i class="fa fa-spinner fa-spin"></i>');
        $('#globalNewUsers').html('<i class="fa fa-spinner fa-spin"></i>');
        $('#globalTotalProducts').html('<i class="fa fa-spinner fa-spin"></i>');
        $('#globalNewProducts').html('<i class="fa fa-spinner fa-spin"></i>');

        // Update orders table
        $('#ordersTableBody').html(`
            <tr>
                <td colspan="7" class="text-center">
                    <i class="fa fa-spinner fa-spin fa-2x text-primary"></i><br>
                    <span class="text-muted">Loading orders...</span>
                </td>
            </tr>
        `);
    }

    /**
     * Update global dashboard with new data
     */
    function updateGlobalDashboard(data) {
        // Update dashboard subtitle
        $('#dashboard-subtitle').text(`Welcome back! Here's an overview of your business performance - ${data.countryName}`);

        // Update orders table title
        $('#ordersTableTitle').text(`Recent Orders - ${data.countryName}`);

        // Update stat cards
        $('#globalTotalOrders').text(data.totalOrders || 0);
        $('#globalOnlineOrders').text(data.onlineOrders || 0);
        $('#globalTotalSalesAmount').text(formatCurrency(data.totalSales || 0));
        $('#globalTotalUsers').text(data.totalUsers || 0);
        $('#globalNewUsers').text(data.newUsers || 0);
        $('#globalTotalProducts').text(data.totalActiveProducts || 0);
        $('#globalNewProducts').text(data.newProducts || 0);

        // Update orders table
        updateOrdersTable(data.ordersData || []);

        console.log('Global dashboard updated successfully');
    }

    /**
     * Update orders table with new data
     */
    function updateOrdersTable(orders) {
        let tableHtml = '';

        if (orders.length === 0) {
            tableHtml = `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i><br>
                        No orders found for the selected criteria
                    </td>
                </tr>
            `;
        } else {
            orders.forEach(function(order) {
                const statusClass = getStatusClass(order.status);
                const typeClass = order.order_type === 'Online' ? 'badge-primary' : 'badge-secondary';

                tableHtml += `
                    <tr>
                        <td><strong>#${order.order_number || order.id}</strong></td>
                        <td>${formatDate(order.order_date)}</td>
                        <td>${order.customer_name || 'N/A'}</td>
                        <td>${order.country_name || 'N/A'}</td>
                        <td><strong>${formatCurrency(order.total_amount || 0)}</strong></td>
                        <td><span class="badge ${typeClass}">${order.order_type || 'N/A'}</span></td>
                        <td><span class="badge ${statusClass}">${order.status || 'N/A'}</span></td>
                    </tr>
                `;
            });
        }

        $('#ordersTableBody').html(tableHtml);
    }

    /**
     * Get CSS class for order status
     */
    function getStatusClass(status) {
        switch (status) {
            case 'Pending': return 'badge-warning';
            case 'Confirmed': return 'badge-info';
            case 'Processing': return 'badge-primary';
            case 'Shipped': return 'badge-secondary';
            case 'Out for Delivery': return 'badge-info';
            case 'Delivered': return 'badge-success';
            case 'Rejected': return 'badge-danger';
            case 'Cancelled': return 'badge-dark';
            default: return 'badge-light';
        }
    }

    /**
     * Format date for display
     */
    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * Show global error message
     */
    function showGlobalError(message) {
        // Update cards with error state
        const errorIcon = '<i class="fa fa-exclamation-triangle text-danger"></i>';
        $('#globalTotalOrders').html(errorIcon);
        $('#globalOnlineOrders').html(errorIcon);
        $('#globalTotalSalesAmount').html(errorIcon);
        $('#globalTotalUsers').html(errorIcon);
        $('#globalNewUsers').html(errorIcon);
        $('#globalTotalProducts').html(errorIcon);
        $('#globalNewProducts').html(errorIcon);

        // Update orders table
        $('#ordersTableBody').html(`
            <tr>
                <td colspan="7" class="text-center text-danger">
                    <i class="fa fa-exclamation-triangle fa-2x mb-2"></i><br>
                    ${message}
                </td>
            </tr>
        `);
    }

    // ==================== COUNTRY-BASED DATA FILTERING ====================

    /**
     * Update country-based data display
     */
    function updateCountryData() {
        const countryId = $('#dataCountrySelect').val();
        const dataType = $('#dataTypeSelect').val();
        const duration = $('#dataChartDuration').val();

        // Show loading state
        $('#country_data_display').html('<div class="text-center p-4"><i class="fa fa-spinner fa-spin fa-2x text-primary"></i><br><span class="text-muted">Loading data...</span></div>');

        // Build URL with parameters
        const url = '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'getCountryBasedData']) ?>';
        const params = new URLSearchParams({
            country_id: countryId,
            data_type: dataType,
            duration: duration
        });

        // Add custom date range if selected
        if (duration === 'custom') {
            const fromDate = $('#from-date').val();
            const toDate = $('#to-date').val();
            if (fromDate && toDate) {
                params.append('fromDate', fromDate);
                params.append('toDate', toDate);
            }
        }

        $.ajax({
            url: url + '?' + params.toString(),
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    console.log('data');
                    console.log(response);
                    displayCountryData(response.data, dataType, countryId);
                    updateCountryDataTitle(response.data.countryName, dataType);
                } else {
                    $('#country_data_display').html('<div class="text-center p-4 text-danger">Failed to load data</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', { xhr, status, error });
                console.error('Response Text:', xhr.responseText);
                $('#country_data_display').html('<div class="text-center p-4 text-danger">Error loading data: ' + error + '</div>');
            }
        });
    }

    /**
     * Display country-based data based on type
     */
    function displayCountryData(data, dataType, countryId) {
        console.log('displayCountryData called with:', { data, dataType, countryId });

        // Create the complete HTML structure
        let html = '';

        switch (dataType) {
            case 'orders':
                html = createOrdersHTML(data);
                console.log('Orders HTML created:', html.substring(0, 200) + '...');
                break;
            case 'users':
                html = createUsersHTML(data);
                console.log('Users HTML created:', html.substring(0, 200) + '...');
                break;
            case 'products':
                html = createProductsHTML(data);
                console.log('Products HTML created:', html.substring(0, 200) + '...');
                break;
            default:
                console.error('Unknown data type:', dataType);
                html = '<div class="text-center p-4 text-danger">Unknown data type: ' + dataType + '</div>';
        }

        console.log('Setting HTML to #country_data_display');
        // Replace the entire content
        $('#country_data_display').html(html);
        console.log('HTML set successfully');
    }

    /**
     * Create orders statistics HTML
     */
    function createOrdersHTML(data) {
        let ordersHtml = `
            <div id="orders-stats" class="country-stats-section">
                <h5 class="stats-title"><i class="fas fa-shopping-cart me-2"></i>Orders Statistics</h5>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${data.totalOrders || 0}</div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${formatCurrency(data.totalSales || 0)}</div>
                        <div class="stat-label">Total Sales</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.onlineOrders || 0}</div>
                        <div class="stat-label">Online Orders</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.showroomOrders || 0}</div>
                        <div class="stat-label">Showroom Orders</div>
                    </div>
                </div>
        `;

        // Add order status breakdown if available
        if (data.ordersByStatus && data.ordersByStatus.length > 0) {
            ordersHtml += `
                <div class="mt-4">
                    <h6 class="text-white mb-3">Orders by Status</h6>
                    <div class="stats-grid">
            `;

            data.ordersByStatus.forEach(function(status) {
                ordersHtml += `
                    <div class="stat-item">
                        <div class="stat-value">${status.count}</div>
                        <div class="stat-label">${status.status}</div>
                    </div>
                `;
            });

            ordersHtml += `
                    </div>
                </div>
            `;
        }

        ordersHtml += `</div>`;
        return ordersHtml;
    }

    /**
     * Create users statistics HTML
     */
    function createUsersHTML(data) {
        let usersHtml = `
            <div id="users-stats" class="country-stats-section">
                <h5 class="stats-title"><i class="fas fa-users me-2"></i>Users Statistics</h5>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${data.totalUsers || 0}</div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.newUsers || 0}</div>
                        <div class="stat-label">New Users</div>
                    </div>
                </div>
        `;

        // Add users by role breakdown if available
        if (data.usersByRole && data.usersByRole.length > 0) {
            usersHtml += `
                <div class="mt-4">
                    <h6 class="text-white mb-3">Users by Role</h6>
                    <div class="stats-grid">
            `;

            data.usersByRole.forEach(function(role) {
                usersHtml += `
                    <div class="stat-item">
                        <div class="stat-value">${role.count}</div>
                        <div class="stat-label">${role.role_name || 'Unknown'}</div>
                    </div>
                `;
            });

            usersHtml += `
                    </div>
                </div>
            `;
        }

        usersHtml += `</div>`;
        return usersHtml;
    }

    /**
     * Create products statistics HTML
     */
    function createProductsHTML(data) {
        let productsHtml = `
            <div id="products-stats" class="country-stats-section">
                <h5 class="stats-title"><i class="fas fa-box me-2"></i>Products Statistics</h5>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${data.totalProductsSold || 0}</div>
                        <div class="stat-label">Products Sold</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.uniqueProducts || 0}</div>
                        <div class="stat-label">Unique Products</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${data.totalActiveProducts || 0}</div>
                        <div class="stat-label">Active Products</div>
                    </div>
                </div>
        `;

        // Add top products if available
        if (data.topProducts && data.topProducts.length > 0) {
            productsHtml += `
                <div class="mt-4">
                    <h6 class="text-white mb-3">Top Selling Products</h6>
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr>
                                    <th>Product Name</th>
                                    <th>Quantity Sold</th>
                                    <th>Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            data.topProducts.slice(0, 5).forEach(function(product) {
                productsHtml += `
                    <tr>
                        <td>${product.product_name || 'Unknown'}</td>
                        <td>${product.total_sold || 0}</td>
                        <td>${formatCurrency(product.total_revenue || 0)}</td>
                    </tr>
                `;
            });

            productsHtml += `
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        productsHtml += `</div>`;
        return productsHtml;
    }

    /**
     * Update country data title
     */
    function updateCountryDataTitle(countryName, dataType) {
        const dataTypeLabel = dataType.charAt(0).toUpperCase() + dataType.slice(1);
        $('#countryDataTitle').text(`${dataTypeLabel} Statistics - ${countryName}`);
    }

    /**
     * Format currency for display
     */
    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount) + ' QAR';
    }


</script>
<?php $this->end(); ?>