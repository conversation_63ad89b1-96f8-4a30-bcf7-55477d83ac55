<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<style>
    /* Modern Admin Dashboard Design */

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .admin-dashboard {
        background: transparent;
        padding: 0;
    }

    /* Header Section */
    .dashboard-header {
        background: linear-gradient(135deg, #66ea8c 0%, #4ba293 100%);
        border-radius: 16px;
        padding: 6px 24px;
        margin-bottom: 15px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateX(-100px) translateY(-100px); }
        100% { transform: translateX(100px) translateY(100px); }
    }

    .dashboard-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 6px;
        position: relative;
        z-index: 2;
    }

    .dashboard-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        position: relative;
        z-index: 2;
        margin: 0;
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
    }

    .stat-card {
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.12);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        border: none;
        background: linear-gradient(135deg, var(--card-bg-start), var(--card-bg-end));
        color: white;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
    }

    .stat-card.orders {
        --card-bg-start: #667eea;
        --card-bg-end: #764ba2;
    }

    .stat-card.users {
        --card-bg-start: #f093fb;
        --card-bg-end: #f5576c;
    }

    .stat-card.products {
        --card-bg-start: #4facfe;
        --card-bg-end: #00f2fe;
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        pointer-events: none;
    }

    .stat-card::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100px;
        height: 100px;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .stat-content {
        flex: 1;
        position: relative;
        z-index: 2;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
        position: relative;
        z-index: 2;
        flex-shrink: 0;
        margin-left: 15px;
    }

    .stat-number {
        font-size: 2.2rem;
        font-weight: 800;
        color: white;
        margin-bottom: 4px;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .stat-label {
        font-size: 0.95rem;
        color: rgba(255,255,255,0.9);
        font-weight: 600;
        margin-bottom: 8px;
        position: relative;
        z-index: 2;
    }

    .stat-details {
        font-size: 0.85rem;
        color: rgba(255,255,255,0.8);
        line-height: 1.4;
        position: relative;
        z-index: 2;
    }

    .stat-details strong {
        color: rgba(255,255,255,0.95);
        font-weight: 600;
    }

    /* Controls Section */
    .controls-section {
        background: white;
        border-radius: 16px;
        padding: 20px 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    }

    .controls-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 15px;
    }

    .control-group {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .control-item {
        display: flex;
        flex-direction: column;
        gap: 6px;
        min-width: 180px;
        flex: 0 0 auto;
    }

    .control-label {
        font-size: 0.85rem;
        font-weight: 500;
        color: #4a5568;
    }

    .form-select, .form-control {
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        padding: 10px 14px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: white;
        min-width: 180px;
        width: auto;
    }

    .form-select:focus, .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .btn-modern {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
        font-size: 0.9rem;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }

    /* Custom Date Range */
    .date-range-section {
        background: #f8fafc;
        border-radius: 12px;
        padding: 18px;
        margin-top: 15px;
        border: 2px dashed #cbd5e0;
    }

    .date-range-grid {
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        gap: 15px;
        align-items: end;
    }

    /* Chart Section */
    .chart-section {
        background: white;
        border-radius: 16px;
        padding: 0;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .chart-header {
        background: linear-gradient(135deg, #cbcdd8, #5c5c5c);
        color: white;
        padding: 20px 25px;
        border-bottom: none;
    }

    .chart-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0;
    }

    .chart-body {
        padding: 25px;
        min-height: 400px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .dashboard-header {
            padding: 30px 20px;
            text-align: center;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .control-group {
            flex-direction: column;
            align-items: stretch;
        }

        .date-range-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .chart-body {
            padding: 20px;
        }
    }

    /* Fix dropdown visibility */
    #chartDuration {
        color: #2d3748 !important;
        background-color: #fff !important;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
        padding-right: 40px !important;
        min-width: 200px !important;
        width: auto !important;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        height:45px !important
    }

    #chartDuration option {
        color: #2d3748 !important;
        background-color: #fff !important;
        padding: 8px 12px;
    }

    #chartDuration:focus {
        color: #2d3748 !important;
        background-color: #fff !important;
    }

    /* Ensure all form selects have proper visibility */
    .form-select {
        color: #2d3748 !important;
        background-color: #fff !important;
    }

    .form-select option {
        color: #2d3748 !important;
        background-color: #fff !important;
    }
</style>
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<?php $this->end(); ?>

<div class="admin-dashboard">
    <div class="container-fluid">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title"><?= __('Admin Dashboard') ?></h1>
            <p class="dashboard-subtitle"><?= __('Welcome back! Here\'s an overview of your business performance.') ?></p>
        </div>

        <!-- Stats Cards -->
        <div class="stats-grid">
            <!-- Orders Card -->
            <div class="stat-card orders">
                <div class="stat-content">
                    <a href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'index']); ?>">
                    <div class="stat-number" id="totalOrders"><?php echo !empty($totalOrders) ? h($totalOrders) : '0'; ?></div>
                    <div class="stat-label"><?= __('Total Orders') ?></div>
                    <div class="stat-details">
                        <!-- <strong><?= __('Online') ?>:</strong> <span id="onlineOrders"><?php echo !empty($onlineOrders) ? h($onlineOrders) : '0'; ?></span> • -->
                        <strong><?= __('Revenue') ?>:</strong> <span id="totalSalesAmount"><?php echo !empty($totalSalesAmount) ? h(number_format((float)$totalSalesAmount, 2, $decimalSeparator, $thousandSeparator)) . ' ' . h($currencySymbol) : '0.00 ' . h($currencySymbol); ?></span>
                    </div>
                    </a>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>

            <!-- Users Card -->
            <div class="stat-card users">
                <div class="stat-content">
                    <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index']); ?>">
                    <div class="stat-number" id="totalUsers"><?php echo !empty($totalUsers) ? h($totalUsers) : '0'; ?></div>
                    <div class="stat-label"><?= __('Total Users') ?></div>
                    <!-- <div class="stat-details">
                        <strong><?= __('New Users') ?>:</strong> <span id="totalNewUsers"><?php echo !empty($newUsers) ? h($newUsers) : '0'; ?></span> •
                        <strong><?= __('Active') ?>:</strong> <?= __('This Month') ?>
                    </div> -->
                  </a>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>

            <!-- Products Card -->
            <div class="stat-card products">
                <div class="stat-content">
                    <a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'index']); ?>">
                    <div class="stat-number" id="totalProducts"><?php echo !empty($totalActiveProducts) ? h($totalActiveProducts) : '0'; ?></div>
                    <div class="stat-label"><?= __('Total Products') ?></div>
                    <!-- <div class="stat-details">
                        <strong><?= __('New Products') ?>:</strong> <span id="totalNewProducts"><?php echo !empty($newProducts) ? h($newProducts) : '0'; ?></span> •
                        <strong><?= __('Active') ?>:</strong> <?= __('This Month') ?>
                    </div> -->
                    </a>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
            </div>
        </div>
                <!-- <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
                    <div class="stats-card showrooms-card">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="card-title"><?= __('Total Showrooms') ?></div>
                                <h3 id="totalShowrooms"><?php echo !empty($totalShowrooms) ? h($totalShowrooms) : '0'; ?></h3>
                                <div class="d-flex justify-content-between text-sm mb-2">
                                    <span><?= __('New Showrooms:') ?> <span id="totalNewShowrooms"><?php echo !empty($newShowrooms) ? h($newShowrooms) : '0'; ?></span></span>
                                    <span><?= __('Growth:') ?> <?= h($newShowroomsPercentage) ?>%</span>
                                </div>
                                <div class="progress">
                                    <div id="showroomProgressBar" class="progress-bar" style="width: <?= h($newShowroomsPercentage) ?>%" aria-valuenow="<?= h($newShowroomsPercentage) ?>"></div>
                                </div>
                                <div class="card-subtitle" id="showroomText"><?= __('New showrooms since this month') ?></div>
                            </div>
                            <div class="text-right">
                                <i class="fas fa-store fa-2x" style="opacity: 0.3;"></i>
                            </div>
                        </div>
                    </div>
                </div> -->
                <?php /*
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style4 dashboard_box">
                        <div class="card-statistic-3 bg4">
                            <div class="card-icon card-icon-large"><i class="fa fa-money-bill-alt"></i>
                            </div>

                            <div class="card-content">
                                <h4 class="card-title"><?= __('Showrooms') ?></h4>
                                <span id="totalShowrooms"><?php echo !empty($totalShowrooms) ? h($totalShowrooms) : '0'; ?> <?= __('Showrooms') ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="showroomProgressBar" role="progressbar" data-width="<?= $newShowroomsPercentage ?>%"
                                        aria-valuenow="<?= $newShowroomsPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewShowrooms"><?php echo !empty($newShowrooms) ? h($newShowrooms) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="showroomText"><?= __('New showrooms since this month') ?></span>
                                </p>
                            </div>

                        </div>
                    </div>
                </div>
                  */ ?>
            </div>

        <!-- Controls Section -->
        <div class="controls-section">
            <h3 class="controls-title"><?= __('Chart Controls') ?></h3>
            <div class="control-group">
                <div class="control-item">
                    <label class="control-label"><?= __('Duration') ?></label>
                    <select id="chartDuration" class="form-select" onchange="handleChartDurationChange(this)">
                        <option value="current_month" selected><?= __('Current Month') ?></option>
                        <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                        <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                        <option value="current_year"><?= __('Current Year') ?></option>
                        <option value="custom"><?= __('Custom Range') ?></option>
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label">&nbsp;</label>
                    <button class="btn-modern" onclick="refreshCharts()">
                        <i class="fas fa-sync-alt me-2"></i><?= __('Refresh Charts') ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Country-Based Order Trends Section -->
        <div class="controls-section">
            <h3 class="controls-title"><?= __('Country-Based Order Trends') ?></h3>
            <div class="control-group">
                <div class="control-item">
                    <label class="control-label"><?= __('Select Country') ?></label>
                    <select id="countrySelect" class="form-select" onchange="updateCountryCharts()">
                        <option value="all" selected><?= __('All Countries') ?></option>
                        <?php foreach ($countries as $country): ?>
                            <option value="<?= h($country->id) ?>"><?= h($country->name) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label"><?= __('Chart Type') ?></label>
                    <select id="chartType" class="form-select" onchange="updateCountryCharts()">
                        <option value="column" selected><?= __('Column Chart') ?></option>
                        <option value="bar"><?= __('Bar Chart') ?></option>
                        <option value="line"><?= __('Line Chart') ?></option>
                        <option value="area"><?= __('Area Chart') ?></option>
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label"><?= __('Duration') ?></label>
                    <select id="countryChartDuration" class="form-select" onchange="updateCountryCharts()">
                        <option value="current_month" selected><?= __('Current Month') ?></option>
                        <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                        <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                        <option value="current_year"><?= __('Current Year') ?></option>
                        <option value="custom"><?= __('Custom Range') ?></option>
                    </select>
                </div>
                <div class="control-item">
                    <label class="control-label">&nbsp;</label>
                    <button class="btn-modern" onclick="updateCountryCharts()">
                        <i class="fas fa-chart-bar me-2"></i><?= __('Update Chart') ?>
                    </button>
                </div>
            </div>

            <!-- Custom Date Range -->
            <div class="date-range-section d-none" id="datesappear">
                <form id="dateRangeForm">
                    <div class="date-range-grid">
                        <div class="control-item">
                            <label class="control-label"><?= __('From Date') ?></label>
                            <input type="date" id="from-date" name="from-date" class="form-control" />
                        </div>
                        <div class="control-item">
                            <label class="control-label"><?= __('To Date') ?></label>
                            <input type="date" id="to-date" name="to-date" class="form-control" />
                        </div>
                        <div class="control-item">
                            <button class="btn-modern" type="submit">
                                <i class="fas fa-search me-2"></i><?= __('Apply Range') ?>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Chart Section -->
        <div class="chart-section">
            <div class="chart-header">
                <h4 class="chart-title"><?= __('Order Trends (Current Month)') ?></h4>
            </div>
            <div class="chart-body">
                <div id="order_trends_chart"></div>
            </div>
        </div>

    </div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<!-- <script src="< ?= $this->Url->webroot('js/page/chart-apexcharts.js') ?>"></script> -->
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>

let chart1Instance;
let chart2Instance;
let chart3Instance;
let chart4Instance;

$(function () {
    console.log('Initializing charts...');
    chart1();
    chart2();
    chart3();
    chart4();
    console.log('Charts initialized');

    // Auto-refresh charts to show current month data by default
    setTimeout(function() {
        refreshCharts();
    }, 1000);
});

function chart1() {
    var options = {
        chart: {
            height: 350,
            type: "bar",
        },
        plotOptions: {
            bar: {
                horizontal: false,
                endingShape: "rounded",
                columnWidth: "20%",
            },
        },
        dataLabels: {
            enabled: false,
        },
        stroke: {
            show: true,
            width: 2,
            colors: ["transparent"],
        },
        series: [
            {
                name: "Orders",
                type: "column",
                data: <?= json_encode($orderCounts); ?>
            },
        ],
        colors: ["#0d839b"],
        xaxis: {
            categories: <?= json_encode($monthNames); ?>,
            title: {
                text: "Months",
            },
            labels: {
                style: {
                    colors: "#8e8da4",
                },
            },
        },
        yaxis: {
            title: {
                text: 'No. Of Orders',
            },
            labels: {
                style: {
                    color: "#8e8da4",
                },
                formatter: function (val) {
                    return Math.floor(val); // Ensure whole numbers only
                }
            },
            min: 0,
            forceNiceScale: true,
            decimalsInFloat: 0
        },
        fill: {
            opacity: 1,
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " Orders";
                },
            },
        },
        legend: {
            position: "top",
            horizontalAlign: "right",
            floating: true,
        },
    };

    chart1Instance = new ApexCharts(document.querySelector("#order_trends_chart"), options);
    chart1Instance.render();
}

function chart2() {
    var options = {
        chart: {
            height: 350,
            type: "line",
        },
        series: [
            {
                name: "Revenue",
                data: <?php echo json_encode($revenueData); ?>
            },
            {
                name: "Expenses",
                data: <?php echo json_encode($expensesData); ?>
            },
        ],
        colors: ["#0d839b", "#f77f00"],
        plotOptions: {
            bar: {
                horizontal: false,
                endingShape: "rounded",
                columnWidth: "50%",
            },
        },
        dataLabels: {
            enabled: false,
        },
        stroke: {
            show: true,
            width: 2,
            colors: ["transparent"],
        },
        title: {
            text: ".",
        },
        xaxis: {
            categories: <?php echo json_encode($revenueMonthNames); ?>,
            title: {
                text: "Data Period",
            },
            labels: {
                style: {
            colors: "#8e8da4",
            fontSize: "10px", // Adjust the font size for the month labels
        },
            },
        },
        yaxis: [
            // {
            //     title: {
            //         text: "Revenue (in thousands)",  // Revenue title
            //     },
            //     labels: {
            //         style: {
            //             color: "#000000",
            //         },
            //     },
            // },
            {
        title: {
            text: "Revenue (in thousands)", // Revenue title
            offsetX: 6, // Adjust horizontal position of the title
            offsetY: 0, // Adjust vertical position of the title
            style: {
                fontSize: "10px", // Title font size
            },
        },
        labels: {
            offsetX: 19, // Adjust horizontal position of the labels
            style: {
                fontSize: "12px", // Label font size
            },
            formatter: function (val) {
                return val.toFixed(1); // Show one decimal place for revenue
            }
        },
        min: 0,
        forceNiceScale: true
    },

            {
                opposite: true,
                title: {
                    text: "Expenses (in thousands)",  // Expenses title
                },
                labels: {
                    style: {
                        color: "#000000",
                    },
                    formatter: function (val) {
                        return val.toFixed(1); // Show one decimal place for expenses
                    }
                },
                min: 0,
                forceNiceScale: true
            },
        ],
        legend: {
            position: "top",
            horizontalAlign: "right",
            floating: true,
        },
    };

    chart2Instance = new ApexCharts(document.querySelector("#revenue_chart"), options);
    chart2Instance.render();
}






// Order Status Distribution Chart (Pie Chart)
function chart3() {
    var orderStatusData = <?= json_encode(array_values($orderStatuses)) ?>;
    var orderStatusLabels = <?= json_encode(array_keys($orderStatuses)) ?>;

    var options = {
        chart: {
            type: 'pie',
            height: 350
        },
        series: orderStatusData,
        labels: orderStatusLabels,
        colors: ['#ffc107', '#17a2b8', '#28a745', '#dc3545', '#6c757d'],
        legend: {
            position: 'bottom'
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " orders";
                }
            }
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };

    chart3Instance = new ApexCharts(document.querySelector("#order_status_chart"), options);
    chart3Instance.render();
}

// Online vs Showroom Orders Chart (Donut Chart)
function chart4() {
    var options = {
        chart: {
            type: 'donut',
            height: 350
        },
        series: [<?= $onlineOrders ?>, <?= $showroomOrders ?>],
        labels: ['Online Orders', 'Showroom Orders'],
        colors: ['#007bff', '#28a745'],
        legend: {
            position: 'bottom'
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '70%',
                    labels: {
                        show: true,
                        total: {
                            show: true,
                            label: 'Total Orders',
                            formatter: function () {
                                return <?= $totalOrders ?>;
                            }
                        }
                    }
                }
            }
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };

    chart4Instance = new ApexCharts(document.querySelector("#order_type_chart"), options);
    chart4Instance.render();
}
// Handle chart duration change
function handleChartDurationChange(selectElement) {
    if (selectElement.value === 'custom') {
        document.getElementById('datesappear').classList.remove('d-none');
    } else {
        document.getElementById('datesappear').classList.add('d-none');
        // Auto-refresh charts when duration changes (except custom)
        refreshCharts();
    }
}

// Refresh all charts with new duration
function refreshCharts() {
    const duration = document.getElementById('chartDuration').value;
    const fromDate = document.getElementById('from-date').value;
    const toDate = document.getElementById('to-date').value;

    // Validate custom date range
    if (duration === 'custom') {
        if (!fromDate || !toDate) {
            swal('<?= __('Error') ?>', '<?= __('Please select both from and to dates for custom range.') ?>', 'error');
            return;
        }

        if (new Date(fromDate) > new Date(toDate)) {
            swal('<?= __('Error') ?>', '<?= __('From date must be earlier than or equal to to date.') ?>', 'error');
            return;
        }
    }

    // Show loading state
    //showChartLoading();

    // Fetch new chart data
    $.ajax({
        url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'getChartData']) ?>',
        type: 'GET',
        data: {
            duration: duration,
            fromDate: fromDate,
            toDate: toDate
        },
        headers: {
            'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
        },
        success: function(response) {
            console.log('Chart data received:', response);
            updateAllCharts(response);
            hideChartLoading();

            // Update chart titles with date range
            updateChartTitles(response.dateRange.label);
        },
        error: function(xhr, status, error) {
            console.log('AJAX Error:', xhr, status, error);
            console.log('Response Text:', xhr.responseText);
            hideChartLoading();
            swal('<?= __('Error') ?>', '<?= __('Failed to fetch chart data. Please try again.') ?>' + ' Error: ' + error, 'error');
        }
    });
}

// Update all charts with new data
function updateAllCharts(data) {
    console.log('Updating charts with data:', data);

    // Update Order Trends Chart
    if (chart1Instance) {
        console.log('Updating chart1 with:', data.orderTrends);
        chart1Instance.updateOptions({
            xaxis: {
                categories: data.orderTrends.labels
            }
        });
        chart1Instance.updateSeries([{
            name: "Orders",
            data: data.orderTrends.data
        }]);
    } else {
        console.log('chart1Instance not found');
    }

    // Update Revenue Chart
    if (chart2Instance && data.revenue) {
        console.log('Updating chart2 with:', data.revenue);
        chart2Instance.updateOptions({
            xaxis: {
                categories: data.revenue.labels
            }
        });
        chart2Instance.updateSeries([{
            name: "Revenue",
            data: data.revenue.data
        }, {
            name: "Expenses",
            data: new Array(data.revenue.data.length).fill(0) // Placeholder for expenses
        }]);
    } else {
        console.log('chart2Instance not found or no revenue data');
    }

    // Update Order Status Chart
    if (chart3Instance && data.orderStatus) {
        console.log('Updating chart3 with:', data.orderStatus);
        chart3Instance.updateOptions({
            labels: data.orderStatus.labels
        });
        chart3Instance.updateSeries(data.orderStatus.data);
    } else {
        console.log('chart3Instance not found or no orderStatus data');
    }

    // Update Order Type Chart
    if (chart4Instance && data.orderType) {
        console.log('Updating chart4 with:', data.orderType);
        chart4Instance.updateOptions({
            labels: data.orderType.labels
        });
        chart4Instance.updateSeries(data.orderType.data);
    } else {
        console.log('chart4Instance not found or no orderType data');
    }
}

// Show loading state for charts
function showChartLoading() {
    $('.chart-body').each(function() {
        $(this).append('<div class="chart-loading text-center p-4"><i class="fa fa-spinner fa-spin fa-2x text-primary"></i><br><span class="text-muted">Loading charts...</span></div>');
    });
}

// Hide loading state for charts
function hideChartLoading() {
    $('.chart-loading').remove();
}

// Update chart titles with date range
function updateChartTitles(dateRangeLabel) {
    $('.card-header h4').each(function() {
        const originalTitle = $(this).text().split(' (')[0]; // Remove existing date range
        $(this).text(originalTitle + ' (' + dateRangeLabel + ')');
    });
}

    function handleChange(answer) {

        if (answer.value == 4) {
            document.getElementById('datesappear').classList.remove('d-none');
        } else
        {

            document.getElementById('datesappear').classList.add('d-none');

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'filterDashboardCard']); ?>',
                type: 'GET',
                data: {
                    dateRange: answer.value
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {

                    if(answer.value == '<?= __('current_month') ?>')
                    {
                        var userText = '<?= __('New users since this month') ?>';
                        var productText = '<?= __('New products since this month') ?>';
                        var showroomText = '<?= __('New showrooms since this month') ?>';
                    }
                    else if(answer.value == '<?= __('last_3_months') ?>')
                    {
                        var userText = '<?= __('Users since 3 months') ?>';
                        var productText = '<?= __('Products since 3 months') ?>';
                        var showroomText = '<?= __('Showrooms since 3 months') ?>';
                    }
                    else if(answer.value == '<?= __('last_6_months') ?>')
                    {
                        var userText = '<?= __('Users since 6 months') ?>';
                        var productText = '<?= __('Products since 6 months') ?>';
                        var showroomText = '<?= __('Showrooms since 6 months') ?>';
                    }
                    else if(answer.value == '<?= __('current_year') ?>')
                    {
                        var userText = '<?= __('Users in current year') ?>';
                        var productText = '<?= __('Products in current year') ?>';
                        var showroomText = '<?= __('Showrooms in current year') ?>';
                    }

                    $('#totalOrders').text(response.totalOrders + ' No.s');
                    $('#onlineOrders').text(response.onlineOrders + ' No.s');
                    $('#showroomOrders').text(response.showroomOrders + ' No.s');
                    $('#totalSalesAmount').text(response.totalSalesAmount);
                    $('#orderProgressBar').css('width', response.percentageOrders + '%').attr('data-width', response.percentageOrders + '%').attr('aria-valuenow', response.percentageOrders);

                    $('#totalUsers').text(response.totalUsers);
                    $('#totalNewUsers').text(response.newUsers);
                    $('#userProgressBar').css('width', response.newUsersPercentage + '%').attr('data-width', response.newUsersPercentage + '%').attr('aria-valuenow', response.newUsersPercentage);
                    $('#userText').text(userText);

                    $('#totalProducts').text(response.totalActiveProducts);
                    $('#totalNewProducts').text(response.newProducts);
                    $('#productProgressBar').css('width', response.newProductsPercentage + '%').attr('data-width', response.newProductsPercentage + '%').attr('aria-valuenow', response.newProductsPercentage);
                    $('#productText').text(productText);

                    $('#totalShowrooms').text(response.totalShowrooms);
                    $('#totalNewShowrooms').text(response.newShowrooms);
                    $('#showroomProgressBar').css('width', response.newShowroomsPercentage + '%').attr('data-width', response.newShowroomsPercentage + '%').attr('aria-valuenow', response.newShowroomsPercentage);
                    $('#showroomText').text(showroomText);
                },
                error: function() {
                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch data. Please try again.') ?>', 'error');
                }
            });
        }
    }

    $('#dateRangeForm').on('submit', function(event) {
        event.preventDefault(); // Prevent default form submission

        // Refresh charts with custom date range
        refreshCharts();
    });


</script>
<?php $this->end(); ?>