<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Datasource\EntityInterface;
use Cake\I18n\FrozenTime;

/**
 * Offers Model
 *
 * @property \App\Model\Table\OfferCategoriesTable&\Cake\ORM\Association\HasMany $OfferCategories
 * @property \App\Model\Table\OfferShowroomsTable&\Cake\ORM\Association\HasMany $OfferShowrooms
 * @property \App\Model\Table\OrdersTable&\Cake\ORM\Association\HasMany $Orders
 *
 * @method \App\Model\Entity\Offer newEmptyEntity()
 * @method \App\Model\Entity\Offer newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Offer> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Offer get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Offer findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Offer patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Offer> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Offer|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Offer saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Offer>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Offer>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Offer>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Offer> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Offer>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Offer>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Offer>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Offer> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class OffersTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('offers');
        $this->setDisplayField('offer_type');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->hasMany('OfferShowrooms', [
            'foreignKey' => 'offer_id',
        ]);

        $this->hasMany('OfferCategories', [
            'foreignKey' => 'offer_id',
        ]);

        $this->hasMany('OfferProducts', [
            'foreignKey' => 'offer_id',
        ]);

        $this->hasMany('OfferCustomerGroups', [
            'foreignKey' => 'offer_id',
        ]);



        $this->hasMany('Orders', [
            'foreignKey' => 'offer_id',
        ]);

        $this->belongsTo('Countries', [
            'foreignKey' => 'country_id',
            'joinType' => 'LEFT',
        ]);

        $this->belongsToMany('Showrooms', [
            'joinTable' => 'offer_showrooms',
            'foreignKey' => 'offer_id',
            'targetForeignKey' => 'showroom_id',
            'through' => 'OfferShowrooms',
        ]);

        $this->belongsToMany('Categories', [
            'joinTable' => 'offer_categories',
            'foreignKey' => 'offer_id',
            'targetForeignKey' => 'category_id',
            'through' => 'OfferCategories',
        ]);

        $this->belongsToMany('Products', [
            'joinTable' => 'offer_products',
            'foreignKey' => 'offer_id',
            'targetForeignKey' => 'product_id',
            'through' => 'OfferProducts',
        ]);

        $this->belongsToMany('CustomerGroups', [
            'joinTable' => 'offer_customer_groups',
            'foreignKey' => 'offer_id',
            'targetForeignKey' => 'customer_group_id',
            'through' => 'OfferCustomerGroups',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('offer_name')
            ->maxLength('offer_name', 255)
            ->requirePresence('offer_name', 'create')
            ->notEmptyString('offer_name');

        $validator
            ->scalar('offer_group')
            ->allowEmptyString('offer_group');

        $validator
            ->scalar('offer_type')
            ->requirePresence('offer_type', 'create')
            ->notEmptyString('offer_type');

        // $validator
        //     ->scalar('redeem_mode')
        //     ->requirePresence('redeem_mode', 'create')
        //     ->notEmptyString('redeem_mode');

        $validator
            ->scalar('offer_code')
            ->maxLength('offer_code', 10, 'Coupon code cannot exceed 10 characters.')
            ->requirePresence('offer_code', 'create')
            ->notEmptyString('offer_code')
            ->add('offer_code', 'validFormat', [
                'rule' => ['custom', '/^[a-zA-Z0-9]+$/'],
                'message' => 'Coupon code can only contain letters and numbers.'
            ])
            ->add('offer_code', 'unique', [
                'rule' => 'validateUnique',
                'provider' => 'table',
                'message' => 'This Coupon code is already in use. Please enter a unique Coupon code.'
            ]);

        $validator
            ->scalar('offer_description')
            ->allowEmptyString('offer_description');

        $validator
            ->scalar('web_image')
            ->maxLength('web_image', 255)
            ->allowEmptyFile('web_image');

        $validator
            ->scalar('mobile_image')
            ->maxLength('mobile_image', 255)
            ->allowEmptyFile('mobile_image');

        $validator
            ->decimal('discount')
            ->requirePresence('discount', 'create')
            ->notEmptyString('discount');

        $validator
            ->decimal('max_discount')
            ->allowEmptyString('max_discount');

        $validator
            ->decimal('max_amt_per_disc_value')
            ->allowEmptyString('max_amt_per_disc_value');

        $validator
            ->decimal('min_cart_value')
            ->allowEmptyString('min_cart_value');

        $validator
            ->notEmptyString('free_shipping');

        $validator
            ->scalar('terms_conditions')
            ->allowEmptyString('terms_conditions');

        $validator
            ->dateTime('offer_start_date')
            ->requirePresence('offer_start_date', 'create')
            ->notEmptyDateTime('offer_start_date');

        $validator
            ->dateTime('offer_end_date')
            ->requirePresence('offer_end_date', 'create')
            ->notEmptyDateTime('offer_end_date');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        $validator
            ->integer('country_id')
            ->allowEmptyString('country_id');

        return $validator;
    }

    //S API/website : $type : web, mobile
    public function homeOffers($type) {

        $order = ['Offers.created' => 'ASC'];
        if($type == 'mobile'){
            $select_col = ['id', 'offer_code', 'offer_description', 'mobile_image', 'discount',
            'offer_start_date', 'offer_end_date'];
            $condition  = [
                'Offers.status' => 'A',
                'OR' => [
                    ['Offers.offer_start_date <=' => date('Y-m-d H:i:s'), 'Offers.offer_end_date >=' => date('Y-m-d H:i:s')],
                    ['Offers.offer_start_date <=' => date('Y-m-d H:i:s'), 'Offers.offer_end_date IS' => null],
                    ['Offers.offer_start_date IS' => null, 'Offers.offer_end_date >=' => date('Y-m-d H:i:s')],
                    ['Offers.offer_start_date IS' => null, 'Offers.offer_end_date IS' => null]
                ]
            ];
        } else {
            $select_col = ['id', 'offer_code', 'offer_description', 'web_image', 'discount',
            'offer_start_date', 'offer_end_date'];
            $condition = [
                'Offers.status' => 'A',
                'OR' => [
                    ['Offers.offer_start_date <=' => date('Y-m-d H:i:s'), 'Offers.offer_end_date >=' => date('Y-m-d H:i:s')],
                    ['Offers.offer_start_date <=' => date('Y-m-d H:i:s'), 'Offers.offer_end_date IS' => null],
                    ['Offers.offer_start_date IS' => null, 'Offers.offer_end_date >=' => date('Y-m-d H:i:s')],
                    ['Offers.offer_start_date IS' => null, 'Offers.offer_end_date IS' => null]
                ]
            ];
        }

        $offers = $this->find()
            ->select($select_col)
            ->where($condition)
            ->order($order)
            ->disableHydration()
            ->toArray();

        return $offers;
    }

    public function isOfferAssociatedWithOrders($offerId)
    {
        return $this->Orders->exists(['offer_id' => $offerId]);
    }

    public function delete(EntityInterface $offer, array $options = []): bool{
        $offer->status = 'D';
        if ($this->save($offer)) {
            return true;
        }
        return false;
    }


    public function getTopDeals($limit = 5)
    {

        if($limit == 0 || $limit == null){
            $limit = 5;
        }

        $today = FrozenTime::now();

        $query = $this->find()
            ->select([
                'id',
                'offer_name',
                'offer_code',
                'discount',
                'offer_description',
                'offer_start_date',
                'offer_end_date',
                'status',
                'mobile_image',
                'web_image'
            ])
            ->where([
                'status' => 'A',
                'offer_start_date <=' => $today,
                'OR' => [
                    'offer_end_date >=' => $today,
                    'offer_end_date IS' => null
                ]
            ])
            ->order(['discount' => 'DESC'])
            ->limit($limit);

        $query->toArray();
        return $query;
    }

    //S apply coupon
    public function applyCoupon ($couponCode, $subTotal, $orderDate){

        // Convert orderDate to proper DateTime object for comparison
        $orderDateTime = new \DateTime($orderDate);

        $offer = $this->find()
        ->where([
            'offer_code' => $couponCode,
            'status' => 'A',
            // 'redeem_mode IN' => ['Store', 'Both', 'Online'],
            'min_cart_value <=' => $subTotal,
            'offer_start_date <=' => $orderDateTime,
            'OR' => [
                'offer_end_date IS' => null,
                'offer_end_date >=' => $orderDateTime
            ]
        ])
        // ->contain(['OfferShowrooms'])
        ->first();

        if($offer){
            return $offer;
        } else {
            return false;
        }
    }

}
