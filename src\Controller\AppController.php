<?php

declare(strict_types=1);

/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link      https://cakephp.org CakePHP(tm) Project
 * @since     0.2.9
 * @license   https://opensource.org/licenses/mit-license.php MIT License
 */

namespace App\Controller;

use Cake\Controller\Controller;
use Cake\Routing\Router;
use Cake\Http\Client;
use Cake\Utility\Inflector;
use Cake\Utility\Text;
use Cake\ORM\TableRegistry;
use Cake\Datasource\Exception\RecordNotFoundException;
use Cake\Core\Configure;
use Cake\I18n\I18n;


/**
 * Application Controller
 *
 * Add your application-wide methods in the class below, your controllers
 * will inherit them.
 *
 * @link https://book.cakephp.org/4/en/controllers.html#the-app-controller
 */
class AppController extends Controller
{
    protected $Modules;
    protected $Permissions;
    protected $GlobalSearch;
    public $storeBased;
    public float $deliveryCharge;
    public function initialize(): void
    {
        parent::initialize();

        $this->loadComponent('Flash');
        $this->Modules = $this->fetchTable('Modules');
        $this->Permissions = $this->fetchTable('Permissions');
        $this->GlobalSearch = $this->fetchTable('GlobalSearch');

        $this->loadComponent('Authentication.Authentication');

        // $this->loadThemeColors();

        $permissionsArray = [];
        $accessibleModules = [];

        if ($this->Authentication->getIdentity()) {
            $identity = $this->Authentication->getIdentity();
            $userId = $identity->id ?? null;

            if ($userId) {
                $usersTable = $this->getTableLocator()->get('Users');
                $user = $usersTable->find()
                    ->where(['Users.id' => $userId, 'Users.status' => 'A'])
                    ->contain('Roles')
                    ->first();

                if ($user && isset($user['role_id'])) {
                    $modules = $this->Modules->find('all')->toArray();

                    foreach ($modules as $module) {
                        $permissionsArray[$module->name] = $this->getModulePermissions($user['role_id'], $module->id);
                    }
                    foreach ($modules as $module) {
                        $permissionsArray[$module->name] = $this->getModulePermissions($user['role_id'], $module->id);
                    }

                    foreach ($permissionsArray as $moduleName => $permissions) {
                        if ($permissions['can_view'] == 1) {
                            $accessibleModules[] = $moduleName;
                        }
                    }
                    $this->set(compact('user'));
                }
            }
        }

        $canView = $this->hasPermission($this->request->getParam('controller'), 'index');
        $canAdd = $this->hasPermission($this->request->getParam('controller'), 'add');
        $canEdit = $this->hasPermission($this->request->getParam('controller'), 'edit');
        $canDelete = $this->hasPermission($this->request->getParam('controller'), 'delete');
        $canApprove = $this->hasPermission($this->request->getParam('controller'), 'approve');
        $storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
        if ($this->Authentication->getIdentity()) {
            $this->checkPermission();
        }

        $siteSettingsTable = TableRegistry::getTableLocator()->get('SiteSettings');
        $settings = $siteSettingsTable->find()->first();

        $paginationCount = $settings->pagination_count ?? 10;

        // echo "<pre>"; print_r($accessibleModules); die;
        $this->set([
            'deleteConfirmationMessage' => Configure::read('Constants.DELETE_CONFIRMATION_MESSAGE'),
            'deleteWarningMessage' => Configure::read('Constants.DELETE_WARNING_MESSAGE'),
            'deleteFailMessage' => Configure::read('Constants.DELETE_FAIL_MESSAGE')
        ]);

        $this->set(compact('paginationCount', 'canView', 'canAdd', 'canEdit', 'canDelete','canApprove','storeBased', 'permissionsArray', 'accessibleModules'));
    }


    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);



            $siteSettingsTable = TableRegistry::getTableLocator()->get('SiteSettings');
            $siteSettings = $siteSettingsTable->find()->first();
             $this->deliveryCharge = $siteSettings->delivery_charge ?? 0;
            $this->set('siteSettings', $siteSettings);

        $result = $this->Authentication->getResult();

        // Get current controller and action
        $controller = $this->request->getParam('controller');
        $action = $this->request->getParam('action');

        // Define guest-allowed actions for different controllers
        $guestAllowedActions = [
            'otp', 'socialCallback', 'socialLogin', 'signup', 'login', 'logout',
            'forgotPassword', 'resetPassword', 'verifyEmail'
        ];

        // Add cart-specific actions if this is the Cart controller
        if ($controller === 'Cart') {
            $guestAllowedActions = array_merge($guestAllowedActions, [
                'addToCart', 'cart', 'updateQuantity', 'remove'
            ]);
        }

        // Add home/website actions for public browsing
        if (in_array($controller, ['Home', 'Website'])) {
            $guestAllowedActions = array_merge($guestAllowedActions, [
                'index', 'productList', 'list', 'ajaxLoadMoreProducts'
            ]);
        }

        // Add customer controller actions for login/register
        if ($controller === 'Customer') {
            $guestAllowedActions = array_merge($guestAllowedActions, [
                'login', 'register', 'index'
            ]);
        }

        // Add products controller for product viewing
        if ($controller === 'Products') {
            $guestAllowedActions = array_merge($guestAllowedActions, [
                'view', 'index'
            ]);
        }

        $this->Authentication->addUnauthenticatedActions($guestAllowedActions);
        $loginAction = ['controller' => 'Users', 'action' => 'login'];

        // if (!$result->isValid() && $this->getRequest()->getParam('action') !== 'login') {
        //     return $this->redirect(Router::url($loginAction));
        // }

        // $lang = Configure::read('Settings.LANGUAGE') ?? 'en';
        // $session = $this->request->getSession();
        // $sessionLang = $session->read("siteSettings.language");

        // if (!empty($sessionLang)) {
        //     $lang = ($sessionLang === "Arbic") ? 'ar' : $sessionLang;
        // }

        // I18n::setLocale($lang);
    }

    private function getModulePermissions($roleId, $moduleId)
    {
        // Get the permission settings for the user based on their role and module
        $permission = $this->Permissions->find()
            ->where(['role_id' => $roleId, 'module_id' => $moduleId])
            ->first();

        // Return an array of the permissions for the module (if any)
        if ($permission) {
            return [
                'can_view' => $permission->can_view,
                'can_add' => $permission->can_create,
                'can_edit' => $permission->can_edit,
                'can_delete' => $permission->can_delete,
                'can_approve' => $permission->can_approve,
                'store_based' => $permission->store_based,
            ];
        }

        // If no permission exists, return false for all actions
        return [
            'can_view' => false,
            'can_add' => false,
            'can_edit' => false,
            'can_delete' => false,
            'can_approve' => false,
            'store_based' => false
        ];
    }

    private function checkPermission()
    {

        $skipControllers = ['Users', 'Dashboards'];
        $skipActions = ['login', 'register', 'forgotPassword', 'index', 'noaccess'];

        $controller = $this->request->getParam('controller');
        $action = $this->request->getParam('action');

        $actionsToCheck = ['index', 'add', 'view', 'edit', 'delete'];

        if (in_array($controller, $skipControllers) && in_array($action, $skipActions)) {
            return;
        }

        if (in_array($action, $actionsToCheck)) {
            if (!$this->hasPermission($controller, $action)) {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'noaccess']);
            }
        }

        if($controller === 'Dashboards' && $action === 'adminDashboard') {
            if (!$this->hasPermission('Dashboards', 'index')) {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'noaccess']);
            }
        }

        if($controller === 'Dashboards' && $action === 'showroomManagerDashboard') {
            if (!$this->hasPermission('Dashboards', 'index')) {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'noaccess']);
            }
        }

        if($controller === 'Dashboards' && $action === 'supervisorManagerDashboard') {
            if (!$this->hasPermission('Dashboards', 'index')) {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'noaccess']);
            }
        }
    }

    public function hasPermission($controllerName, $action)
    {

        $user = $this->Authentication->getIdentity();

        try {
            $module = $this->Modules->findByName($controllerName)->firstOrFail();
        } catch (RecordNotFoundException $e) {
            return false;
        }

        if (!empty($user) && !empty($user->id)) {

            $roleId = $user->role_id;
            if ($roleId != null) {
                $permission = $this->Permissions->find()
                    ->where(['role_id' => $roleId, 'module_id' => $module->id])
                    ->first();
                if (!$permission) {
                    return false;
                }

                $actionMap = [
                    'index' => 'can_view',
                    'view' => 'can_view',
                    'add' => 'can_create',
                    'edit' => 'can_edit',
                    'delete' => 'can_delete',
                    'approve' => 'can_approve',
                    'store' => 'store_based'
                ];

                $permissionColumn = $actionMap[$action] ?? null;

                if ($permissionColumn === null) {
                    return false;
                }
                return $permission->$permissionColumn;
            }
        }
    }

    // protected function loadThemeColors()
    // {
    //     $settingsTable = $this->getTableLocator()->get('Settings');
    //     $themeColors = $settingsTable->getThemeColors();

    //     $scssContent = <<<SCSS
    //     \$color-primary: {$themeColors['theme_primary_color']};
    //     \$color-white: {$themeColors['theme_primary_color']};
    //     \$color-secondary: {$themeColors['theme_secondary_color']};
    //     \$color-tertiary: {$themeColors['theme_tertiary_color']};
    //     SCSS;

    //     // Write combined content back to _white.scss
    //     file_put_contents('scss/skins/_dynamic.scss', $scssContent);

    // }

    function stringToSlug($str)
    {
        $slug = Text::slug($str);
        $slug = strtolower($slug);
        return $slug;
    }

    public function generateUniqueUrlKey($title, $modelName, $excludeId = null)
    {
        $urlKey = $this->stringToSlug($title);

        $model = $this->getTableLocator()->get($modelName);

        $query = $model->find()
            ->where(['url_key' => $urlKey]);

        if ($excludeId !== null) {
            $query->andWhere(['id !=' => $excludeId]);
        }

        $existingCount = $query->count();

        $originalUrlKey = $urlKey;
        $i = 1;

        while ($existingCount > 0) {
            $urlKey = $originalUrlKey . '-' . $i;

            $query = $model->find()->where(['url_key' => $urlKey]);

            if ($excludeId !== null) {
                $query->andWhere(['id !=' => $excludeId]);
            }

            $existingCount = $query->count();

            $i++;
        }

        return $urlKey;
    }

    public function globalSearch()
    {
        $entered_text = $this->request->getData('q');

        $modules = $this->GlobalSearch->find()
            ->select(['module_name', 'search_fields', 'display_field','display_category'])
            ->where(['status' => 'A'])
            ->toArray();

        $results = [];
        $tableLocator = $this->getTableLocator();

        foreach ($modules as $module) {
            $table = $module->module_name;

            $model = $tableLocator->get($table);
            $searchFields = explode(',', $module->search_fields);
            $conditions = [];

            foreach ($searchFields as $field) {
                $field = trim($field);
                if (!empty($field)) {
                    $conditions[] = ['LOWER(' . trim($field) . ') LIKE' => "%" . strtolower(trim($entered_text)) . "%"];
                }
            }

            if (!empty($conditions)) {
                $query = $model->find()
                    ->select(['id',$module->display_field])
                    ->where(['OR' => $conditions]);

                $records = $query->toArray();

                foreach ($records as $record) {
                    $results[] = [
                        'table' => $table,
                        'data' => $record,
                        'value' => $record[$module->display_field],
                        'display' => $module->display_category,
                        'link' => Router::url(['controller' => $table, 'action' => 'view', $record->id], true) // 'true' generates full URLs
                    ];
                }
            }
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($results));
    }
}
