<!-- Hero Section -->
<?php if (isset($widgets['banner']) && is_array($widgets['banner']) && !empty($widgets['banner'][0]['widget']['summary'])): ?>
      <?= $widgets['banner'][0]['widget']['summary'] ?>
<?php endif; ?>

<?php if (isset($widgets['better_life']) && is_array($widgets['better_life']) && !empty($widgets['better_life'][0]['widget']['title'])): ?>  
    <div class="text-center desktop">
        <h1 class="mb-5 better-air"><?= $widgets['better_life'][0]['widget']['title'] ?></h1>
    </div>
<?php endif; ?>


<?php if (isset($widgets['better_life']) && is_array($widgets['better_life']) && !empty($widgets['better_life'][0]['widget']['summary'])): ?>
      <?= $widgets['better_life'][0]['widget']['summary'] ?>
<?php endif; ?>

<?php if (isset($widgets['featured']) && is_array($widgets['featured'])): ?>
    <!-- Air Quality Section -->
    <section class="py-md-5 products">
        <div class="container">
            <h2 class="section-title text-left mt-lg-5 pt-lg-5 air-quality">
                <?= $widgets['featured'][0]['widget']['title'] ?>
            </h2>
            <p class="text-left content mb-md-5"><?= $widgets['featured'][0]['widget']['summary'] ?></p>

            <ul class="nav nav-tabs" id="products-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-category-id="all" type="button"
                        role="tab" aria-controls="all-products" aria-selected="true">All</button>
                </li>
                <?php foreach ($widgets['featured'][0]['category'] as $category): ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="category-<?= $category['category_id'] ?>-tab"
                            data-category-id="<?= $category['category_id'] ?>"
                            data-category-url="<?= h($category['url_key']) ?>"
                            type="button" role="tab"
                            aria-controls="category-<?= $category['category_id'] ?>-products"
                            aria-selected="false"><?= h($category['category_name']) ?></button>
                    </li>
                <?php endforeach; ?>
            </ul>
            <div class="tab-content mt-5">
                <div class="tab-pane active" id="all-products" role="tabpanel" aria-labelledby="all-tab">
                    <div class="row mb-4 desktop" id="featured-products-container">

                        <?php foreach ($widgets['featured'][0]['products'] as $product): ?>
                            <?php
                                // Get category IDs for this product
                                $productCategoryIds = [];
                                if (isset($product['product_categories'])) {
                                    foreach ($product['product_categories'] as $productCategory) {
                                        if (isset($productCategory['category'])) {
                                            $productCategoryIds[] = $productCategory['category']['id'];
                                        }
                                    }
                                }
                                $categoryIdsString = implode(',', $productCategoryIds);
                            ?>
                            <!-- Product -->
                            <div class="col-md-6 col-lg-4 mb-4 product-item" data-category-ids="<?= $categoryIdsString ?>">
                                <a class='text-decoration-none'
                                    href="<?= $this->Url->build('/product/' . h($product['url_key'])) ?>">
                                    <div class="product-card">
                                        <div class="position-relative substract">
                                            <img src="<?= $product['product_image'] ?>" alt="Air Conditioner"
                                                class="img-fluid w-100 ">
                                            <div class=" position-absolute">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div><?php if (isset($product['discount']) && $product['discount'] > 0): ?>
                                                                <div class="badge"><?= h($product['discount']) ?>% <?= __('Off') ?></div>
                                                    <?php endif; ?></div>
                                                    <div class="add-cart add-to-cart-btn" data-product-id="<?= $product['id'] ?>">
                                                        <span class="bor-top-rig"></span>
                                                        <span class="bor-bot-left"></span>
                                                        <div class="energy-rating">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38"
                                                                viewBox="0 0 38 38" fill="none">
                                                                <path
                                                                    d="M11.8 26.2L27.4962 24.8919C32.4075 24.4827 33.51 23.41 34.0543 18.5119L35.2 8.19995"
                                                                    stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                <path d="M8.19995 8.19995H9.09995M37 8.19995H32.5"
                                                                    stroke="white" stroke-width="2" stroke-linecap="round" />
                                                                <path d="M14.5 8.1999H27.1M20.8 14.4999V1.8999" stroke="white"
                                                                    stroke-width="2" stroke-linecap="round" />
                                                                <circle cx="8.19985" cy="33.4" r="3.6" stroke="white"
                                                                    stroke-width="2" />
                                                                <circle cx="28.0001" cy="33.4" r="3.6" stroke="white"
                                                                    stroke-width="2" />
                                                                <path d="M11.8001 33.3999L24.4001 33.3999" stroke="white"
                                                                    stroke-width="2" stroke-linecap="round" />
                                                                <path
                                                                    d="M1 1H2.7388C4.43923 1 5.92145 2.12427 6.33387 3.72687L11.6893 24.5377C11.96 25.5894 11.7284 26.7035 11.0588 27.5708L9.33784 29.8"
                                                                    stroke="white" stroke-width="2" stroke-linecap="round" />
                                                            </svg>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="pt-3 px-3">
                                            <h5 class="text-truncate-2-line"><?= $product['name'] ?></h5>
                                            <?php
                                                $rating = floatval($product['avg_rating']);
                                                $fullStars = floor($rating);
                                                $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                                $emptyStars = 5 - $fullStars - $halfStar;
                                            ?>
                                            <div class="rating mb-2">
                                                <?php for ($i = 0; $i < $fullStars; $i++): ?>
                                                    <i class="fas fa-star"></i>
                                                <?php endfor; ?>
                                                <?php if ($halfStar): ?>
                                                    <i class="fas fa-star-half-alt"></i>
                                                <?php endif; ?>
                                                <?php for ($i = 0; $i < $emptyStars; $i++): ?>
                                                    <i class="far fa-star"></i>
                                                <?php endfor; ?>
                                                <span class="text-muted ms-2">(<?= number_format($rating, 1) ?>)</span>
                                            </div>
                                           
                                            
                                            <div class="price">
                                                <div>
                                                    <?= $this->Price->setPriceFormat($product['promotion_price']) ?> <span>incl. VAT</span>
                                                </div>
                                                <?php if ($product['sales_price'] > $product['promotion_price']): ?>
                                                <div class="text-muted text-decoration-line-through small">
                                                    <?= $this->Price->setPriceFormat($product['sales_price']) ?>
                                                </div>
                                                <?php endif; ?> 
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="row mb-4 mobile">
                        <div class="large-12 columns relative p-0">
                            <div class="owl-carousel owl-theme p-0 owl-carousel-product" id="featured-products-mobile-container">
                                <?php foreach ($widgets['featured'][0]['products'] as $product): ?>
                                    <?php
                                        $productCategoryIds = [];
                                        if (isset($product['product_categories'])) {
                                            foreach ($product['product_categories'] as $productCategory) {
                                                if (isset($productCategory['category'])) {
                                                    $productCategoryIds[] = $productCategory['category']['id'];
                                                }
                                            }
                                        }
                                        $categoryIdsString = implode(',', $productCategoryIds);
                                    ?>
                                    <div class="item product-item-mobile" data-category-ids="<?= $categoryIdsString ?>">
                                    
                                       
                                            <div class="product-card clickable-product-card" data-product-id="<?= $product['url_key'] ?>">
                                                <div class="position-relative substract">
                                                      <img src="<?= $product['product_image'] ?>" alt="Air Conditioner"
                                                class="img-fluid w-100 ">
                                                    <div class=" position-absolute">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div><?php if (isset($product['discount']) && $product['discount'] > 0): ?>
                                                                <div class="badge"><?= h($product['discount']) ?>% <?= __('Off') ?></div>
                                                            <?php endif; ?></div>
                                                            <div class="add-cart add-to-cart-btn" data-product-id="<?= $product['url_key'] ?>">
                                                                <span class="bor-top-rig"></span>
                                                                <span class="bor-bot-left"></span>
                                                                <div class="energy-rating">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38"
                                                                        viewBox="0 0 38 38" fill="none">
                                                                        <path
                                                                            d="M11.8 26.2L27.4962 24.8919C32.4075 24.4827 33.51 23.41 34.0543 18.5119L35.2 8.19995"
                                                                            stroke="white" stroke-width="2"
                                                                            stroke-linecap="round" />
                                                                        <path d="M8.19995 8.19995H9.09995M37 8.19995H32.5"
                                                                            stroke="white" stroke-width="2"
                                                                            stroke-linecap="round" />
                                                                        <path d="M14.5 8.1999H27.1M20.8 14.4999V1.8999"
                                                                            stroke="white" stroke-width="2"
                                                                            stroke-linecap="round" />
                                                                        <circle cx="8.19985" cy="33.4" r="3.6" stroke="white"
                                                                            stroke-width="2" />
                                                                        <circle cx="28.0001" cy="33.4" r="3.6" stroke="white"
                                                                            stroke-width="2" />
                                                                        <path d="M11.8001 33.3999L24.4001 33.3999" stroke="white"
                                                                            stroke-width="2" stroke-linecap="round" />
                                                                        <path
                                                                            d="M1 1H2.7388C4.43923 1 5.92145 2.12427 6.33387 3.72687L11.6893 24.5377C11.96 25.5894 11.7284 26.7035 11.0588 27.5708L9.33784 29.8"
                                                                            stroke="white" stroke-width="2"
                                                                            stroke-linecap="round" />
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                                <div class="pt-3 px-3">
                                                        <h5 class="text-truncate-2-line"><?= $product['name'] ?></h5>
                                                    <div class="rating mb-2">
                                                        <i class="fas fa-star"></i>
                                                        <i class="fas fa-star"></i>
                                                        <i class="fas fa-star"></i>
                                                        <i class="fas fa-star"></i>
                                                        <i class="fas fa-star-half-alt"></i>
                                                        <span class="text-muted ms-2">(4.5)</span>
                                                    </div>
                                                    <!-- <div class="price ">3,400 QAR <span>incl. VAT</span></div> -->
                                                    <div class="price">
                                                        <div>
                                                            <?= $this->Price->setPriceFormat($product['promotion_price']) ?> <span>incl. VAT</span>
                                                        </div>
                                                        <?php if ($product['sales_price'] > $product['promotion_price']): ?>
                                                        <div class="text-muted text-decoration-line-through small">
                                                            <?= $this->Price->setPriceFormat($product['sales_price']) ?>
                                                        </div>
                                                        <?php endif; ?> 
                                                    </div>
                                                </div>
                                            </div>
                                       
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>


                    <!-- <div class="row mb-4 mobile">
                        <div class="owl-carousel owl-theme p-0 owl-carousel-product" id="featured-products-mobile-container">
                            <?php foreach ($widgets['featured'][0]['products'] as $product): ?>
                                <?php
                                    $productCategoryIds = [];
                                    if (isset($product['product_categories'])) {
                                        foreach ($product['product_categories'] as $productCategory) {
                                            if (isset($productCategory['category'])) {
                                                $productCategoryIds[] = $productCategory['category']['id'];
                                            }
                                        }
                                    }
                                    $categoryIdsString = implode(',', $productCategoryIds);
                                ?>
                                <div class="item product-item-mobile" data-category-ids="<?= $categoryIdsString ?>">
                                 
                                    <a class='text-decoration-none'
                                        href="<?= $this->Url->build('/product/' . h($product['url_key'])) ?>">
                                        <div class="product-card">
                                            <div class="position-relative substract">
                                                <img src="<?= $product['product_image'] ?>" alt="Air Conditioner"
                                                    class="img-fluid w-100 ">
                                                <div class=" position-absolute">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div class="badge">10% Offer</div>
                                                        <div class="add-cart">
                                                            <span class="bor-top-rig"></span>
                                                            <span class="bor-bot-left"></span>
                                                            <div class="energy-rating">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38"
                                                                    viewBox="0 0 38 38" fill="none">
                                                                    <path
                                                                        d="M11.8 26.2L27.4962 24.8919C32.4075 24.4827 33.51 23.41 34.0543 18.5119L35.2 8.19995"
                                                                        stroke="white" stroke-width="2"
                                                                        stroke-linecap="round" />
                                                                    <path d="M8.19995 8.19995H9.09995M37 8.19995H32.5"
                                                                        stroke="white" stroke-width="2"
                                                                        stroke-linecap="round" />
                                                                    <path d="M14.5 8.1999H27.1M20.8 14.4999V1.8999"
                                                                        stroke="white" stroke-width="2"
                                                                        stroke-linecap="round" />
                                                                    <circle cx="8.19985" cy="33.4" r="3.6" stroke="white"
                                                                        stroke-width="2" />
                                                                    <circle cx="28.0001" cy="33.4" r="3.6" stroke="white"
                                                                        stroke-width="2" />
                                                                    <path d="M11.8001 33.3999L24.4001 33.3999" stroke="white"
                                                                        stroke-width="2" stroke-linecap="round" />
                                                                    <path
                                                                        d="M1 1H2.7388C4.43923 1 5.92145 2.12427 6.33387 3.72687L11.6893 24.5377C11.96 25.5894 11.7284 26.7035 11.0588 27.5708L9.33784 29.8"
                                                                        stroke="white" stroke-width="2"
                                                                        stroke-linecap="round" />
                                                                </svg>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="pt-3 px-3">
                                                <h5><?= $product['product_name'] ?></h5>
                                                <div class="rating mb-2">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star-half-alt"></i>
                                                    <span class="text-muted ms-2">(4.5)</span>
                                                </div>
                                                <div class="price ">3,400 QAR <span>incl. VAT</span></div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div> -->

                </div>

            </div>

            <!-- View More Button -->
            <div class="prdt-viewmore  mt-4">
                <button id="view-more-btn" onclick="redirectToProductList();" class="btn btn-outline-primary px-4">
                    View More
                </button>
            </div>

            <script>
                // Featured products filtering functionality
                let currentCategoryId = 'all';

                // Tab click handlers
                document.querySelectorAll('#products-tab .nav-link').forEach(function(tab) {
                    tab.addEventListener('click', function(e) {
                        e.preventDefault();

                        // Remove active class from all tabs
                        document.querySelectorAll('#products-tab .nav-link').forEach(function(t) {
                            t.classList.remove('active');
                            t.setAttribute('aria-selected', 'false');
                        });

                        // Add active class to clicked tab
                        this.classList.add('active');
                        this.setAttribute('aria-selected', 'true');

                        // Get category ID
                        currentCategoryId = this.getAttribute('data-category-id');

                        // Filter products
                        filterProducts(currentCategoryId);
                    });
                });

                function filterProducts(categoryId) {
                    // Desktop products
                    const desktopProducts = document.querySelectorAll('#featured-products-container .product-item');

                    desktopProducts.forEach(function(product) {
                        const productCategories = product.getAttribute('data-category-ids').split(',');

                        if (categoryId === 'all' || productCategories.includes(categoryId)) {
                            product.style.display = 'block';
                        } else {
                            product.style.display = 'none';
                        }
                    });

                    // Mobile products - rebuild carousel
                    const mobileContainer = document.querySelector('#featured-products-mobile-container');
                    const mobileProducts = document.querySelectorAll('.product-item-mobile');

                    // Destroy existing carousel if it exists
                    if (typeof $ !== 'undefined' && mobileContainer) {
                        $(mobileContainer).trigger('destroy.owl.carousel').removeClass('owl-carousel owl-loaded');
                        $(mobileContainer).find('.owl-stage-outer').children().unwrap();
                    }

                    // Filter mobile products
                    mobileProducts.forEach(function(product) {
                        const productCategories = product.getAttribute('data-category-ids').split(',');

                        if (categoryId === 'all' || productCategories.includes(categoryId)) {
                            product.style.display = 'block';
                        } else {
                            product.style.display = 'none';
                        }
                    });

                    // Reinitialize carousel for mobile
                    if (typeof $ !== 'undefined' && mobileContainer) {
                        $(mobileContainer).addClass('owl-carousel owl-theme');
                        $(mobileContainer).owlCarousel({
                            loop: true,
                            margin: 10,
                            nav: true,
                            responsive: {
                                0: { items: 1 },
                                600: { items: 2 },
                                1000: { items: 3 }
                            }
                        });
                    }
                }

                function redirectToProductList() {
                    if (currentCategoryId === 'all') {
                        window.location.href = '/product-list';
                    } else {
                        // Find the category URL key
                        const categoryTab = document.querySelector(`[data-category-id="${currentCategoryId}"]`);
                        if (categoryTab && categoryTab.getAttribute('data-category-url')) {
                            const categoryUrl = categoryTab.getAttribute('data-category-url');
                            window.location.href = `/product-list/${categoryUrl}`;
                        } else {
                            // Fallback to general product list
                            window.location.href = '/product-list';
                        }
                    }
                }

                // Initialize with all products visible
                document.addEventListener('DOMContentLoaded', function() {
                    filterProducts('all');
                });
            </script>
        </div>
    </section>

<?php endif; ?>

<!-- Latest Offers Section -->
<?php if (isset($widgets['latest_offers']) && is_array($widgets['latest_offers'])): ?>
<section class="latest-offers py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-5">
            <h2 class="section-title mb-0"><?= $widgets['latest_offers'][0]['widget']['title'] ?></h2>
        </div>
        <div class="row">
            <div class="large-12 columns relative p-0">
                <div class="owl-carousel owl-theme " id="general-carousel">
                    <?php foreach ($widgets['latest_offers'][0]['products'] as $product): ?>
                        
                        <div class="item">
                            <div class="product-card clickable-product-card" data-product-id="<?= $product->url_key ?>">
                                <div class="position-relative substract">
                                    <img src="<?= $product['product_image'] ?>" alt="Air Conditioner"
                                        class="img-fluid w-100 ">
                                    <div class=" position-absolute">
                                        <div class="d-flex justify-content-between align-items-center">
                                           <div> <?php if (isset($product['discount']) && $product['discount'] > 0): ?>
                                                                <div class="badge"><?= h($product['discount']) ?>% <?= __('Off') ?></div>
                                            <?php endif; ?></div>
                                            <div class="add-cart add-to-cart-btn" data-product-id="<?= $product['url_key'] ?>">
                                                <span class="bor-top-rig"></span>
                                                <span class="bor-bot-left"></span>
                                                <div class="energy-rating">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38"
                                                        viewBox="0 0 38 38" fill="none">
                                                        <path
                                                            d="M11.8 26.2L27.4962 24.8919C32.4075 24.4827 33.51 23.41 34.0543 18.5119L35.2 8.19995"
                                                            stroke="white" stroke-width="2" stroke-linecap="round" />
                                                        <path d="M8.19995 8.19995H9.09995M37 8.19995H32.5" stroke="white"
                                                            stroke-width="2" stroke-linecap="round" />
                                                        <path d="M14.5 8.1999H27.1M20.8 14.4999V1.8999" stroke="white"
                                                            stroke-width="2" stroke-linecap="round" />
                                                        <circle cx="8.19985" cy="33.4" r="3.6" stroke="white"
                                                            stroke-width="2" />
                                                        <circle cx="28.0001" cy="33.4" r="3.6" stroke="white"
                                                            stroke-width="2" />
                                                        <path d="M11.8001 33.3999L24.4001 33.3999" stroke="white"
                                                            stroke-width="2" stroke-linecap="round" />
                                                        <path
                                                            d="M1 1H2.7388C4.43923 1 5.92145 2.12427 6.33387 3.72687L11.6893 24.5377C11.96 25.5894 11.7284 26.7035 11.0588 27.5708L9.33784 29.8"
                                                            stroke="white" stroke-width="2" stroke-linecap="round" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="pt-3 px-3">
                                    <h5 class="text-truncate-2-line"><?= $product['name'] ?></h5>
                                      <?php
                                            $rating = floatval($product['avg_rating']);
                                            $fullStars = floor($rating);
                                            $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                            $emptyStars = 5 - $fullStars - $halfStar;
                                        ?>
                                        <div class="rating mb-2">
                                            <?php for ($i = 0; $i < $fullStars; $i++): ?>
                                                <i class="fas fa-star"></i>
                                            <?php endfor; ?>
                                            <?php if ($halfStar): ?>
                                                <i class="fas fa-star-half-alt"></i>
                                            <?php endif; ?>
                                            <?php for ($i = 0; $i < $emptyStars; $i++): ?>
                                                <i class="far fa-star"></i>
                                            <?php endfor; ?>
                                            <span class="text-muted ms-2">(<?= number_format($rating, 1) ?>)</span>
                                        </div>
                                            <div class="price">
                                                <div>
                                                    <?= $this->Price->setPriceFormat($product['promotion_price']) ?> <span>incl. VAT</span>
                                                </div>
                                                <?php if ($product['sales_price'] > $product['promotion_price']): ?>
                                                    <div class="text-muted text-decoration-line-through small mb-1">
                                                        <?= $this->Price->setPriceFormat($product['sales_price']) ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                    <!-- <div class="price"><?= $this->Price->setPriceFormat($product['promotion_price']) ?> <span>incl. VAT</span></div> -->
                                </div>
                            </div>
                        </div>
                    
                    <?php endforeach; ?>
           
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Brand Banner -->
<?php if (isset($widgets['general_section']) && is_array($widgets['general_section']) && !empty($widgets['general_section'][0]['widget']['summary'])): ?>
      <?= $widgets['general_section'][0]['widget']['summary'] ?>
<?php endif; ?>
<!-- Success History Section -->
<?php if (isset($widgets['customer_story']) && is_array($widgets['customer_story']) && !empty($widgets['customer_story'][0]['widget']['summary'])): ?>
      <?= $widgets['customer_story'][0]['widget']['summary'] ?>
<?php endif; ?>
<!-- Partners Section -->
<!-- <section class="py-5 client-logo">
        <div class="container">
            <h2 class="text-left mb-3 section-title">Prefer to buy our products elsewhere?</h2>
            <p class="text-left mb-5">No problem. Here’s our authorised online retail partners who’ll give you the same
                quality products as we do.</p>
            <div class="d-flex justify-content-center flex-wrap">
                <div class="owl-carousel owl-theme partner-carousel" id="partner-carousel">
                    <img src="/placeholder.svg?height=40&width=100" alt="ACE" class="partner-logo">
                    <img src="/placeholder.svg?height=40&width=100" alt="Amazon" class="partner-logo">
                    <img src="/placeholder.svg?height=40&width=100" alt="Carrefour" class="partner-logo">
                    <img src="/placeholder.svg?height=40&width=100" alt="Sharaf DG" class="partner-logo">
                    <img src="/placeholder.svg?height=40&width=100" alt="Jumbo" class="partner-logo">
                    <img src="/placeholder.svg?height=40&width=100" alt="Lulu" class="partner-logo">
                    <img src="/placeholder.svg?height=40&width=100" alt="Emax" class="partner-logo">
                </div>

            </div>
        </div>
    </section> -->

<!-- Testimonials Section -->
<section class="py-4 testimonial-banner">
    <div class="container">
        <p class="text-center mb-3 title">Testimonials</p>
        <h2 class="text-center mb-5 section-title">Our Client <span class="highlight">Reviews</span></h2>
        <div class="row">
            <div class="large-12 columns p-0">
                <div class="owl-carousel  owl-theme " id="owl-carousel-testimonial">
                    <div class="item testimonials">
                        <div class="testimonial-card">
                            <div class="profile-img">
                                <img src="https://i.pravatar.cc/60" alt="Profile">
                            </div>
                            <h5 class="mt-4 mb-1">Bang Upin</h5>
                            <p class="text-muted small">Pedagang Asongan</p>
                            <p class="mt-3 text-secondary">
                                “Terimakasih banyak, kini ruanganku menjadi lebih mewah dan terlihat mahal”
                            </p>
                            <div class="stars">
                                ★ ★ ★ ★ ☆
                            </div>
                        </div>
                    </div>
                    <div class="item testimonials-two">
                        <div class="testimonial-card">
                            <div class="profile-img">
                                <img src="https://i.pravatar.cc/60" alt="Profile">
                            </div>
                            <h5 class="mt-4 mb-1">Bang Upin</h5>
                            <p class="text-muted small">Pedagang Asongan</p>
                            <p class="mt-3 text-secondary">
                                “Terimakasih banyak, kini ruanganku menjadi lebih mewah dan terlihat mahal”
                            </p>
                            <div class="stars">
                                ★ ★ ★ ★ ☆
                            </div>
                        </div>
                    </div>
                    <div class="item testimonials-three">
                        <div class="testimonial-card">
                            <div class="profile-img">
                                <img src="https://i.pravatar.cc/60" alt="Profile">
                            </div>
                            <h5 class="mt-4 mb-1">Bang Upin</h5>
                            <p class="text-muted small">Pedagang Asongan</p>
                            <p class="mt-3 text-secondary">
                                “Terimakasih banyak, kini ruanganku menjadi lebih mewah dan terlihat mahal”
                            </p>
                            <div class="stars">
                                ★ ★ ★ ★ ☆
                            </div>
                        </div>
                    </div>
                    <div class="item testimonials">
                        <div class="testimonial-card">
                            <div class="profile-img">
                                <img src="https://i.pravatar.cc/60" alt="Profile">
                            </div>
                            <h5 class="mt-4 mb-1">Bang Upin</h5>
                            <p class="text-muted small">Pedagang Asongan</p>
                            <p class="mt-3 text-secondary">
                                “Terimakasih banyak, kini ruanganku menjadi lebih mewah dan terlihat mahal”
                            </p>
                            <div class="stars">
                                ★ ★ ★ ★ ☆
                            </div>
                        </div>
                    </div>
                    <div class="item testimonials">
                        <div class="testimonial-card">
                            <div class="profile-img">
                                <img src="https://i.pravatar.cc/60" alt="Profile">
                            </div>
                            <h5 class="mt-4 mb-1">Bang Upin</h5>
                            <p class="text-muted small">Pedagang Asongan</p>
                            <p class="mt-3 text-secondary">
                                “Terimakasih banyak, kini ruanganku menjadi lebih mewah dan terlihat mahal”
                            </p>
                            <div class="stars">
                                ★ ★ ★ ★ ☆
                            </div>
                        </div>
                    </div>
                    <div class="item testimonials">
                        <div class="testimonial-card">
                            <div class="profile-img">
                                <img src="https://i.pravatar.cc/60" alt="Profile">
                            </div>
                            <h5 class="mt-4 mb-1">Bang Upin</h5>
                            <p class="text-muted small">Pedagang Asongan</p>
                            <p class="mt-3 text-secondary">
                                “Terimakasih banyak, kini ruanganku menjadi lebih mewah dan terlihat mahal”
                            </p>
                            <div class="stars">
                                ★ ★ ★ ★ ☆
                            </div>
                        </div>
                    </div>
                    <div class="item testimonials">
                        <div class="testimonial-card">
                            <div class="profile-img">
                                <img src="https://i.pravatar.cc/60" alt="Profile">
                            </div>
                            <h5 class="mt-4 mb-1">Bang Upin</h5>
                            <p class="text-muted small">Pedagang Asongan</p>
                            <p class="mt-3 text-secondary">
                                “Terimakasih banyak, kini ruanganku menjadi lebih mewah dan terlihat mahal”
                            </p>
                            <div class="stars">
                                ★ ★ ★ ★ ☆
                            </div>
                        </div>
                    </div>
                    <div class="item testimonials">
                        <div class="testimonial-card">
                            <div class="profile-img">
                                <img src="https://i.pravatar.cc/60" alt="Profile">
                            </div>
                            <h5 class="mt-4 mb-1">Bang Upin</h5>
                            <p class="text-muted small">Pedagang Asongan</p>
                            <p class="mt-3 text-secondary">
                                “Terimakasih banyak, kini ruanganku menjadi lebih mewah dan terlihat mahal”
                            </p>
                            <div class="stars">
                                ★ ★ ★ ★ ☆
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>





        <!-- <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="review-card h-100">
                        <div class="d-flex align-items-center mb-3">
                            <img src="/placeholder.svg?height=60&width=60" alt="Customer" class="me-3">
                            <div>
                                <h5 class="mb-0">Sarah Johnson</h5>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p>
                            "The AC unit I purchased from OzoneX has been a game-changer. It's energy-efficient, quiet,
                            and cools my living room perfectly. The installation team was professional and quick."
                        </p>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="review-card h-100">
                        <div class="d-flex align-items-center mb-3">
                            <img src="/placeholder.svg?height=60&width=60" alt="Customer" class="me-3">
                            <div>
                                <h5 class="mb-0">Michael Chen</h5>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p>
                            "I've had my OzoneX air conditioner for over 2 years now and it still works like new. The
                            energy savings have been substantial compared to my old unit. Highly recommend!"
                        </p>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="review-card h-100">
                        <div class="d-flex align-items-center mb-3">
                            <img src="/placeholder.svg?height=60&width=60" alt="Customer" class="me-3">
                            <div>
                                <h5 class="mb-0">Aisha Rahman</h5>
                                <div class="rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p>
                            "Customer service at OzoneX is exceptional. When I had an issue with my AC, they sent a
                            technician the same day. The problem was fixed quickly and they followed up to ensure
                            everything was working properly."
                        </p>
                    </div>
                </div>
            </div> -->
    </div>
</section>
<script>
$(document).ready(function(){
    $('#featured-products-carousel').owlCarousel({
        loop: true,
        margin: 20,
        nav: true,
        dots: true,
        responsive: {
            0: {
                items: 1.2
            },
            576: {
                items: 2
            },
            992: {
                items: 3
            }
        }
    });
});
</script>
<?php $this->append('add_js'); ?>
<script>
    // Add to Cart functionality
    $(document).on('click', '.add-to-cart-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const productId = $(this).data('product-id');
        const button = $(this);
        const originalContent = button.html();

        // Show loading state
        button.prop('disabled', true).html(`
            <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                <circle cx="19" cy="19" r="15" stroke="white" stroke-width="2" fill="none" opacity="0.3"/>
                <path d="M19 4 L19 19 L30 19" stroke="white" stroke-width="2" stroke-linecap="round">
                    <animateTransform attributeName="transform" type="rotate" values="0 19 19;360 19 19" dur="1s" repeatCount="indefinite"/>
                </path>
            </svg>
        `);

        addToCart(productId)
            .then(response => {
                if (response.status === 'success') {
                    // Update cart count if provided
                    if (response.cartCount !== undefined && window.CartUpdater) {
                        window.CartUpdater.updateCartCount(response.cartCount);
                        console.log('Cart count updated to:', response.cartCount);
                    } else if (window.refreshCartCount) {
                        // Fallback: refresh cart count from server
                        window.refreshCartCount();
                        console.log('Refreshing cart count from server');
                    }

                    // Show success state
                    button.html(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" fill="none">
                            <circle cx="19" cy="19" r="15" stroke="white" stroke-width="2" fill="green" opacity="0.8"/>
                            <path d="M12 19 L17 24 L26 15" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    `);

                    // Show success message
                    showToastMessage(response.message || 'Product added to cart successfully!', 'success');

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.prop('disabled', false).html(originalContent);
                    }, 2000);
                } else {
                    button.prop('disabled', false).html(originalContent);
                    showToastMessage(response.message || 'Failed to add product to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                button.prop('disabled', false).html(originalContent);
                showToastMessage('Failed to add product to cart', 'error');
            });
    });

    // Helper function for add to cart
    function addToCart(productId) {
        return new Promise((resolve, reject) => {
            $.ajax({
                headers: {
                    'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
                },
                url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addToCart']) ?>/" + productId,
                type: 'POST',
                data: {quantity: 1},
                success: function (response) {
                    resolve(response);
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    // // Show cart message function - using common toast functionality
    // function showCartMessage(message, type) {
    //     showToastMessage(message, type);
    // }

    // Product card click functionality - redirect to product details
    $(document).on('click', '.clickable-product-card', function(e) {
        // Don't redirect if user clicked on cart button, wishlist button, or their children
        if ($(e.target).closest('.add-to-cart-btn, .wishlist, .add-cart').length > 0) {
            return;
        }

        const productId = $(this).data('product-id');
        if (productId) {
            // Redirect to product details page (Home controller, product method)
            window.location.href = `<?= $this->Url->build(['controller' => 'Home', 'action' => 'product']) ?>/${productId}`;
        }
    });
    // Product card click functionality - redirect to product details
    $(document).on('click', '.clickable-product-card', function(e) {
        // Don't redirect if user clicked on cart button, wishlist button, or their children
        if ($(e.target).closest('.add-to-cart-btn, .wishlist, .add-cart').length > 0) {
            return;
        }

        const productId = $(this).data('product-id');
        if (productId) {
            // Redirect to product details page (Home controller, product method)
            window.location.href = `<?= $this->Url->build(['controller' => 'Home', 'action' => 'product']) ?>/${productId}`;
        }
    });
</script>
<?php $this->end(); ?>