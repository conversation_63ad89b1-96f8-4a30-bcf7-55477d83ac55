<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Rejected - <?= h($order_number) ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 150px;
            height: auto;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            color: #dc3545;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .message {
            font-size: 16px;
            line-height: 1.6;
            color: #555;
            margin-bottom: 30px;
        }
        .order-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #dc3545;
        }
        .order-details h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 20px;
            font-weight: 600;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 500;
            color: #666;
        }
        .detail-value {
            font-weight: 600;
            color: #333;
        }
        .status-rejected {
            color: #dc3545;
            font-weight: 700;
            font-size: 18px;
        }
        .rejection-info {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
        }
        .rejection-info h3 {
            margin: 0 0 15px 0;
            color: #721c24;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .rejection-info h3::before {
            content: "⚠️";
            margin-right: 10px;
            font-size: 20px;
        }
        .rejection-info p {
            margin: 5px 0;
            line-height: 1.5;
        }
        .items-section {
            margin: 30px 0;
        }
        .items-section h3 {
            color: #333;
            font-size: 20px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .item:last-child {
            border-bottom: none;
        }
        .item-info {
            flex: 1;
        }
        .item-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .item-details {
            font-size: 14px;
            color: #666;
        }
        .item-total {
            font-weight: 600;
            color: #dc3545;
            font-size: 16px;
        }
        .refund-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            color: #0c5460;
        }
        .refund-info h3 {
            margin: 0 0 15px 0;
            color: #0c5460;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .refund-info h3::before {
            content: "💰";
            margin-right: 10px;
            font-size: 20px;
        }
        .refund-info p {
            margin: 5px 0;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: transform 0.3s ease;
            margin: 0 10px;
        }
        .cta-button:hover {
            transform: translateY(-2px);
            color: white;
        }
        .cta-button.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        .support-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            color: #856404;
        }
        .support-info h3 {
            margin: 0 0 15px 0;
            color: #856404;
            font-size: 18px;
            display: flex;
            align-items: center;
        }
        .support-info h3::before {
            content: "🤝";
            margin-right: 10px;
            font-size: 20px;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .footer .company-name {
            font-weight: 600;
            color: #dc3545;
            font-size: 16px;
        }
        .social-links {
            margin: 20px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #007bff;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 20px;
            }
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            .item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            .cta-button {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="https://ozone.com360degree.com/img/ozone/logo.png" alt="OZONEX Marketplace" width="150">
            </div>
            <h1><?= __('Order Rejected') ?></h1>
            <p><?= __('We apologize for any inconvenience') ?></p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                <?= __('Dear') ?> <?= h($customer_name) ?>,
            </div>

            <div class="message">
                <?= __('We regret to inform you that your order has been rejected. We understand this may be disappointing, and we sincerely apologize for any inconvenience this may cause.') ?>
            </div>

            <!-- Order Details -->
            <div class="order-details">
                <h3><?= __('Order Information') ?></h3>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Number:') ?></span>
                    <span class="detail-value"><?= h($order_number) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Date:') ?></span>
                    <span class="detail-value"><?= h($order_date) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Payment Method:') ?></span>
                    <span class="detail-value"><?= h($payment_method) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Status:') ?></span>
                    <span class="detail-value status-rejected"><?= __('REJECTED') ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Subtotal:') ?></span>
                    <span class="detail-value"><?= h($subtotal) ?></span>
                </div>
                  <div class="detail-row">
                    <span class="detail-label"><?= __('Delivery Charge:') ?></span>
                    <span class="detail-value"><?= h($delivery_charge) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Discount:') ?></span>
                    <span class="detail-value"><?= h($discount_amount) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Total Amount:') ?></span>
                    <span class="detail-value"><?= h($total_amount_formatted) ?></span>
                </div>
            </div>

            <!-- Rejection Information -->
            <div class="rejection-info">
                <h3><?= __('Rejection Details') ?></h3>
                <?php if (!empty($rejection_reason)): ?>
                <p><strong><?= __('Reason:') ?></strong> <?= h($rejection_reason) ?></p>
                <?php endif; ?>
                <p><strong><?= __('Rejected On:') ?></strong> <?= h($rejection_date) ?></p>
                <p><?= __('Your order was carefully reviewed but unfortunately could not be processed due to the reasons mentioned above.') ?></p>
            </div>

            <!-- Order Items -->
            <?php if (!empty($order_items)): ?>
            <div class="items-section">
                <h3><?= __('Rejected Items') ?></h3>
                <?php foreach ($order_items as $item): ?>
                <div class="item">
                    <div class="item-info">
                        <div class="item-name"><?= h($item['product_name']) ?></div>
                        <div class="item-details">
                            <?= __('Quantity:') ?> <?= h($item['quantity']) ?> × <?= h($item['price_formatted']) ?>
                        </div>
                    </div>
                    <div class="item-total"><?= h($item['total_formatted']) ?></div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Refund Information -->
            <?php if ($payment_method !== 'Cash on Delivery'): ?>
            <div class="refund-info">
                <h3><?= __('Refund Information') ?></h3>
                <p><?= __('Since your payment has been processed, we will initiate a full refund of') ?> <strong><?= h($total_amount_formatted) ?></strong> <?= __('to your original payment method.') ?></p>
                <p><?= __('Please allow 3-7 business days for the refund to appear in your account.') ?></p>
                <p><?= __('You will receive a separate email confirmation once the refund has been processed.') ?></p>
            </div>
            <?php endif; ?>

            <!-- Support Information -->
            <div class="support-info">
                <h3><?= __('Need Assistance?') ?></h3>
                <p><?= __('If you have any questions about this rejection or would like to discuss alternative options, our customer support team is here to help.') ?></p>
                <p><?= __('You can also browse our website for similar products that might interest you.') ?></p>
            </div>

            <!-- Call to Action -->
            <!-- <div class="cta-section">
                <a href="<?= $contact_support_url ?>" class="cta-button">
                    <?= __('Contact Support') ?>
                </a>
                <a href="<?= $browse_products_url ?>" class="cta-button secondary">
                    <?= __('Browse Products') ?>
                </a>
            </div> -->

            <div class="message">
                <?= __('We value your business and hope to serve you better in the future. Thank you for choosing OZONEX Marketplace.') ?>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="company-name"><?= __('OZONEX Marketplace') ?></p>
            <p><?= __('We apologize for any inconvenience caused.') ?></p>
            <div class="social-links">
                <a href="#"><?= __('Contact Support') ?></a> |
                <a href="#"><?= __('Return Policy') ?></a> |
                <a href="#"><?= __('Browse Products') ?></a>
            </div>
            <p><?= __('This email was sent to') ?> <?= h($customer_email) ?></p>
            <p><?= __('© 2025 OZONEX Marketplace. All rights reserved.') ?></p>
        </div>
    </div>
</body>
</html>
