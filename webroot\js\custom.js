    "use strict";

// ========================================
// HELPER FUNCTIONS
// ========================================

/**
 * Get CSRF token from various sources
 */
function getCsrfToken() {
    // First try the global variable set by PHP
    if (window.csrfToken) {
        return window.csrfToken;
    }

    // Try to get CSRF token from meta tags (most common)
    let csrfToken = document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                   document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    // If not found in meta tags, try to get from hidden input in forms
    if (!csrfToken) {
        const csrfInput = document.querySelector('input[name="_csrfToken"]');
        if (csrfInput) {
            csrfToken = csrfInput.value;
        }
    }

    // If still not found, try to get from any form on the page
    if (!csrfToken) {
        const forms = document.querySelectorAll('form');
        for (let form of forms) {
            const tokenInput = form.querySelector('input[name="_csrfToken"]');
            if (tokenInput) {
                csrfToken = tokenInput.value;
                break;
            }
        }
    }

    // Debug logging
    if (!csrfToken) {
        // console.warn('CSRF token not found. This may cause AJAX requests to fail.');
        // console.log('Available meta tags:', document.querySelectorAll('meta[name*="csrf"], meta[name*="CSRF"]'));
        // console.log('Available CSRF inputs:', document.querySelectorAll('input[name="_csrfToken"]'));
        // console.log('Global csrfToken:', window.csrfToken);
    }

    return csrfToken || '';
}

// ========================================
// COMMON COUPON FUNCTIONALITY
// ========================================

/**
 * Apply coupon from input field
 */
function applyCoupon() {
    const couponInput = document.getElementById('coupon-code-input');
    const couponCode = couponInput.value.trim();

    if (!couponCode) {
        showToastMessage('Please enter a coupon code', 'error');
        return;
    }

    applyCouponCode(couponCode);
}

/**
 * Apply specific coupon code
 */
function applyCouponCode(couponCode) {
    const applyBtn = document.getElementById('apply-coupon-btn');
    const btnText = applyBtn.querySelector('.btn-text');
    const spinner = applyBtn.querySelector('.spinner-border');

    // Show loading state
    btnText.textContent = 'Applying...';
    spinner.classList.remove('d-none');
    applyBtn.disabled = true;

    // Get CSRF token
    const csrfToken = getCsrfToken();

    if (!csrfToken) {
        showToastMessage('Security token not found. Please refresh the page and try again.', 'error');
        // Reset button state
        btnText.textContent = 'Apply';
        spinner.classList.add('d-none');
        applyBtn.disabled = false;
        return;
    }

    fetch('/cart/applyCoupon', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify({
            coupon_code: couponCode
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('Coupon apply response:', data); // Debug logging
        if (data.success) {
            console.log('Coupon applied successfully, reloading page...'); // Debug logging
            showToastMessage(data.message, 'success');
            // Show loading message and reload page
            btnText.textContent = 'Applied! Refreshing...';
            showPageReloadMessage('Coupon applied successfully! Updating cart...');
            setTimeout(() => {
                console.log('Executing page reload...'); // Debug logging
                window.location.reload();
            }, 1500);
        } else {
            console.log('Coupon apply failed:', data.message); // Debug logging
            showToastMessage(data.message, 'error');
            // Reset button state
            btnText.textContent = 'Apply';
            spinner.classList.add('d-none');
            applyBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error applying coupon:', error);
        showToastMessage('An error occurred while applying the coupon', 'error');
        // Reset button state
        btnText.textContent = 'Apply';
        spinner.classList.add('d-none');
        applyBtn.disabled = false;
    });
}

/**
 * Remove applied coupon
 */
function removeCoupon() {
    if (!confirm('Are you sure you want to remove this coupon?')) {
        return;
    }

    // Show loading state on remove button if it exists
    const removeBtn = document.querySelector('[onclick="removeCoupon()"]');
    if (removeBtn) {
        removeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Removing...';
        removeBtn.disabled = true;
    }

    // Get CSRF token
    const csrfToken = getCsrfToken();

    if (!csrfToken) {
        showToastMessage('Security token not found. Please refresh the page and try again.', 'error');
        return;
    }

    fetch('/cart/removeCoupon', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Coupon remove response:', data); // Debug logging
        if (data.success) {
            console.log('Coupon removed successfully, reloading page...'); // Debug logging
            showToastMessage(data.message, 'success');
            // Show loading message and reload page
            showPageReloadMessage('Coupon removed successfully! Updating cart...');
            setTimeout(() => {
                console.log('Executing page reload...'); // Debug logging
                window.location.reload();
            }, 1500);
        } else {
            console.log('Coupon remove failed:', data.message); // Debug logging
            showToastMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error removing coupon:', error);
        showToastMessage('An error occurred while removing the coupon', 'error');
    });
}

/**
 * Toggle available coupons display
 */
function toggleAvailableCoupons() {
    const container = document.getElementById('available-coupons');
    const toggleText = document.getElementById('available-coupons-toggle-text');
    const toggleIcon = document.getElementById('available-coupons-toggle-icon');

    if (container.style.display === 'none') {
        container.style.display = 'block';
        toggleText.textContent = 'Hide available coupons';
        toggleIcon.classList.remove('fa-chevron-down');
        toggleIcon.classList.add('fa-chevron-up');
    } else {
        container.style.display = 'none';
        toggleText.textContent = 'View available coupons';
        toggleIcon.classList.remove('fa-chevron-up');
        toggleIcon.classList.add('fa-chevron-down');
    }
}

/**
 * Show coupon terms and conditions modal
 */
function showCouponTerms(couponCode, termsConditions) {
    document.getElementById('couponCodeTitle').textContent = couponCode;
    document.getElementById('couponTermsContent').innerHTML = termsConditions.replace(/\n/g, '<br>');

    const modal = new bootstrap.Modal(document.getElementById('couponTermsModal'));
    modal.show();
}

/**
 * Show page reload message with loading indicator
 */
function showPageReloadMessage(message) {
    // Create or update loading overlay
    let overlay = document.getElementById('page-reload-overlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'page-reload-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-size: 18px;
            text-align: center;
        `;
        document.body.appendChild(overlay);
    }

    overlay.innerHTML = `
        <div style="background: white; padding: 30px; border-radius: 10px; color: #333; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
            <div style="margin-bottom: 20px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #28a745;"></i>
            </div>
            <div style="font-weight: bold; margin-bottom: 10px;">${message}</div>
            <div style="color: #666; font-size: 14px;">Please wait...</div>
        </div>
    `;
    overlay.style.display = 'flex';
}

/**
 * Initialize coupon functionality
 */
function initializeCouponFunctionality() {
    // Handle Enter key press in coupon input
    const couponInput = document.getElementById('coupon-code-input');
    if (couponInput) {
        couponInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                applyCoupon();
            }
        });
    }
}

/**
 * Debug function to test CSRF token detection
 * Call debugCsrfToken() in browser console to test
 */
function debugCsrfToken() {
    const token = getCsrfToken();
    console.log('=== CSRF Token Debug ===');
    console.log('Token found:', token ? 'YES' : 'NO');
    console.log('Token value:', token);
    console.log('Token length:', token.length);
    console.log('Global window.csrfToken:', window.csrfToken);
    console.log('Meta tag csrfToken:', document.querySelector('meta[name="csrfToken"]')?.getAttribute('content'));
    console.log('Meta tag csrf-token:', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));
    console.log('Form CSRF inputs:', document.querySelectorAll('input[name="_csrfToken"]'));
    return token;
}

// Make debug function globally available
window.debugCsrfToken = debugCsrfToken;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCouponFunctionality();

    // Log CSRF token status on page load
    console.log('CSRF Token available:', getCsrfToken() ? 'YES' : 'NO');
});