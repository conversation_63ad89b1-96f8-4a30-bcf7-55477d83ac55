function selectDropdownItem(element, languageCode, country) {

    localStorage.setItem('user_country', country);
    localStorage.setItem('user_language', languageCode);

    // First validate coupon for country change, then update settings
    validateCouponOnCountryChange(country, function() {
        $.ajax({
            url: '/Home/getSiteSettings',
            method: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
            },
            data: JSON.stringify({
                country: country,
                lang: languageCode.toLowerCase()
            }),
            beforeSend: function(xhr) {
                // Include CSRF token in the request headers
                xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrfToken"]').attr('content'));
            },
            success: function (data) {
                window.location.reload();
            },
            error: function (xhr, status, error) {
                console.error('Error:', error);
                if (xhr.status === 403) {
                    alert('CSRF token validation failed. Please refresh the page and try again.');
                }
            }
        });
    });
}

/**
 * Validate coupon when country changes
 * @param {string} newCountry - The newly selected country
 * @param {function} callback - Callback to execute after validation
 */
function validateCouponOnCountryChange(newCountry, callback) {
    // Get country ID mapping
    const countryMapping = {
        'Qatar': 1,
        'Saudi Arabia': 2
    };

    const newCountryId = countryMapping[newCountry] || 1;

    // Only validate if we're on cart-related pages
    const currentPath = window.location.pathname;
    const isCartPage = currentPath.includes('/cart') || currentPath.includes('/address') || currentPath.includes('/checkout');

    if (!isCartPage) {
        // Not on cart pages, proceed with country change
        callback();
        return;
    }

    $.ajax({
        url: '/Cart/handleCountryChange',
        method: 'POST',
        data: {
            country_id: newCountryId
        },
        headers: {
            'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // Handle coupon action
                if (response.coupon_action === 'removed') {
                    // Show notification that coupon was removed
                    showCouponNotification(response.message, 'warning');
                } else if (response.coupon_action === 'kept') {
                    // Show notification that coupon is still valid
                    showCouponNotification(response.message, 'success');
                }

                // Proceed with country change
                callback();
            } else {
                console.error('Coupon validation failed:', response.message);
                // Still proceed with country change
                callback();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error validating coupon on country change:', error);
            // Still proceed with country change
            callback();
        }
    });
}

/**
 * Show coupon notification
 * @param {string} message - The message to show
 * @param {string} type - The type of notification (success, warning, error)
 */
function showCouponNotification(message, type) {
    // Create notification element
    const notification = $(`
        <div class="coupon-notification alert alert-${type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'danger'} alert-dismissible fade show"
             style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
            <i class="fas fa-${type === 'warning' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'times-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    // Add to body
    $('body').append(notification);

    // Auto remove after 5 seconds
    setTimeout(function() {
        notification.fadeOut(function() {
            $(this).remove();
        });
    }, 5000);
}

// function selectDropdownregion(element) {
//     const button = document.getElementById("dropdownregion");
//     const selectedContent = element.innerHTML;
//     button.innerHTML = selectedContent;
// }

// document.addEventListener('DOMContentLoaded', function() {
// fetch('https://ipapi.co/json/')
// .then(response => response.json())
// .then(data => {
//     const country = data.country_name;
//     if (country === 'Qatar' || country === 'Saudi Arabia') {
//         selectDropdownItem('', 'eng', country);
//     } else {
//         selectDropdownItem('', 'eng', 'Qatar');
//     }
// });
// });

document.addEventListener('DOMContentLoaded', function () {
  // Check if country is already stored in sessionStorage
  const countryInSession = sessionStorage.getItem('user_country');

  if (!countryInSession) {
    // Only fetch if not stored yet
    fetch('https://ipapi.co/json/')
      .then(response => response.json())
      .then(data => {
        const country = data.country_name?.trim() || 'Qatar';
        console.log('Fetched country:', country);
        // Save only to sessionStorage (removes after browser close)
        sessionStorage.setItem('user_country', country);

        // Call your dropdown function with cart checking
        if (country === 'Qatar' || country === 'Saudi Arabia') {
          selectDropdownItemWithCartCheck('', 'eng', country);
        } else {
          selectDropdownItemWithCartCheck('', 'eng', 'Qatar');
        }
      })
      .catch(() => {
         console.log('Fetched country 2:');
        // On error, fallback and store default in sessionStorage
        sessionStorage.setItem('user_country', 'Qatar');
        selectDropdownItemWithCartCheck('', 'eng', 'Qatar');
      });
  }
});

function customNumberFormat(price) {
  return parseFloat(price).toFixed(2);
}
function customNumberFormatWithCountry(price) {
  const formattedPrice = parseFloat(price).toFixed(2);
  let country = localStorage.getItem('user_country') || 'Qatar';
 
  switch (country) {
    case 'Qatar':
      return `${formattedPrice} QAR`;
    case 'Saudi Arabia':
      return `${formattedPrice} SAR`;
    default:
      return `${formattedPrice} QAR`;
  }
}