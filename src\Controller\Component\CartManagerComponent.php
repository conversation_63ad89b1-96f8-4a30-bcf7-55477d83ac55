<?php
namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\Utility\Text;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
class CartManagerComponent extends Component
{
    protected $controller;
    protected $Carts;
    protected $CartItems;


       public function initialize(array $config): void
    {
        parent::initialize($config);
        $this->controller = $this->_registry->getController();
        $this->Carts = TableRegistry::getTableLocator()->get('Carts');
        $this->CartItems = TableRegistry::getTableLocator()->get('CartItems');
        $this->Users = TableRegistry::getTableLocator()->get('Users');
        $this->Offers = TableRegistry::getTableLocator()->get('Offers');
    }

      public function addToCart($productId, $variantId = null, $quantity = 1)
    {
        $session = $this->controller->getRequest()->getSession();
        $identity = $session->read('Auth.User');
        $customerId = null;

        if ($identity) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        $guestToken = $session->read('GuestToken');

        if (!$customerId && !$guestToken) {
            $guestToken = Text::uuid();
            $session->write('GuestToken', $guestToken);
        }

        $cart = $this->Carts->find()
            ->where($customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken])
            ->first();

        if (!$cart) {
            $cart = $this->Carts->newEntity([
                'customer_id' => $customerId,
                'guest_token' => $guestToken,
            ]);
            $this->Carts->save($cart);
        }

        // Check if the item already exists
        $query = $this->CartItems->find()
            ->where([
                'cart_id' => $cart->id,
                'product_id' => $productId
            ]);

        if ($variantId === null) {
            $query->where(['product_variant_id IS' => null]);
        } else {
            $query->where(['product_variant_id' => $variantId]);
        }

        $existingItem = $query->first();

        // Fetch promotion_price
        $Products = TableRegistry::getTableLocator()->get('Products');
        $product = $Products->find()
            ->select(['promotion_price'])
            ->where(['id' => $productId])
            ->first();

        $unitPrice = $product ? $product->promotion_price : 0;

        \Cake\Log\Log::debug('AddToCart - Product ID: ' . $productId . ', Quantity to add: ' . $quantity . ', Unit Price: ' . $unitPrice);

        if ($existingItem) {
            \Cake\Log\Log::debug('Existing item found - Current quantity: ' . $existingItem->quantity . ', Current price: ' . $existingItem->price);
            $existingItem->quantity += $quantity;
            $existingItem->price = $existingItem->quantity * $unitPrice;
            \Cake\Log\Log::debug('Updated existing item - New quantity: ' . $existingItem->quantity . ', New price: ' . $existingItem->price);
        } else {
            \Cake\Log\Log::debug('Creating new cart item');
            $existingItem = $this->CartItems->newEntity([
                'cart_id' => $cart->id,
                'customer_id'=> $customerId,
                'product_id' => $productId,
                'product_variant_id' => $variantId,
                'quantity' => $quantity,
                'price' => $quantity * $unitPrice,
            ]);
            \Cake\Log\Log::debug('New cart item - Quantity: ' . $quantity . ', Price: ' . ($quantity * $unitPrice));
        }

        $saveResult = $this->CartItems->save($existingItem);
        \Cake\Log\Log::debug('Save result: ' . ($saveResult ? 'Success' : 'Failed'));

        if ($saveResult) {
            \Cake\Log\Log::debug('Final saved item - ID: ' . $existingItem->id . ', Quantity: ' . $existingItem->quantity . ', Price: ' . $existingItem->price);
        }

        return $saveResult;
    }
      public function getCartData($customerId = null, $guestToken = null)
    {
        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];


        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products', 'ProductVariants']])
            ->first();


        $cartItems = [];
        $cartOutItems = [];
        $totalPrice = 0;
        $totalSalePrice = 0;

        if ($cart) {
            \Cake\Log\Log::debug('Cart found with ID: ' . $cart->id . ', Total cart items: ' . count($cart->cart_items));

            foreach ($cart->cart_items as $item) {
                \Cake\Log\Log::debug('Processing cart item - ID: ' . $item->id . ', Product ID: ' . $item->product_id . ', Quantity: ' . $item->quantity . ', Price: ' . $item->price);

                $price = null;
                $salePrice = null;

                // if ($item->product_variant_id && $item->product_variant) {
                //     $price = $item->product_variant->promotion_price;
                //     $salePrice = $item->product_variant->sales_price;
                // }

                if (!$price) {
                    $product = $item->product;
                    $price = $product->promotion_price ?? $this->controller->Products->getProductPrice($item->product_id);
                    $salePrice = $product->sales_price ?? null;
                }

                \Cake\Log\Log::debug('Product prices - Unit price: ' . $price . ', Sale price: ' . $salePrice);

                // $image = $this->controller->ProductImages->getDefaultProductImage($item->product_id);
                // $availability = $this->controller->Products->getAvailabilityStatus($item->product_id);

                // if ($image) {
                //     $image = $this->controller->Media->getCloudFrontURL($image);
                // }

                // $discount = $this->controller->Products->getDiscountProduct($item->product_id, $item->product_variant_id);
                $unitSalePrice = $salePrice ?? $item->product->sales_price;

                $totalRowPrice = $item->quantity * $price;
                $totalRowSalePrice = $item->quantity * $unitSalePrice;

                $totalPrice += $totalRowPrice;
                $totalSalePrice += $totalRowSalePrice;

                $itemData = [
                    'cart_item_id' => $item->id,
                    'product_id' => $item->product_id,
                    'product_variant_id' => $item->product_variant_id,
                    'product_name' => $item->product->name,
                    'variant_name' => $item->product_variant->name ?? $item->product->name,
                    'quantity' => $item->quantity,
                    'price' => $price,
                    'sale_price' => $totalRowSalePrice,
                     'total_price' =>$totalRowPrice,
                    // 'sale_price' => number_format($totalRowSalePrice, 2),
                    // 'total_price' => number_format($totalRowPrice, 2),
                   // 'discount' => $discount,
                    // 'product_image' => $image,
                    // 'get_available_status' => $availability
                ];

                \Cake\Log\Log::debug('Item data for template: ' . json_encode($itemData));
                $cartItems[] = $itemData;
                // if ($availability === "In Stock") {
                //     $cartItems[] = $itemData;
                // } else {
                //     $cartOutItems[] = $itemData;
                // }
            }
        } else {
            \Cake\Log\Log::debug('No cart found for customer ID: ' . $customerId . ', guest token: ' . $guestToken);
        }

        return [
            'cart_id' => $cart->id ?? 0,
            'cartItems' => $cartItems,
            'cartOutItems' => $cartOutItems,
            'totalPrice' => $totalPrice,
            'totalSalePrice' => number_format($totalSalePrice, 2),
            // 'totalDiscountedPrice' => number_format($totalSalePrice - $totalPrice, 2),
            'total_items' => count($cartItems),
            'checkCartCount' => count($cartItems) + count($cartOutItems)
        ];
    }

    public function getOrderSummary($customerId = null, $guestToken = null, $region = 'Qatar', $shippingCost = 50.00, $discountAmount = 0.00, $couponCode = null, $couponType = null)
    {
        // Get cart data
        $cartData = $this->getCartData($customerId, $guestToken);
        $subtotal = (float) str_replace(',', '', $cartData['totalPrice']);
        $totalSalePrice = (float) str_replace(',', '', $cartData['totalSalePrice']);
        $totalSavings = $totalSalePrice - $subtotal;

        // Handle shipping discount for shipping coupons
        $finalShippingCost = $shippingCost;
        if ($couponType === 'shipping' && !empty($couponCode)) {
            $finalShippingCost = 0; // Free shipping
        }

        $finalTotal = $subtotal - $discountAmount + $finalShippingCost;
        $estimated_days = Configure::read('Settings.ESTIMATED_DATE');
        $estimatedDeliveryDate = date('d M, Y', strtotime($estimated_days));

        return [
            'cart_id' => $cartData['cart_id'],
            'cart_items' => $cartData['cartItems'],
            'total_items' => $cartData['total_items'],
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'total_savings' => $totalSavings,
            'shipping_cost' => $finalShippingCost,
            'original_shipping_cost' => $shippingCost,
            'final_total' => $finalTotal,
            'estimated_delivery_date' => $estimatedDeliveryDate,
            'coupon_code' => $couponCode,
            'coupon_type' => $couponType,
            'has_items' => $cartData['total_items'] > 0,
            'shipping_discount' => $couponType === 'shipping' ? $shippingCost : 0
        ];
    }


    public function applyCoupon($couponCode, $subtotal)
    {
        $discountAmount = 0;
        $isValid = false;
        $message = 'Invalid coupon code';
        $couponType = '';
        $discountValue = 0;
        $couponDetails = null;

        $couponCode = strtoupper(trim($couponCode));
        $currentDate = date('Y-m-d H:i:s');
    
        // Fetch coupon from database using the existing applyCoupon method in OffersTable
        $offer = $this->Offers->applyCoupon($couponCode, $subtotal, $currentDate);

        if ($offer) {
            $isValid = true;
            $couponDetails = [
                'id' => $offer->id,
                'offer_name' => $offer->offer_name,
                'offer_code' => $offer->offer_code,
                'description' => $offer->offer_description,
                'min_amount' => (float) $offer->min_cart_value,
                'max_discount' => (float) $offer->max_discount,
                'free_shipping' => (bool) $offer->free_shipping,
                'terms_conditions' => $offer->terms_conditions
            ];

            // Determine coupon type based on offer_type and free_shipping
            if ($offer->free_shipping == 1) {
                $couponType = 'shipping';
                $discountAmount = 0; // Shipping discount handled separately
                $discountValue = 0;
                $message = "Coupon applied! Free shipping";
            } elseif ($offer->offer_type === 'Percentage') {
                $couponType = 'percentage';
                $discountValue = (float) $offer->discount;
                $discountAmount = ($subtotal * $discountValue) / 100;

                // Apply max discount limit if set
                if (!empty($offer->max_discount) && $discountAmount > (float) $offer->max_discount) {
                    $discountAmount = (float) $offer->max_discount;
                    $message = "Coupon applied! {$discountValue}% discount (Max " . number_format($offer->max_discount, 2) . ")";
                } else {
                    $message = "Coupon applied! {$discountValue}% discount";
                }
            } else {
                // Fixed amount discount
                $couponType = 'fixed';
                $discountAmount = (float) $offer->discount;
                $discountValue = $discountAmount;
                $message = "Coupon applied! " . number_format($discountAmount, 2) . " QAR discount";
            }
        } else {
            // Check if coupon exists but doesn't meet criteria
            $existingOffer = $this->Offers->find()
                ->where([
                    'offer_code' => $couponCode,
                    'status' => 'A'
                ])
                ->first();
          
           
            if ($existingOffer) {
                if ($subtotal < (float) $existingOffer->min_cart_value) {
                    $message = "Minimum order amount of " . number_format($existingOffer->min_cart_value, 2) . "  required for this coupon";
                } elseif ($existingOffer->offer_start_date > $currentDate) {
                    $message = "This coupon is not yet active. Valid from " . $existingOffer->offer_start_date->format('d M, Y');
                } elseif ($existingOffer->offer_end_date && $existingOffer->offer_end_date < $currentDate) {
                    $message = "This coupon has expired on " . $existingOffer->offer_end_date->format('d M, Y');
                } else {
                    $message = "This coupon is not applicable for online orders";
                }
            } else {
                $message = "Invalid coupon code. Please check and try again.";
            }
        }

        return [
            'is_valid' => $isValid,
            'discount_amount' => round($discountAmount, 2),
            'discount_value' => $discountValue,
            'coupon_type' => $couponType,
            'message' => $message,
            'coupon_code' => $couponCode,
            'coupon_details' => $couponDetails,
            'offer_id' => $offer ? $offer->id : null
        ];
    }


    public function removeCoupon($couponCode)
    {
        return [
            'is_removed' => true,
            'message' => "Coupon '{$couponCode}' has been removed successfully",
            'coupon_code' => $couponCode
        ];
    }

    /**
     * Get available coupons for display from database
     * @param float $subtotal Current cart subtotal
     * @param int|null $countryId Filter coupons by country ID
     * @return array List of available coupons with their details
     */
    public function getAvailableCoupons($subtotal = 0, $countryId = null)
    {
        $currentDate = date('Y-m-d H:i:s');
        $query = $this->Offers->find()
            ->where([
                'status' => 'A',

            ]);

        // Add country filter if provided
        if ($countryId !== null) {
            $query->where([
                'OR' => [
                    'country_id' => $countryId,
                    'country_id IS' => null 
                ]
            ]);
        }
        else{
             $query->where([
                'OR' => [
                    'country_id' => 1,
                    'country_id IS' => null 
                ]
            ]);
        }
        $offers = $query->order(['min_cart_value' => 'ASC', 'discount' => 'DESC'])
            ->toArray();
        

        $availableCoupons = [];

        foreach ($offers as $offer) {
            // Determine coupon type
            $couponType = 'fixed'; // default
            if ($offer->free_shipping == 1) {
                $couponType = 'shipping';
            } elseif ($offer->offer_type === 'Percentage') {
                $couponType = 'percentage';
            }

            // Format description
            $description = $offer->offer_description;
            if (empty($description)) {
                if ($couponType === 'shipping') {
                    $description = 'Free shipping on orders above ' . number_format($offer->min_cart_value, 0);
                } elseif ($couponType === 'percentage') {
                    $description = $offer->discount . '% off on orders above ' . number_format($offer->min_cart_value, 0);
                } else {
                    $description = number_format($offer->discount, 0) . ' off on orders above ' . number_format($offer->min_cart_value, 0);
                }
            }

            $minAmount = (float) $offer->min_cart_value;
            $isApplicable = $subtotal >= $minAmount;

            $couponData = [
                'id' => $offer->id,
                'country_id' => $offer->country_id,
                'code' => $offer->offer_code,
                'type' => $couponType,
                'value' => (float) $offer->discount,
                'min_amount' => $minAmount,
                'max_discount' => (float) $offer->max_discount,
                'description' => $description,
                'offer_name' => $offer->offer_name,
                'is_applicable' => $isApplicable,
                'amount_needed' => $isApplicable ? 0 : ($minAmount - $subtotal),
                'free_shipping' => (bool) $offer->free_shipping,
                'terms_conditions' => $offer->terms_conditions,
                'valid_until' => $offer->offer_end_date ? $offer->offer_end_date->format('d M, Y') : 'No expiry'
            ];

            $availableCoupons[] = $couponData;
        }
        return $availableCoupons;
    }
  
    /**
     * Validate if a coupon can be applied
     * @param string $couponCode
     * @param float $subtotal
     * @return array Validation result
     */
    // public function validateCoupon($couponCode, $subtotal)
    // {
    //     $result = $this->applyCoupon($couponCode, $subtotal);
    //     return [
    //         'is_valid' => $result['is_valid'],
    //         'message' => $result['message'],
    //         'can_apply' => $result['is_valid']
    //     ];
    // }

    /**
     * Get all active coupons for admin/management purposes
     * @return array List of all active coupons
     */
    // public function getAllActiveCoupons()
    // {
    //     $currentDate = date('Y-m-d H:i:s');

    //     return $this->Offers->find()
    //         ->where([
    //             'status' => 'A',
    //             'offer_start_date <=' => $currentDate,
    //             'OR' => [
    //                 'offer_end_date IS' => null,
    //                 'offer_end_date >=' => $currentDate
    //             ]
    //         ])
    //         ->order(['created' => 'DESC'])
    //         ->toArray();
    // }

    /**
     * Get coupon statistics
     * @return array Coupon usage statistics
     */
    // public function getCouponStatistics()
    // {
    //     $currentDate = date('Y-m-d H:i:s');

    //     $totalCoupons = $this->Offers->find()->where(['status' => 'A'])->count();
    //     $activeCoupons = $this->Offers->find()
    //         ->where([
    //             'status' => 'A',
    //             'offer_start_date <=' => $currentDate,
    //             'OR' => [
    //                 'offer_end_date IS' => null,
    //                 'offer_end_date >=' => $currentDate
    //             ]
    //         ])
    //         ->count();

    //     $expiredCoupons = $this->Offers->find()
    //         ->where([
    //             'status' => 'A',
    //             'offer_end_date <' => $currentDate
    //         ])
    //         ->count();

    //     return [
    //         'total_coupons' => $totalCoupons,
    //         'active_coupons' => $activeCoupons,
    //         'expired_coupons' => $expiredCoupons,
    //         'upcoming_coupons' => $totalCoupons - $activeCoupons - $expiredCoupons
    //     ];
    // }


}