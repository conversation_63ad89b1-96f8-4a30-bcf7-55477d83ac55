<?php
/**
 * Sales & Revenue Reports Dashboard
 */
?>

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= __('Sales & Revenue Reports') ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <?= $this->Html->link(__('Dashboard'), ['controller' => 'Dashboards', 'action' => 'adminDashboard']) ?>
                    </li>
                    <li class="breadcrumb-item">
                        <?= $this->Html->link(__('Reports'), ['action' => 'index']) ?>
                    </li>
                    <li class="breadcrumb-item active"><?= __('Sales & Revenue') ?></li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <?= $this->Html->link(
                '<i class="fas fa-chart-line me-2"></i>' . __('Revenue Details'),
                ['action' => 'revenueDetails'],
                ['class' => 'btn btn-primary', 'escape' => false]
            ) ?>
            <?= $this->Html->link(
                '<i class="fas fa-shopping-cart me-2"></i>' . __('Sales Details'),
                ['action' => 'salesDetails'],
                ['class' => 'btn btn-info', 'escape' => false]
            ) ?>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i><?= __('Report Filters') ?>
            </h6>
        </div>
        <div class="card-body">
            <?= $this->Form->create(null, [
                'type' => 'get',
                'class' => 'row g-3',
                'id' => 'reportFiltersForm'
            ]) ?>
                <div class="col-md-3">
                    <label class="form-label"><?= __('Date From') ?></label>
                    <?= $this->Form->control('date_from', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'value' => $filters['date_from'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-3">
                    <label class="form-label"><?= __('Date To') ?></label>
                    <?= $this->Form->control('date_to', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'value' => $filters['date_to'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Country') ?></label>
                    <?= $this->Form->control('country_id', [
                        'type' => 'select',
                        'options' => ['' => __('All Countries')] + array_column($countries, 'name', 'id'),
                        'class' => 'form-select',
                        'value' => $filters['country_id'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Status') ?></label>
                    <?= $this->Form->control('status', [
                        'type' => 'select',
                        'options' => [
                            '' => __('All Statuses'),
                            'Pending' => __('Pending'),
                            'Confirmed' => __('Confirmed'),
                            'Processing' => __('Processing'),
                            'Shipped' => __('Shipped'),
                            'Out for Delivery' => __('Out for Delivery'),
                            'Delivered' => __('Delivered'),
                            'Rejected' => __('Rejected')
                        ],
                        'class' => 'form-select',
                        'value' => $filters['status'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Order Type') ?></label>
                    <?= $this->Form->control('order_type', [
                        'type' => 'select',
                        'options' => [
                            '' => __('All Types'),
                            'Online' => __('Online'),
                            'Showroom' => __('Showroom')
                        ],
                        'class' => 'form-select',
                        'value' => $filters['order_type'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i><?= __('Apply Filters') ?>
                    </button>
                    <?= $this->Html->link(
                        '<i class="fas fa-undo me-2"></i>' . __('Reset'),
                        ['action' => 'salesReports'],
                        ['class' => 'btn btn-secondary', 'escape' => false]
                    ) ?>
                </div>
            <?= $this->Form->end() ?>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                <?= __('Total Revenue') ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $currencySymbol ?> <?= number_format($summaryStats['total_revenue'], 2) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                <?= __('Total Orders') ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($summaryStats['total_orders']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                <?= __('Average Order Value') ?>
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $currencySymbol ?> <?= number_format($summaryStats['avg_order_value'], 2) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                <?= __('Report Period') ?>
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                <?= date('M j', strtotime($filters['date_from'])) ?> - 
                                <?= date('M j, Y', strtotime($filters['date_to'])) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analysis Row -->
    <div class="row mb-4">
        <!-- Orders by Status -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary"><?= __('Orders by Status') ?></h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th><?= __('Status') ?></th>
                                    <th><?= __('Count') ?></th>
                                    <th><?= __('Revenue') ?></th>
                                    <th><?= __('%') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($summaryStats['orders_by_status'] as $status): ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-<?= $this->element('status_color', ['status' => $status->status]) ?>">
                                            <?= h($status->status) ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($status->count) ?></td>
                                    <td><?= $currencySymbol ?> <?= number_format($status->revenue, 2) ?></td>
                                    <td>
                                        <?php 
                                        $percentage = $summaryStats['total_orders'] > 0 ? 
                                            round(($status->count / $summaryStats['total_orders']) * 100, 1) : 0;
                                        ?>
                                        <?= $percentage ?>%
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders by Type -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary"><?= __('Orders by Type') ?></h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th><?= __('Type') ?></th>
                                    <th><?= __('Count') ?></th>
                                    <th><?= __('Revenue') ?></th>
                                    <th><?= __('%') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($summaryStats['orders_by_type'] as $type): ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-<?= $type->order_type === 'Online' ? 'primary' : 'secondary' ?>">
                                            <?= h($type->order_type) ?>
                                        </span>
                                    </td>
                                    <td><?= number_format($type->count) ?></td>
                                    <td><?= $currencySymbol ?> <?= number_format($type->revenue, 2) ?></td>
                                    <td>
                                        <?php 
                                        $percentage = $summaryStats['total_orders'] > 0 ? 
                                            round(($type->count / $summaryStats['total_orders']) * 100, 1) : 0;
                                        ?>
                                        <?= $percentage ?>%
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Countries and Recent Orders Row -->
    <div class="row mb-4">
        <!-- Top Countries by Revenue -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary"><?= __('Top Countries by Revenue') ?></h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th><?= __('Country') ?></th>
                                    <th><?= __('Orders') ?></th>
                                    <th><?= __('Revenue') ?></th>
                                    <th><?= __('Share') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($summaryStats['top_countries'] as $country): ?>
                                <tr>
                                    <td><?= h($country->country_name) ?></td>
                                    <td><?= number_format($country->order_count) ?></td>
                                    <td><?= $currencySymbol ?> <?= number_format($country->total_revenue, 2) ?></td>
                                    <td>
                                        <?php
                                        $share = $summaryStats['total_revenue'] > 0 ?
                                            round(($country->total_revenue / $summaryStats['total_revenue']) * 100, 1) : 0;
                                        ?>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar"
                                                 style="width: <?= $share ?>%"
                                                 aria-valuenow="<?= $share ?>"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                                <?= $share ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary"><?= __('Recent Orders') ?></h6>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button"
                                data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-download me-1"></i><?= __('Export') ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <?= $this->Html->link(
                                    '<i class="fas fa-file-csv me-2"></i>' . __('Revenue Report CSV'),
                                    ['action' => 'exportRevenue'] + $filters,
                                    ['class' => 'dropdown-item', 'escape' => false]
                                ) ?>
                            </li>
                            <li>
                                <?= $this->Html->link(
                                    '<i class="fas fa-file-csv me-2"></i>' . __('Sales Report CSV'),
                                    ['action' => 'exportSales'] + $filters,
                                    ['class' => 'dropdown-item', 'escape' => false]
                                ) ?>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th><?= __('Order #') ?></th>
                                    <th><?= __('Customer') ?></th>
                                    <th><?= __('Amount') ?></th>
                                    <th><?= __('Status') ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentReports as $order): ?>
                                <tr>
                                    <td>
                                        <strong>#<?= h($order->order_number) ?></strong><br>
                                        <small class="text-muted">
                                            <?= $order->order_date->format('M j, Y') ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?= h($order->customer_name) ?><br>
                                        <small class="text-muted"><?= h($order->country_name) ?></small>
                                    </td>
                                    <td>
                                        <strong><?= $currencySymbol ?> <?= number_format($order->total_amount, 2) ?></strong><br>
                                        <small class="text-muted"><?= h($order->order_type) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $this->element('status_color', ['status' => $order->status]) ?>">
                                            <?= h($order->status) ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <?= $this->Html->link(
                            __('View All Orders'),
                            ['action' => 'revenueDetails'],
                            ['class' => 'btn btn-sm btn-outline-primary']
                        ) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i><?= __('Quick Actions') ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <?= $this->Html->link(
                                '<i class="fas fa-chart-line fa-2x mb-2 d-block"></i>' .
                                '<strong>' . __('Revenue Details') . '</strong><br>' .
                                '<small class="text-muted">' . __('Detailed revenue analysis') . '</small>',
                                ['action' => 'revenueDetails'],
                                ['class' => 'btn btn-outline-primary btn-lg w-100 h-100 text-center', 'escape' => false]
                            ) ?>
                        </div>
                        <div class="col-md-3 mb-3">
                            <?= $this->Html->link(
                                '<i class="fas fa-shopping-cart fa-2x mb-2 d-block"></i>' .
                                '<strong>' . __('Sales Details') . '</strong><br>' .
                                '<small class="text-muted">' . __('Product-wise sales data') . '</small>',
                                ['action' => 'salesDetails'],
                                ['class' => 'btn btn-outline-info btn-lg w-100 h-100 text-center', 'escape' => false]
                            ) ?>
                        </div>
                        <div class="col-md-3 mb-3">
                            <?= $this->Html->link(
                                '<i class="fas fa-download fa-2x mb-2 d-block"></i>' .
                                '<strong>' . __('Export Revenue') . '</strong><br>' .
                                '<small class="text-muted">' . __('Download revenue CSV') . '</small>',
                                ['action' => 'exportRevenue'] + $filters,
                                ['class' => 'btn btn-outline-success btn-lg w-100 h-100 text-center', 'escape' => false]
                            ) ?>
                        </div>
                        <div class="col-md-3 mb-3">
                            <?= $this->Html->link(
                                '<i class="fas fa-download fa-2x mb-2 d-block"></i>' .
                                '<strong>' . __('Export Sales') . '</strong><br>' .
                                '<small class="text-muted">' . __('Download sales CSV') . '</small>',
                                ['action' => 'exportSales'] + $filters,
                                ['class' => 'btn btn-outline-warning btn-lg w-100 h-100 text-center', 'escape' => false]
                            ) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.badge-primary { background-color: #4e73df; }
.badge-secondary { background-color: #858796; }
.badge-success { background-color: #1cc88a; }
.badge-danger { background-color: #e74a3b; }
.badge-warning { background-color: #f6c23e; color: #000; }
.badge-info { background-color: #36b9cc; }
</style>
