<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Placed Successfully - <?= h($order_number) ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .logo {
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 150px;
            height: auto;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            color: #28a745;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .message {
            font-size: 16px;
            line-height: 1.6;
            color: #555;
            margin-bottom: 30px;
        }
        .order-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #28a745;
        }
        .order-details h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 20px;
            font-weight: 600;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 18px;
            color: #28a745;
        }
        .detail-label {
            font-weight: 500;
            color: #666;
        }
        .detail-value {
            font-weight: 600;
            color: #333;
        }
        .items-section {
            margin: 30px 0;
        }
        .items-section h3 {
            color: #333;
            font-size: 20px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .item:last-child {
            border-bottom: none;
        }
        .item-info {
            flex: 1;
        }
        .item-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        .item-details {
            font-size: 14px;
            color: #666;
        }
        .item-total {
            font-weight: 600;
            color: #28a745;
            font-size: 16px;
        }
        .payment-info {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
            text-align: center;
        }
        .payment-info h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .payment-info p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .delivery-info {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .delivery-info h3 {
            margin: 0 0 15px 0;
            color: #2d5a2d;
            font-size: 18px;
        }
        .delivery-info p {
            margin: 5px 0;
            color: #2d5a2d;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: transform 0.3s ease;
        }
        .cta-button:hover {
            transform: translateY(-2px);
            color: white;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .footer .company-name {
            font-weight: 600;
            color: #28a745;
            font-size: 16px;
        }
        .social-links {
            margin: 20px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #28a745;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            .content {
                padding: 20px;
            }
            .header {
                padding: 20px;
            }
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            .item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="https://ozone.com360degree.com/img/ozone/logo.png" alt="OZONEX Marketplace" width="150">
            </div>
            <h1><?= __('🎉 Order Placed Successfully!') ?></h1>
            <p><?= __('Your order has been received and is being processed') ?></p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                <?= __('Hello') ?> <?= h($customer_name) ?>,
            </div>

            <div class="message">
                <?= __('Thank you for your order! We\'re excited to confirm that your order has been successfully placed and is now being processed. You\'ll receive updates as your order progresses.') ?>
            </div>

            <!-- Order Details -->
            <div class="order-details">
                <h3><?= __('📋 Order Summary') ?></h3>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Number:') ?></span>
                    <span class="detail-value"><?= h($order_number) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Date:') ?></span>
                    <span class="detail-value"><?= h($order_date) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Order Status:') ?></span>
                    <span class="detail-value"><?= h($order_status) ?></span>
                </div>
                 <div class="detail-row">
                    <span class="detail-label"><?= __('Discount:') ?></span>
                    <span class="detail-value"><?= h($discount_amount) ?></span>
                </div>
                <div class="detail-row">
                    <span class="detail-label"><?= __('Delivery Charges:') ?></span>
                    <span class="detail-value"><?= h($delivery_charge) ?></span>
                </div>

                <?php if (!empty($applied_coupon)): ?>
                <div class="detail-row" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    <div style="display: flex; align-items: center;">
                        <span style="color: #28a745; margin-right: 8px; font-size: 16px;">🎫</span>
                        <div style="flex-grow: 1;">
                            <span class="detail-label" style="color: #28a745; font-weight: bold;">
                                <?= __('Coupon Applied:') ?> <?= h($applied_coupon['code']) ?>
                            </span>
                            <br>
                            <small style="color: #666;">
                                <?= __('You saved') ?> <?= h($applied_coupon['discount_amount']) ?>
                                <?php if ($applied_coupon['coupon_type'] === 'percentage'): ?>
                                    <?= __('with this discount coupon!') ?>
                                <?php else: ?>
                                    <?= __('with this fixed discount coupon!') ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="detail-row">
                    <span class="detail-label"><?= __('Total Amount:') ?></span>
                    <span class="detail-value"><?= h($total_amount_formatted) ?></span>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="payment-info">
                <h3><?= __('💳 Payment Method') ?></h3>
                <p><?= __('Cash on Delivery (COD) - Pay') ?> <strong><?= h($total_amount_formatted) ?></strong> <?= __('when your order arrives') ?></p>
            </div>

            <!-- Order Items -->
            <div class="items-section">
                <h3><?= __('📦 Your Items') ?></h3>
                <?php foreach ($order_items as $item): ?>
                <div class="item">
                    <div class="item-info">
                        <div class="item-name"><?= h($item['product_name']) ?></div>
                        <div class="item-details">
                            <?= __('Quantity:') ?> <?= h($item['quantity']) ?> × <?= h($item['price_formatted']) ?>
                        </div>
                    </div>
                    <div class="item-total"><?= h($item['total_formatted']) ?></div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Delivery Information -->
            <div class="delivery-info">
                <h3><?= __('🚚 Delivery Information') ?></h3>
                <p><strong><?= __('Delivery Address:') ?></strong></p>
                <p><?= h($delivery_address) ?></p>
                <?php if (!empty($order_notes)): ?>
                <p><strong><?= __('Order Notes:') ?></strong> <?= h($order_notes) ?></p>
                <?php endif; ?>
                <!-- <p><strong><?= __('Delivery Mode:') ?></strong> <?= h($delivery_mode) ?></p> -->
            </div>

            <!-- Call to Action -->
            <!-- <div class="cta-section">
                <a href="#" class="cta-button"><?= __('Track Your Order') ?></a>
            </div> -->

            <div class="message">
                <?= __('We\'ll keep you updated on your order status via email and SMS. If you have any questions, our customer support team is here to help!') ?>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="company-name"><?= __('OZONEX') ?></p>
            <p><?= __('Thank you for choosing us for your shopping needs!') ?></p>
            <div class="social-links">
                <a href="#"><?= __('Contact Support') ?></a> |
                <a href="#"><?= __('Track Order') ?></a> |
                <a href="#"><?= __('Return Policy') ?></a>
            </div>
            <p><?= __('This email was sent to') ?> <?= h($customer_email) ?></p>
            <p><?= __('© 2025 OZONEX Marketplace. All rights reserved.') ?></p>
        </div>
    </div>
</body>
</html>
