<?php

declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Datasource\EntityInterface;

/**
 * ProductImages Model
 *
 * @property \App\Model\Table\ProductsTable&\Cake\ORM\Association\BelongsTo $Products
 *
 * @method \App\Model\Entity\ProductImage newEmptyEntity()
 * @method \App\Model\Entity\ProductImage newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\ProductImage> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\ProductImage get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\ProductImage findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\ProductImage patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\ProductImage> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\ProductImage|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\ProductImage saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\ProductImage>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ProductImage>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ProductImage>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ProductImage> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ProductImage>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ProductImage>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\ProductImage>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\ProductImage> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class ProductImagesTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('product_images');
        $this->setDisplayField('image');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
            'joinType' => 'INNER',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('product_id')
            ->notEmptyString('product_id');

        $validator
            ->scalar('media_type')
            ->notEmptyString('media_type');

        $validator
            ->scalar('image')
            ->maxLength('image', 255)
            ->allowEmptyFile('image');

        $validator
            ->notEmptyString('image_default');

        $validator
            ->scalar('video')
            ->maxLength('video', 255)
            ->allowEmptyString('video');

        $validator
            ->scalar('status')
            ->notEmptyString('status');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['product_id'], 'Products'), ['errorField' => 'product_id']);

        return $rules;
    }

    public function delete(EntityInterface $productimage, array $options = []): bool
    {
        $productimage->status = 'D';

        if ($this->save($productimage)) {
            if ($productimage->image_default == 1) {
                $exists = $this->find()
                    ->where([
                        'product_id' => $productimage->product_id, 
                        'media_type' => 'Image',
                        'image_default' => 0, 
                        'status' => 'A'
                    ])
                    ->first();

                if ($exists) {
                    $exists->image_default = 1;
                    $this->save($exists);
                }
            }
            return true;
        }
        return false;
    }


    public function makeDefault(EntityInterface $productimage, array $options = []): bool
    {
        $this->getConnection()->transactional(function () use ($productimage) {
            $productimage->image_default = '1';
            $productId = $productimage->product_id;
            $otherImages = $this->find('all', [
                'conditions' => ['product_id' => $productId, 'id !=' => $productimage->id]
            ]);

            foreach ($otherImages as $image) {
                $image->image_default = '0';
                $this->save($image);
            }

            return $this->save($productimage);
        });

        return true;
    }

    //M
    public function getDefaultProductImage($productId)
    {
        $query = $this->find()
            ->select(['image'])
            ->where([
                'ProductImages.product_id' => $productId,
                'ProductImages.image_default' => 1,
                'ProductImages.media_type' => 'image'
            ])
            ->first();

        return $query ? $query->image : null;
    }
}
