<?php
/**
 * Payment Details Report
 */
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= __('Payment Details Report') ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <?= $this->Html->link(__('Dashboard'), ['controller' => 'Dashboards', 'action' => 'adminDashboard']) ?>
                    </li>
                    <li class="breadcrumb-item">
                        <?= $this->Html->link(__('Payment Reports'), ['action' => 'paymentReports']) ?>
                    </li>
                    <li class="breadcrumb-item active"><?= __('Payment Details') ?></li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <?= $this->Html->link(
                '<i class="fas fa-arrow-left me-2"></i>' . __('Back to Reports'),
                ['action' => 'paymentReports'],
                ['class' => 'btn btn-secondary', 'escape' => false]
            ) ?>
            <?= $this->Html->link(
                '<i class="fas fa-download me-2"></i>' . __('Export CSV'),
                ['action' => 'exportPayments'] + $filters,
                ['class' => 'btn btn-success', 'escape' => false]
            ) ?>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i><?= __('Filter Payment Data') ?>
            </h6>
        </div>
        <div class="card-body">
            <?= $this->Form->create(null, [
                'type' => 'get',
                'class' => 'row g-3',
                'id' => 'paymentFiltersForm'
            ]) ?>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Date From') ?></label>
                    <?= $this->Form->control('date_from', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'value' => $filters['date_from'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Date To') ?></label>
                    <?= $this->Form->control('date_to', [
                        'type' => 'date',
                        'class' => 'form-control',
                        'value' => $filters['date_to'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Country') ?></label>
                    <?= $this->Form->control('country_id', [
                        'type' => 'select',
                        'options' => ['' => __('All Countries')] + array_column($countries, 'name', 'id'),
                        'class' => 'form-select',
                        'value' => $filters['country_id'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Status') ?></label>
                    <?= $this->Form->control('status', [
                        'type' => 'select',
                        'options' => [
                            '' => __('All Statuses'),
                            'Pending' => __('Pending'),
                            'Completed' => __('Completed'),
                            'Failed' => __('Failed'),
                            'Refunded' => __('Refunded')
                        ],
                        'class' => 'form-select',
                        'value' => $filters['status'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Payment Method') ?></label>
                    <?= $this->Form->control('payment_method', [
                        'type' => 'select',
                        'options' => [
                            '' => __('All Methods'),
                            'COD' => __('Cash on Delivery'),
                            'Credit Card' => __('Credit Card'),
                            'Debit Card' => __('Debit Card'),
                            'Bank Transfer' => __('Bank Transfer'),
                            'PayPal' => __('PayPal'),
                            'MoMo' => __('Mobile Money')
                        ],
                        'class' => 'form-select',
                        'value' => $filters['payment_method'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-md-2">
                    <label class="form-label"><?= __('Per Page') ?></label>
                    <?= $this->Form->control('limit', [
                        'type' => 'select',
                        'options' => [
                            '25' => '25',
                            '50' => '50',
                            '100' => '100',
                            '250' => '250'
                        ],
                        'class' => 'form-select',
                        'value' => $filters['limit'],
                        'label' => false
                    ]) ?>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i><?= __('Apply Filters') ?>
                    </button>
                    <?= $this->Html->link(
                        '<i class="fas fa-undo me-2"></i>' . __('Reset'),
                        ['action' => 'paymentDetails'],
                        ['class' => 'btn btn-secondary', 'escape' => false]
                    ) ?>
                </div>
            <?= $this->Form->end() ?>
        </div>
    </div>

    <!-- Payment Data Table -->
    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-table me-2"></i><?= __('Payment Details') ?>
            </h6>
            <div class="text-muted">
                <?= __('Showing {0} items', count($paymentData)) ?>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="paymentTable">
                    <thead class="table-dark">
                        <tr>
                            <th><?= __('Transaction #') ?></th>
                            <th><?= __('Date') ?></th>
                            <th><?= __('Order #') ?></th>
                            <th><?= __('Customer') ?></th>
                            <th><?= __('Country') ?></th>
                            <th><?= __('Amount') ?></th>
                            <th><?= __('Method') ?></th>
                            <th><?= __('Status') ?></th>
                            <th><?= __('Transaction ID') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($paymentData)): ?>
                        <tr>
                            <td colspan="9" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-3x mb-3 d-block"></i>
                                <?= __('No payment data found for the selected criteria') ?>
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php 
                            $totalPayments = 0;
                            $totalTransactions = count($paymentData);
                            ?>
                            <?php foreach ($paymentData as $payment): ?>
                                <?php $totalPayments += $payment->amount; ?>
                            <tr>
                                <td>
                                    <strong><?= h($payment->transaction_number) ?></strong><br>
                                    <small class="text-muted">Invoice: <?= h($payment->invoice_number) ?></small>
                                </td>
                                <td>
                                    <?php 
                                    $transactionDate = is_string($payment->transaction_date) ? new \DateTime($payment->transaction_date) : $payment->transaction_date;
                                    ?>
                                    <?= $transactionDate->format('M j, Y') ?><br>
                                    <small class="text-muted"><?= $transactionDate->format('H:i') ?></small>
                                </td>
                                <td>
                                    <strong><?= h($payment->order_number) ?></strong>
                                </td>
                                <td>
                                    <strong><?= h($payment->customer_name) ?></strong><br>
                                    <small class="text-muted"><?= h($payment->customer_email) ?></small>
                                </td>
                                <td><?= h($payment->country_name) ?></td>
                                <td class="text-end">
                                    <strong class="text-success">
                                        <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                            <?= $currencyInfo['currency_symbol'] ?> <?= number_format($payment->amount, 2) ?>
                                        <?php else: ?>
                                            <?= number_format($payment->amount, 2) ?>
                                        <?php endif; ?>
                                    </strong>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $this->element('payment_method_color', ['method' => $payment->payment_method]) ?>">
                                        <?= h($payment->payment_method) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $this->element('payment_status_color', ['status' => $payment->payment_status]) ?>">
                                        <?= h($payment->payment_status) ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted"><?= h($payment->transactionID) ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                    <?php if (!empty($paymentData)): ?>
                    <tfoot class="table-dark">
                        <tr>
                            <th colspan="5" class="text-end"><?= __('Totals:') ?></th>
                            <th class="text-end">
                                <strong class="text-success">
                                    <?php if (!empty($currencyInfo['currency_symbol'])): ?>
                                        <?= $currencyInfo['currency_symbol'] ?> <?= number_format($totalPayments, 2) ?>
                                    <?php else: ?>
                                        <?= number_format($totalPayments, 2) ?>
                                    <?php endif; ?>
                                </strong>
                            </th>
                            <th colspan="3">
                                <small class="text-muted">
                                    <?= __('Transactions: {0}', number_format($totalTransactions)) ?>
                                    <?php if ($totalTransactions > 0): ?>
                                        | <?= __('Avg: {0}', number_format($totalPayments / $totalTransactions, 2)) ?>
                                    <?php endif; ?>
                                </small>
                            </th>
                        </tr>
                    </tfoot>
                    <?php endif; ?>
                </table>
            </div>

            <!-- Pagination -->
            <?php if (!empty($paymentData) && count($paymentData) >= $filters['limit']): ?>
            <nav aria-label="Payment pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($filters['page'] > 1): ?>
                    <li class="page-item">
                        <?= $this->Html->link(
                            __('Previous'),
                            ['action' => 'paymentDetails'] + array_merge($filters, ['page' => $filters['page'] - 1]),
                            ['class' => 'page-link']
                        ) ?>
                    </li>
                    <?php endif; ?>
                    
                    <li class="page-item active">
                        <span class="page-link"><?= $filters['page'] ?></span>
                    </li>
                    
                    <?php if (count($paymentData) >= $filters['limit']): ?>
                    <li class="page-item">
                        <?= $this->Html->link(
                            __('Next'),
                            ['action' => 'paymentDetails'] + array_merge($filters, ['page' => $filters['page'] + 1]),
                            ['class' => 'page-link']
                        ) ?>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.badge-primary { background-color: #4e73df; }
.badge-secondary { background-color: #858796; }
.badge-success { background-color: #1cc88a; }
.badge-danger { background-color: #e74a3b; }
.badge-warning { background-color: #f6c23e; color: #000; }
.badge-info { background-color: #36b9cc; }

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.075);
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const filterForm = document.getElementById('paymentFiltersForm');
    const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');
    
    filterInputs.forEach(input => {
        if (input.name !== 'limit') { // Don't auto-submit on limit change
            input.addEventListener('change', function() {
                // Reset to page 1 when filters change
                const pageInput = filterForm.querySelector('input[name="page"]');
                if (pageInput) {
                    pageInput.value = 1;
                }
                filterForm.submit();
            });
        }
    });
});
</script>
