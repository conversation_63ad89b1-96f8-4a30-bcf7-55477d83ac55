<!DOCTYPE html>
<html lang="en">
<?php
        use Cake\Core\Configure;
        // Get session instance
        $session = $this->request->getSession();
        $currentLang = $session->read('siteSettings.language') ? strtolower($session->read('siteSettings.language')) : 'english';
        $country = $session->read('siteSettings.country') ? strtolower($session->read('siteSettings.country')) : 'Qatar';
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <?= $this->Html->meta('csrfToken', $this->request->getAttribute('csrfToken')); ?>
    <title><?= $this->fetch('title') ? $this->fetch('title') . ' - ' . $session->read('siteSettings.site_title') : $session->read('siteSettings.site_title'); ?></title>

    <!-- Global JavaScript Variables -->
    <script>
        window.csrfToken = '<?= $this->request->getAttribute('csrfToken') ?>';
    </script>

    <!-- Meta Tags -->
    <?php if ($this->fetch('meta_description')): ?>
        <meta name="description" content="<?= h($this->fetch('meta_description')) ?>">
    <?php endif; ?>

    <?php if ($this->fetch('meta_keywords')): ?>
        <meta name="keywords" content="<?= h($this->fetch('meta_keywords')) ?>">
    <?php endif; ?>

    <?php if (isset($meta_title) && !empty($meta_title)): ?>
        <meta property="og:title" content="<?= h($meta_title) ?>">
    <?php endif; ?>

    <?php if (isset($meta_description) && !empty($meta_description)): ?>
        <meta property="og:description" content="<?= h($meta_description) ?>">
    <?php endif; ?>

    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.min.css" />
    <link rel="stylesheet" href="../../bundles/bootstrap/css/bootstrap.css" />
    <link rel="stylesheet" href="../../css/ozone.css" />
    <link rel="stylesheet" href="../../css/responsive.css" />
    <!-- For SVG format -->
    <link rel="icon" type="image/svg+xml" href="../../img/ozone/Ozonex-svg.svg">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Owl Stylesheets -->
    <link rel="stylesheet" href="../../css/owl.carousel.min.css">
    <link rel="stylesheet" href="../../css/owl.theme.default.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />

    <link href="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flag-icons@6.7.0/css/flag-icons.min.css">
    <script src="https://ipapi.co/json/"></script>


    <!-- jQuery and jQuery Validation Plugin -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        .footer-link {
    color: #003300; /* Adjust to your brand's dark green */
    text-decoration: none;
    font-size: 14px;
}

.footer-link:hover {
    text-decoration: underline;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
}

.footer-links a {
    margin-right: 15px;
    margin-bottom: 5px;
}

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        #toast-message-container .alert {
            transition: all 0.3s ease;
        }
    </style>
    <script src="../../js/jquery.min.js"></script>
    <script src="../../js/owl.carousel.js"></script>

<style>
span.show-password-icon {
    position: absolute;
    top: 54%;
    right: 8%;
    color:black;
}
span.show-password-icon-register {
    position: absolute;
    top: 54%;
    right: 8%;
    color:black;
}
</style>
</head>
</head>

<body class="">
    <!-- Navigation -->
    <div class="md-language d-lg-none">
        <div class="container">
            <div class="d-flex justify-content-end align-items-center navbar-icons">

                    <div class="dropdown">
                        <button id="dropdownregion" class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown"
                          aria-expanded="false">
                          <span class="fi fi-qa flag md:my-4"></span> <?= ucwords($country) ?>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" onclick="selectDropdownItem(this, '<?= $currentLang ?>', 'Qatar')">
                            <span class="fi fi-qa flag " alt="Qatar" ></span> <span class="dropdown-country-name">Qatar</span>
                              <!-- <span>Qatar</span> -->
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" onclick="selectDropdownItem(this, '<?= $currentLang ?>', 'Saudi Arabia')">
                            <span class="fi fi-sa flag " alt="Saudi Arabia" ></span> <span class="dropdown-country-name">المملكة العربية السعودية</span>
                              <!-- <span>Saudi Arabia</span> -->
                            </a>
                          </li>
                        </ul>
                      </div>
                      <span class="right-bord"></span>


                    <div class="dropdown">


                        <button id="dropdownButton" class="btn " type="button" onclick="selectDropdownItem(this, '<?= $currentLang === 'arabic' ? 'Eng' : 'Arb' ?>', '<?= $country ?>')">
                        <?= strtolower($currentLang) === 'english' ? 'عربي' : 'English'; ?>
                        </button>


                    </div>


                </div>
        </div>
    </div>
    <nav class="navbar-head-main navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="../../img/ozone/logo.png" class="img-fluid logo-head">
            </a>

            <?php
                $session = $this->request->getSession();
                $authUser = $session->read('Auth.User');
            ?>
            <?php if($authUser): ?>
                <a href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount']) ?>" class="btn d-md-none"><img src="../../img/ozone/Account.png" class="img-fluid" /></a>
            <?php else: ?>
                <button class="btn d-md-none" data-bs-target="#loginmodal" data-bs-toggle="modal">
                            <img src="../../img/ozone/Account.png" class="img-fluid" />
                </button>
            <?php endif; ?>

            <span class="right-bord d-md-none"></span>

            <a href="/cart" class="text-dark mx-3 position-relative d-md-none">
                <img src="../../img/ozone/Shopping.png" class="img-fluid" />
                <?= $this->cell('Cart::display', ['mobile']) ?>
            </a>


            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <?php
                    // Use CakePHP's URL helper for clean URLs
                    $currentUrl = $this->request->getRequestTarget();
                ?>
                <ul class="navbar-nav m-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= $currentUrl === '/' ? 'active' : '' ?>" href="<?= $this->Url->build('/') ?>"><?= __('Home') ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($currentUrl, '/product-list') === 0 ? 'active' : '' ?>" href="<?= $this->Url->build('/product-list') ?>"><?= __('Shop All') ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($currentUrl, '/about-us') === 0 ? 'active' : '' ?>" href="<?= $this->Url->build('/about-us') ?>"><?= __('About Us') ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($currentUrl, '/ozonex') === 0 ? 'active' : '' ?>" href="<?= $this->Url->build('/ozonex') ?>"><?= __('OzoneX') ?></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($currentUrl, '/contact-us') === 0 ? 'active' : '' ?>" href="<?= $this->Url->build('/contact-us') ?>"><?= __('Contact Us') ?></a>
                    </li>
                </ul>
                <div class="d-flex align-items-center navbar-icons">


                    <div class="dropdown">
                        <button id="dropdownregion" class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown"
                          aria-expanded="false">
                        <?php
                            // Show country name in Arabic if Saudi Arabia
                            if (strtolower(trim($currentLang)) == 'arabic') {
                                if (strtolower(trim($country)) === 'saudi arabia') {
                                    $countryName = 'المملكة العربية السعودية';
                                } else {
                                    $countryName = 'قطر';
                                }
                            } else {
                                $countryName = ucwords($country);
                            }
                            if (strtolower(trim($countryName)) === 'saudi arabia' || strtolower(trim($countryName)) === 'المملكة العربية السعودية') {
                                $flagClass = 'fi fi-sa';
                            } else {
                                $flagClass = 'fi fi-qa';
                            }
                        ?>
                        <span class="<?= $flagClass ?> flag me-2 my-4"></span> <?= $countryName ?>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" onclick="selectDropdownItem(this, '<?= $currentLang ?>', 'Qatar')">
                            <span class="fi fi-qa flag me-2" alt="Qatar" ></span> <span class="dropdown-country-name">
                                <?= strtolower($currentLang) === 'english' ? 'Qatar' : 'قطر'; ?>
                            </span>
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" onclick="selectDropdownItem(this, '<?= $currentLang ?>', 'Saudi Arabia')">
                            <span class="fi fi-sa flag me-2" alt="Saudi Arabia" ></span> <span class="dropdown-country-name">
                                <?= strtolower($currentLang) === 'english' ? 'Saudi Arabia' : 'المملكة العربية السعودية'; ?>
                            </span>
                            </a>
                          </li>
                        </ul>
                      </div>
                      <span class="right-bord"></span>

                    <div class="dropdown">


                        <button id="dropdownButton" class="btn " type="button" onclick="selectDropdownItem(this, '<?= $currentLang === 'arabic' ? 'Eng' : 'Arb' ?>', '<?= $country ?>')">
                        <?= strtolower($currentLang) === 'english' ? 'عربي' : 'English'; ?>
                        </button>


                    </div>
                    <span class="right-bord"></span>

                    <?php if($authUser): ?>
                        <a href="<?= $this->Url->build(['controller' => 'Account', 'action' => 'myAccount']) ?>" class="btn d-none d-lg-block"><img src="../../img/ozone/Account.png" class="img-fluid" /></a>
                    <?php else: ?>
                        <button class="btn  d-none d-lg-block" data-bs-target="#loginmodal" data-bs-toggle="modal">
                            <img src="../../img/ozone/Account.png" class="img-fluid" />
                        </button>
                    <?php endif; ?>

                     <span class="right-bord "></span>
                    <a href="/whishlist" class="text-dark  mx-2 ">
                        <img src="../../img/ozone/heart.png" class="img-fluid" />
                    </a>

                    <span class="right-bord  d-none d-lg-block"></span>

                    <a href="/cart" class="text-dark mx-2 position-relative d-none d-lg-block">

                        <img src="../../img/ozone/Shopping.png" class="img-fluid" />
                                 <?= $this->cell('Cart::display', ['desktop']) ?>
                    </a>
                    <span class="right-bord  d-none d-lg-block"></span>

                    <span class="right-bord  d-none d-lg-block"></span>
                    <a href="#" class="text-dark  mx-2 d-none d-lg-block">
                        <img src="../../img/ozone/Search.png" class="img-fluid" />
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        <?= $this->Flash->render() ?>
    </div>

    <!-- Toast Message Container -->
    <div id="toast-message-container" style="position: fixed; top: 80px; right: 20px; z-index: 9999; max-width: 350px;"></div>


    <?= $this->fetch('content') ?>


    <!-- Footer -->
    <footer class="footer-banner ">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 md-mb-4">
                    <a class="py-5" href="#">
                        <img src="../../img/ozone/logo.png" class="img-fluid">
                    </a>
                    <p class="my-5">
                        Ozone Cool Qatar Authorized Distributor for Fujitsu General Air Conditioner for Residential, Commercial and Industrial Applications
                    </p>
                    <div class="social-icons my-3">
                        <a href="<?= h($this->Url->build($session->read('siteSettings.facebook_url'), ['fullBase' => true])) ?>"><i class="fab fa-facebook-f fs-3"></i></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.twitter_url'), ['fullBase' => true])) ?>"><i class="fab fa-twitter fs-3"></i></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.instagram_url'), ['fullBase' => true])) ?>"><i class="fab fa-instagram fs-3"></i></a>
                        <a href="<?= h($this->Url->build($session->read('siteSettings.linkedin_url'), ['fullBase' => true])) ?>"><i class="fab fa-linkedin-in fs-3"></i></a>
                    </div>
                </div>
                <div class="col-lg-9">
                    <div class="row footer-content">
                        <div class="col-lg-3 mb-md-4  d-none d-lg-block">
                            <h5 class="mt-3"><?= __('Website Link') ?></h5>
                            <ul class="mt-5">
                                <li><a href="#"><?= __('Home') ?></a></li>
                                <li><a href="#"><?= __('Shop All') ?></a></li>
                                <li><a href="about-us"><?= __('About Us') ?></a></li>
                                <li><a href="#"><?= __('OzoneX') ?></a></li>
                                <li><a href="contact-us"><?= __('Contact Us') ?></a></li>
                            </ul>
                        </div>
                        <div class="col-lg-3 mb-md-4  d-none d-lg-block">
                            <h5 class="mt-3"><?= __('Working Hours') ?></h5>
                            <ul class="mt-5">
                                <li class="week"><?= __('Sat - Thu:') ?></li>
                                <li><a href="#"><?= $session->read('siteSettings.business_open_time') ?> - <?= $session->read('siteSettings.business_close_time') ?></a></li>

                            </ul>
                        </div>
                        <div class="col-lg-3 mb-md-4">
                            <h5 class="mt-3"><?= __('Contact Us') ?></h5>
                            <ul class="mt-md-5 icons">
                                <li><i class="fas fa-envelope me-2"></i><span><?= $session->read('siteSettings.support_email') ?></span></li>
                                <li><i class="fas fa-phone-alt me-2"></i><span><?= $session->read('siteSettings.contact_no') ?></span>
                                </li>
                                <li><i class="fas fa-map-marker-alt me-2"></i><span>
                                    <?= $session->read('siteSettings.address_line1') ?>, <?= $session->read('siteSettings.address_line2') ?>, <?= $session->read('siteSettings.city') ?>, <?= $session->read('siteSettings.state') ?> <?= $session->read('siteSettings.zipcode') ?>
                                </span></li>
                            </ul>
                        </div>
                        <div class="col-lg-3 mb-md-4">
                        <div class="col-lg-3 mb-md-4">
                            <h5 class="mt-3"><?= __('Language') ?></h5>
                            <ul class="mt-md-5 icons language-footer">
                                <li class="<?= strtolower($currentLang) === 'english' ? 'active' : '' ?>">
                                    <span style="cursor: pointer;" onclick="selectDropdownItem(this, '<?= $currentLang === 'arabic' ? 'Eng' : 'Arb' ?>', '<?= $country ?>')">
                                        <span><?= __('English') ?></span>
                                    </span>
                                </li>
                                <li class="<?= strtolower($currentLang) === 'arabic' ? 'active' : '' ?>">
                                    <span style="cursor: pointer;" href="" onclick="selectDropdownItem(this, '<?= $currentLang === 'arabic' ? 'Eng' : 'Arb' ?>', '<?= $country ?>')">
                                        <span><?= __('عربي') ?></span>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-lg-3 mb-md-4   d-lg-none">
                            <h5 class="mt-3"><?= __('Working Hours') ?></h5>
                            <ul class="mt-md-5">
                                <li class="week"><?= __('Sat - Thu:') ?></li>
                                <li><a href="#"><?= $session->read('siteSettings.business_open_time') ?> - <?= $session->read('siteSettings.business_close_time') ?></a></li>

                            </ul>
                        </div>
                    </div>
                    <!-- <hr class="my-md-4 bg-secondary">
                    <div class="row">
                        <div class="col-md-12 text-end w-100 copy-rights">
                            <p class="mb-0 text-right">
                                <?= __('Copyright') ?> © <?= date('Y') ?> <?= $session->read('siteSettings.site_title') ?> Cool
                                &nbsp;|&nbsp;
                                <a href="/privacy-policy" target="_blank"><?= __('Privacy Policy') ?></a>
                                &nbsp;|&nbsp;
                                <a href="/terms-and-conditions" target="_blank"><?= __('Terms & Conditions') ?></a>
                            </p>
                        </div>

                    </div> -->
                    <hr class="my-md-4 bg-secondary">
                    <div class="row">
                       <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-2">
                        <div class="footer-links mb-2 mb-md-0">
                            <a href="/privacy-policies" class="footer-link me-3"><?= __('Privacy Policy') ?></a>
                            <a href="/terms-condtions" class="footer-link"><?= __('Terms & Conditions For Purchase') ?></a>
                            <a href="/refund-policies" class="footer-link"><?= __('Refund Policies') ?></a>
                        </div>
                        <div class="text-muted small">
                            <?= __('Copyright') ?> © <?= date('Y') ?> <?= $session->read('siteSettings.site_title') ?>
                        </div>
                    </div>

                    </div>

                </div>
            </div>
            

        </div>
    </footer>

    <!--Login Modal -->
    <div class="modal fade " id="loginmodal" aria-hidden="true" aria-labelledby="loginmodalLabel"
        tabindex="-1">
        <div class="modal-dialog modal-fullscreen modal-dialog-right">
            <div class="modal-content login">
                <div class="login-container">
                    <div class="position-relative">
                        <div class="container">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                                    src="../../img/ozone/close-circle.png" width="40" height="40" /></button>
                            <div class="form-login my-5">
                                <div class="logo">
                                    <img src="../../img/ozone/logo.png" class="img-fluid">
                                    <span style="font-weight: 600;">Log in</span>
                                </div>
                                <!-- Social Icons -->
                                <div class="social-icons my-5">
                                    <a href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'googleLogin']) ?>"><button><img src="https://img.icons8.com/color/24/google-logo.png" /><span> Sign in
                                            with
                                            Google</span></button></a>
                                    <button><img src="https://img.icons8.com/ios-filled/24/facebook-new.png" /><span>
                                            Sign
                                            in with Facebook</span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/mac-os.png" /><span> Sign in
                                            with
                                            Apple</span></button>
                                </div>

                                <!-- Divider -->
                                <div class="divider">OR</div>

                                <!-- Email Field -->
                                <?= $this->Form->create(null, ['url' => ['controller' => 'Customer', 'action' => 'login'],'type' => 'post']) ?>
                                <div class="input-group email mb-4">
                                    <label>Enter your email address</label>
                                    <input type="hidden" id="csrf-token-login" value="<?= h($this->request->getAttribute('csrfToken')) ?>">
                                    <input type="email" placeholder="Username or email address" name="email" class="text" required="" />
                                </div>
                                <div class="text-center mb-3" id="emailError" style="color:orange;display:none"></div>
                                <!-- Password Field -->
                                <div class="input-group">
                                    <label>Enter your Password</label>
                                    <input type="password" name="password" placeholder="Password" class="text password-field" required=""/>
                                    <span class="show-password-icon" onclick="togglePasswordVisibility()"><i class="fa fa-eye"></i></span>
                                </div>

                                <div class="forgot-password text-center">
                                    <a href="<?= $this->Url->build(['controller' => 'Customer', 'action' => 'forgotPassword','type' => 'post']) ?>" class="forgot_pwd"><span>Forgot password?</span></a>
                                </div>

                                <!-- Login Button -->
                                <input type="submit" class="btn-glow btn-login my-5" value="Log In" />
                                </form>
                                <!-- Register Button -->
                                <button class="btn-glow btn-register" id="btn-register" data-bs-target="#loginmodal2"
                                    data-bs-toggle="modal" data-bs-dismiss="modal" aria-label="Close">Register here
                                    !</button>

                                <!-- Footer -->
                                <div class="footer-text">
                                    If you don't have an account, you can register here.
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Register Modal -->
    <div class="modal fade" id="loginmodal2" aria-hidden="true" aria-labelledby="loginmodalLabel2"
        tabindex="-1">
        <div class="modal-dialog modal-fullscreen modal-dialog-right">
            <div class="modal-content login">

                <div class="login-container">
                    <div class="position-relative">
                        <div class="container">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                                    src="../../img/ozone/close-circle.png" width="40" height="40" /></button>
                            <div class="form-login my-5">
                                <div class="logo">
                                    <img src="../../img/ozone/logo.png" class="img-fluid">
                                    <span style="font-weight: 600;">Register</span>
                                </div>
                                <!-- Social Icons -->
                                <div class="social-icons my-5">
                                    <button><img src="https://img.icons8.com/color/24/google-logo.png" /><span> Sign in
                                            with
                                            Google</span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/facebook-new.png" /><span>
                                            Sign
                                            in with Facebook</span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/mac-os.png" /><span> Sign in
                                            with
                                            Apple</span></button>
                                </div>

                                <!-- Divider -->
                                <div class="divider">OR</div>
                                <?= $this->Form->create(null, ['url' => ['controller' => 'Customer', 'action' => 'signup'],'type' => 'post']) ?>
                                <label>Enter your email address</label>
                                <div class="d-flex my-4 ">
                                    <!-- Email Field -->
                                    <div class="input-group email ">
                                        <input type="email" id="email" name="email" placeholder="Username or email address" required/>
                                    </div>
                                    <button class="verify-btn ms-2" id="send_code_btn">
                                        <span class="btn-text">Send Code</span>
                                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                    </button>
                                </div>
                                <div class="text-center mb-3" id="emailError" style="color:orange;display:none"></div>
                                <div class="d-flex align-items-center mb-3">
                                    <!-- Email Field -->
                                    <div class="input-group email ">
                                        <input type="text" id="verification_code" placeholder="Verification Code" />
                                        <span class="timer-inside" id="countdown">05:00</span>
                                    </div>
                                    <button class="verify-btn" id="verify-btn">Verify Email</button>
                                </div>
                                <div class="error-text mb-5"></div>
                                <!-- Email -->

                                <div id="show_registration_form" style="display:none;">
                                    <!-- Full Name -->
                                    <div class="mb-4 input-group ">
                                        <label class="form-label">Full Name</label>
                                        <input type="text" name="name" id="name" class="form-control rounded-pill"
                                            placeholder="Enter your full Name" required/>
                                    </div>
                                    <!-- Password -->
                                    <div class="mb-4 input-group ">
                                        <label class="form-label">Password</label>
                                        <input type="password" name="password" id="password" placeholder="Enter Password" class="text rounded-pill" required=""/>
                                        <span class="show-password-icon" onclick="togglePasswordVisibilityRegister()"><i class="fa fa-eye"></i></span>
                                    </div>
                                    <div class="text-center mb-3" id="passwordError" style="color:orange;display:none"></div>
                                    <!-- Confirm Password -->
                                    <div class="mb-4 input-group ">
                                        <label class="form-label">Confirm Password</label>
                                        <input type="password" name="confirm_password" id="confirm_password" placeholder="Confirm Password" class="text rounded-pill" required=""/>
                                        <span class="show-password-icon" onclick="toggleConfirmPasswordVisibilityRegister()"><i class="fa fa-eye"></i></span>
                                    </div>
                                    <div class="text-center mb-3 password_error" style="color:orange;display:none">Password do not match!</div>
                                    <!-- Terms -->
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="terms" required/>
                                        <label class="form-check-label terms-condition" for="terms">
                                            By signing up you agree to our Terms &
                                                Conditions</span>
                                        </label>
                                    </div>
                                    <!-- Register Button -->
                                    <input type="submit" class="btn-glow btn-login my-5">
                                </div>
                            </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="removeCouponModal" tabindex="-1" aria-labelledby="removeCouponLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="removeCouponLabel">Remove Coupon</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Are you sure you want to remove this coupon?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmRemoveCoupon">Yes, Remove</button>
      </div>
    </div>
  </div>
</div>


<script>
    function togglePasswordVisibility() {
        const passwordField = document.querySelector('.password-field');
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
    }

    function togglePasswordVisibilityRegister() {
        const password = document.getElementById('password');
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
    }

    function toggleConfirmPasswordVisibilityRegister() {
        const confirmPassword = document.getElementById('confirm_password');
        const type = confirmPassword.getAttribute('type') === 'password' ? 'text' : 'password';
        confirmPassword.setAttribute('type', type);
    }

    function setLanguageDirection(lang) {
        const body = document.body;
        if (lang === 'arabic') {
            body.classList.add('rtl');
            body.classList.remove('ltr');
            document.documentElement.setAttribute('dir', 'rtl');
            document.documentElement.setAttribute('lang', 'ar');
        } else {
            body.classList.remove('rtl');
            body.classList.add('ltr');
            document.documentElement.setAttribute('dir', 'ltr');
            document.documentElement.setAttribute('lang', 'en');
        }
    }

    // Call this on page load using PHP language
    document.addEventListener('DOMContentLoaded', function () {
        const currentLang = '<?= strtolower($currentLang) ?>';
        setLanguageDirection(currentLang);

        // Initialize cart count
        if (window.CartUpdater && typeof window.CartUpdater.refreshCartCount === 'function') {
            window.CartUpdater.refreshCartCount();
        }
    });


    function selectDropdownItem() {
        const langBtn = document.getElementById('languageToggleBtn');
        const currentText = langBtn.textContent.trim();
        const newLang = currentText === 'عربي' ? 'arabic' : 'english';

        // Update direction
        setLanguageDirection(newLang);
        // Update direction
        setLanguageDirection(newLang);

        // Toggle button text
        langBtn.textContent = newLang === 'arabic' ? 'English' : 'عربي';
    }

    $(document).ready(function() {
        let countdownInterval;
        function startCountdown(durationInSeconds) {
            let timer = durationInSeconds;
            const countdownEl = $('#countdown');

            clearInterval(countdownInterval);

            countdownInterval = setInterval(function () {
                const minutes = String(Math.floor(timer / 60)).padStart(2, '0');
                const seconds = String(timer % 60).padStart(2, '0');
                countdownEl.text(`${minutes}:${seconds}`);

                if (--timer < 0) {
                    clearInterval(countdownInterval);
                    countdownEl.text("00:00");
                }
            }, 1000);
        }

        $('#send_code_btn').on('click', function() {
            const $btn = $(this);
            const $spinner = $btn.find('.spinner-border');
            const $btnText = $btn.find('.btn-text');

            const csrfToken = $('#csrf-token-login').val();
            $email = $('#loginmodal2 #email').val();

            if($email == ''){
                $('#emailError').text('Email is required.').show();
                return false;
            }

            $btn.prop('disabled', true);
            $btnText.addClass('d-none');
            $spinner.removeClass('d-none');

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Customer', 'action' => 'sendEmailOtp']) ?>',
                method: 'POST',
                data: { email: $email, _csrfToken: csrfToken },
                success: function(response) {
                    if (response.success) {
                        showToastMessage(response.message,'success');
                        startCountdown(300);
                    } else {
                        showToastMessage(response.message,'error');         
                    }
                },
                error: function() {
                    alert('An error occurred while checking user email.');
                },
                complete: function () {
                    $btn.prop('disabled', false);
                    $spinner.addClass('d-none');
                    $btnText.removeClass('d-none');
                }
            });
        });

        $('#verify-btn').on('click', function() {
            const csrfToken = $('#csrf-token-login').val();
            $verificationCode = $('#verification_code').val();
            $email = $('#loginmodal2 #email').val();
            if($email == ''){
                $('#emailError').text('Email is required.').show();
                return false;
            }
            if ($verificationCode == '') {
                $('.error-text').text('Please enter the verification code.').show();
                return false;
            }
            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Customer', 'action' => 'verifyEmail']) ?>',
                method: 'POST',
                data: { email: $email, verification_code: $verificationCode, _csrfToken: csrfToken },
                success: function(response) {
                    if (response.success) {
                        clearInterval(countdownInterval);
                        $('#countdown').text('00:00');
                        $('#show_registration_form').show();
                        $('#verify-btn').prop('disabled', true);
                        $('.error-text').text('');
                        showToastMessage(response.message,'success');
                    } else {
                        showToastMessage(response.message,'error');
                    }
                },
                error: function() {
                    showToastMessage('An Error Occured while processing your request.','error');
                }
            });
        });

        //this will check password and confirm password match on keyup event
        $('#confirm_password').on('keyup', function() {
            const password = $('#password').val();
            const confirmPassword = $('#confirm_password').val();
            if (password !== confirmPassword) {
                $('.password_error').show();
                $('.btn-login').prop('disabled', true);
            } else {
                $('.password_error').hide();
                $('.btn-login').prop('disabled', false);
            }
        });

        $('#name').on('input', function () {
            var cleanValue = $(this).val().replace(/[^A-Za-z\s\-]/g, '');
            $(this).val(cleanValue);
        });

        $('[type="email"]').on('keyup', function () {
            var email = $(this).val().trim();
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (email === '') {
                $('#emailError').text('Email is required.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!emailRegex.test(email)) {
                $('#emailError').text('Please enter a valid email address.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else {
                $('#emailError').text('').show();
                $('.verify-btn').prop('disabled',false);
                $('#send_code_btn').prop('disabled',false);
                $('.btn-login').prop('disabled',false);
            }
        });

        $('#show_registration_form #password').on('keyup', function () {
            var password = $(this).val();
            var errorMsg = '';

            if (password.length < 8) {
                $('#passwordError').text('Password must be at least 8 characters long.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[A-Z]/.test(password)) {
                $('#passwordError').text('Password must include at least one uppercase letter.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[a-z]/.test(password)) {
                $('#passwordError').text('Password must include at least one lowercase letter.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[0-9]/.test(password)) {
                $('#passwordError').text('Password must include at least one number.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            } else if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
                $('#passwordError').text('Password must include at least one special character.').show();
                $('.verify-btn').prop('disabled',true);
                $('#send_code_btn').prop('disabled',true);
                $('.btn-login').prop('disabled',true);
            }
            else{
                $('#passwordError').text('').hide()
                $('.verify-btn').prop('disabled',false);
                $('#send_code_btn').prop('disabled',false);
                $('.btn-login').prop('disabled',false);
            }
        });
    });

</script>

<script src="../../javascript/ozone-dev.js"></script>
<script src="../../javascript/ozone.js"></script>
<!-- Bootstrap JS -->
<script src="../../bundles/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
        integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
 <script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.js"></script>

    <!-- vendors -->
     <?php 
     /****
    <script src="<?= $this->Url->build('carousel/assets/vendors/highlight.js') ?>"></script>
    <script src="<?= $this->Url->build('carousel/assets/js/app.js') ?>"></script>
    ****/ ?>
    <script src="https://cdn.jsdelivr.net/npm/nouislider@15.7.0/dist/nouislider.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>





    <!-- Toast Message Script -->
<script>
    $(document).ready(function() {
        // Check for toast message from session
        <?php
        $toastMessage = $session->read('toast_message');
        if ($toastMessage):
            // Clear the message from session after reading
            $session->delete('toast_message');
        ?>
        showToastMessage('<?= h($toastMessage['message']) ?>', '<?= h($toastMessage['type']) ?>');
        <?php endif; ?>
    });

    function showToastMessage(message, type) {
        // Create message container with enhanced styling
        const messageContainer = $(`
            <div class="alert alert-dismissible fade show" style="
                margin-bottom: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 8px;
                border: none;
                animation: slideInRight 0.3s ease-out;
            "></div>
        `);

        // Set message type and styling
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        messageContainer.addClass(alertClass);

        // Add message content with icon
        const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-circle' :
                    type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        messageContainer.html(`
            <div class="alert-body d-flex align-items-center">
                <i class="${icon} me-2"></i>
                <span><b>${message}</b></span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.alert').fadeOut(300, function(){ $(this).remove(); })"></button>
            </div>
        `);

        // Add to container and show with animation
        $('#toast-message-container').append(messageContainer);

        // Auto hide after 4 seconds
        setTimeout(() => {
            messageContainer.fadeOut(300, function() {
                $(this).remove();
            });
        }, 4000);
    }

    // Common quantity update functionality for cart and product pages
    // Global variables for rate limiting and request management
    window.updateRequests = window.updateRequests || new Map();
    window.lastUpdateTime = window.lastUpdateTime || new Map();
    const UPDATE_COOLDOWN = 1000; // 1 second cooldown between updates

    // Common function to update quantity dynamically
    function updateQuantityDynamic(inputId, change, options = {}) {
        const input = document.getElementById(inputId);
        if (!input) {
            console.error('Input element not found:', inputId);
            return;
        }

        // Get identifier for tracking (cart_item_id for cart page, product_id for product page)
        const cartItemId = input.dataset.cartItemId;
        const productId = input.dataset.productId;
        const identifier = cartItemId || productId;
        const isCartPage = !!cartItemId;
        const isProductPage = !!productId;

        // Check if there's already an ongoing request for this item
        if (window.updateRequests.has(identifier)) {
            return;
        }

        // Check cooldown period
        const lastUpdate = window.lastUpdateTime.get(identifier) || 0;
        const now = Date.now();
        if (now - lastUpdate < UPDATE_COOLDOWN) {
            showToastMessage('Please wait before updating quantity again', 'warning');
            return;
        }

        let value = parseInt(input.value) || 1;
        const newValue = Math.max(1, value + change);

        // Update input value immediately for better UX
        input.value = newValue;

        if (!identifier) {
            showToastMessage('Error: Item ID not found', 'error');
            input.value = value; // Revert value
            return;
        }

        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                         document.querySelector('input[name="_csrfToken"]')?.value;

        if (!csrfToken) {
            showToastMessage('Error: Security token not found', 'error');
            input.value = value; // Revert value
            return;
        }

        // Mark request as ongoing and disable buttons
        window.updateRequests.set(identifier, true);
        window.lastUpdateTime.set(identifier, now);

        // Disable quantity buttons if function exists
        if (typeof disableQuantityButtons === 'function') {
            disableQuantityButtons(inputId, true);
        }

        // Prepare request data - same endpoint for both cart and product pages
        const requestData = {
            quantity: newValue
        };

        // Add the appropriate ID field
        if (isCartPage) {
            requestData.cart_item_id = cartItemId;
        } else if (isProductPage) {
            requestData.product_id = productId;
        }

        // Use same endpoint for both pages
        const fetchUrl = '<?= $this->Url->build(['controller' => 'Cart', 'action' => 'updateQuantity']) ?>';

        // Make the fetch request
        fetch(fetchUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showToastMessage('Quantity updated successfully', 'success');

                // Update cart count if provided
                if (data.cartCount !== undefined && window.CartUpdater) {
                    window.CartUpdater.updateCartCount(data.cartCount);
                }

                // Handle page-specific actions
                if (isCartPage) {
                    // Reload cart page after short delay
                    setTimeout(() => {
                        location.reload();
                    }, 500);
                } else if (isProductPage) {
                    // Update product page elements
                    input.setAttribute('data-original-value', newValue);

                    // Update cart button if function exists
                    if (typeof updateCartButton === 'function') {
                        updateCartButton(true, newValue);
                    }
                }
            } else {
                showToastMessage('Failed to update quantity: ' + (data.message || 'Unknown error'), 'error');
                input.value = value; // Revert value
            }
        })
        .catch(err => {
            console.error('Fetch error:', err);
            showToastMessage('Error updating quantity: ' + err.message, 'error');
            input.value = value; // Revert value
        })
        .finally(() => {
            // Always cleanup after request completes
            window.updateRequests.delete(identifier);

            // Re-enable quantity buttons if function exists
            if (typeof disableQuantityButtons === 'function') {
                disableQuantityButtons(inputId, false);
            }
        });
    }

    // Simple quantity update function for UI only (no API call)
    function updateQuantitySimple(inputId, change) {
        const input = document.getElementById(inputId);
        if (!input) return;

        let value = parseInt(input.value) || 1;
        const newValue = Math.max(1, value + change);
        input.value = newValue;
    }

    // ========== WISHLIST FUNCTIONALITY ==========

    // Wishlist event handlers
    $(document).on('click', '.add-wishlist-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const productId = $(this).closest('.wishlist-container, .wishlist, .product-card').data('product-id');
        const wishlistContainer = $(this).closest('.wishlist-container, .wishlist');
        const button = $(this);

        // Show loading state - different for buttons vs icons
        if (button.hasClass('btn')) {
            button.prop('disabled', true).html(`
                <i class="fas fa-spinner fa-spin me-2"></i> Adding to Wishlist...
            `);
        } else {
            button.css('opacity', '0.5');
        }

        addToWishlist(productId)
            .then(response => {
                if (response.status === 'success') {
                    // Update UI based on container type
                    if (wishlistContainer.hasClass('wishlist-container')) {
                        // Product page style (button)
                        wishlistContainer.html(`
                            <button class="btn btn-wishlist w-100 d-flex justify-content-center align-items-center remove-wishlist-btn">
                                <i class="fas fa-heart text-danger me-2"></i> Remove from Wishlist
                            </button>
                        `);
                    } else {
                        // List page style (heart icon)
                        wishlistContainer.html(`
                            <span class="wishlist-icon remove-wishlist-btn heart-filled" title="Remove from wishlist">❤️</span>
                        `);
                    }

                    // Show success message
                    showToastMessage(response.message || 'Added to wishlist successfully!', 'success');

                    // Update wishlist count in header if exists
                    updateWishlistCount(1);
                } else {
                    // Reset button state
                    if (button.hasClass('btn')) {
                        button.prop('disabled', false).html(`
                            <i class="far fa-heart text-success me-2"></i> Add to Wishlist
                        `);
                    } else {
                        button.css('opacity', '1');
                    }
                    showToastMessage(response.message || 'Failed to add to wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error adding to wishlist:', error);
                // Reset button state
                if (button.hasClass('btn')) {
                    button.prop('disabled', false).html(`
                        <i class="far fa-heart text-success me-2"></i> Add to Wishlist
                    `);
                } else {
                    button.css('opacity', '1');
                }
                showToastMessage('Failed to add to wishlist', 'error');
            });
    });

    $(document).on('click', '.remove-wishlist-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const productId = $(this).closest('.wishlist-container, .wishlist, .product-card').data('product-id');
        const wishlistContainer = $(this).closest('.wishlist-container, .wishlist');
        const button = $(this);

        // Show loading state - different for buttons vs icons
        if (button.hasClass('btn')) {
            button.prop('disabled', true).html(`
                <i class="fas fa-spinner fa-spin me-2"></i> Removing...
            `);
        } else {
            button.css('opacity', '0.5');
        }

        removeFromWishlist(productId)
            .then(response => {
                if (response.status === 'success') {
                    // Update UI based on container type
                    if (wishlistContainer.hasClass('wishlist-container')) {
                        // Product page style (button)
                        wishlistContainer.html(`
                            <button class="btn btn-wishlist w-100 d-flex justify-content-center align-items-center add-wishlist-btn">
                                <i class="far fa-heart text-success me-2"></i> Add to Wishlist
                            </button>
                        `);
                    } else {
                        // List page style (heart icon)
                        wishlistContainer.html(`
                            <span class="wishlist-icon add-wishlist-btn heart-empty" title="Add to wishlist">🤍</span>
                        `);
                    }

                    // Show success message
                    showToastMessage(response.message || 'Removed from wishlist successfully!', 'success');

                    // Update wishlist count in header if exists
                    updateWishlistCount(-1);
                } else {
                    // Reset button state
                    if (button.hasClass('btn')) {
                        button.prop('disabled', false).html(`
                            <i class="fas fa-heart text-danger me-2"></i> Remove from Wishlist
                        `);
                    } else {
                        button.css('opacity', '1');
                    }
                    showToastMessage(response.message || 'Failed to remove from wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error removing from wishlist:', error);
                // Reset button state
                if (button.hasClass('btn')) {
                    button.prop('disabled', false).html(`
                        <i class="fas fa-heart text-danger me-2"></i> Remove from Wishlist
                    `);
                } else {
                    button.css('opacity', '1');
                }
                showToastMessage('Failed to remove from wishlist', 'error');
            });
    });

    // Helper functions for wishlist
    function addToWishlist(productId) {
        return new Promise((resolve, reject) => {
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_csrfToken"]')?.value ||
                             '<?= $this->request->getAttribute('csrfToken') ?>';

            $.ajax({
                headers: {
                    'X-CSRF-Token': csrfToken
                },
                url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addToWishlist']) ?>",
                type: 'POST',
                data: {product_id: productId},
                success: function (response) {
                    resolve(response);
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    function removeFromWishlist(productId) {
        return new Promise((resolve, reject) => {
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_csrfToken"]')?.value ||
                             '<?= $this->request->getAttribute('csrfToken') ?>';

            $.ajax({
                headers: {
                    'X-CSRF-Token': csrfToken
                },
                url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'removeFromWishlist']) ?>",
                type: 'POST',
                data: {product_id: productId},
                success: function (response) {
                    resolve(response);
                },
                error: function (xhr, status, error) {
                    reject('An error occurred: ' + error);
                }
            });
        });
    }

    function updateWishlistCount(change) {
        const countElement = $('.wishlist-count, .cart-superscript');
        if (countElement.length > 0) {
            const currentCount = parseInt(countElement.text()) || 0;
            const newCount = Math.max(0, currentCount + change);
            countElement.text(newCount);
        }
    }
</script>
  <?= $this->Html->script('cart-updates') ?>
  <?= $this->Html->script('custom') ?>
 <?php echo $this->fetch('add_js'); ?>
    
</body>


</html>
