<?php
namespace App\Controller;

use App\Controller\AppController;
use Cake\I18n\FrozenTime;
use Cake\I18n\FrozenDate;
use Cake\Utility\Hash;
use Cake\Core\Configure;

class DashboardsController extends AppController
{
    protected $Roles;
    protected $Orders;
    protected $Users;
    protected $Products;
    protected $Showrooms;
    protected $Warehouses;
    protected $Transactions;
    protected $SupplierPayment;
    protected $Suppliers;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $SupplierProducts;
    protected $ProductStocks;
    protected $OrderItems;
    protected $Categories;
    protected $ProductCategories;
    protected $Zones;
    protected $ShowroomExpenses;
    protected $StockRequests;
    protected $StockMovements;
    protected $StockRequestItems;
    protected $StockMovementItems;
    protected $Countries;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin'); // Use the admin layout
        $this->loadComponent('Media');

        $this->Roles = $this->fetchTable('Roles');
        $this->Orders = $this->fetchTable('Orders');
        $this->Users = $this->fetchTable('Users');
        $this->Products = $this->fetchTable('Products');
        //$this->Showrooms = $this->fetchTable('Showrooms');
        //$this->Warehouses = $this->fetchTable('Warehouses');
        $this->Transactions = $this->fetchTable('Transactions');
        //$this->SupplierPayment = $this->fetchTable('SupplierPayment');
        //$this->Suppliers = $this->fetchTable('Suppliers');
        //$this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        //$this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        //$this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Categories = $this->fetchTable('Categories');
        $this->ProductCategories = $this->fetchTable('ProductCategories');
        //$this->Zones = $this->fetchTable('Zones');
        //$this->ShowroomExpenses = $this->fetchTable('ShowroomExpenses');
        //$this->StockRequests = $this->fetchTable('StockRequests');
        //$this->StockMovements = $this->fetchTable('StockMovements');
        //$this->StockMovementItems = $this->fetchTable('StockMovementItems');
        $this->Countries = $this->fetchTable('Countries');
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        $this->Authentication->addUnauthenticatedActions(['index', 'view', 'add']);
    }

    public function index()
    {
        $user = $this->Authentication->getIdentity();
        if (!empty($user)) {
            $role = $this->Roles->get($user->role_id);
            $controllerName = $this->request->getParam('controller');
            if (strtolower($role->name) == 'admin') {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'adminDashboard']);
            } elseif (strtolower($role->name) == 'showroom manager') {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'showroomManagerDashboard']);
            } elseif (strtolower($role->name) == 'showroom supervisor') {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'supervisorManagerDashboard']);
            } elseif (strtolower($role->name) == 'warehouse manager') {
                return $this->redirect(['controller' => 'Dashboards', 'action' => 'warehouseManagerDashboard']);
            }
        } else {
            // If the user is not logged in, redirect to login page
            return $this->redirect(['controller' => 'Users', 'action' => 'login']);
        }
    }

    public function adminDashboard()
    {
        // Get country filter from request (for AJAX calls)
        $countryId = $this->request->getQuery('country_id', 'all');
        $duration = $this->request->getQuery('duration', 'current_month');
        $fromDate = $this->request->getQuery('fromDate');
        $toDate = $this->request->getQuery('toDate');

        // Calculate date range based on duration
        if ($duration === 'custom' && $fromDate && $toDate) {
            $currentMonthStart = FrozenTime::parse($fromDate);
            $currentMonthEnd = FrozenTime::parse($toDate);
        } else {
            $dateRange = $this->calculateDateRange($duration, $fromDate, $toDate);
            $currentMonthStart = $dateRange['start'];
            $currentMonthEnd = $dateRange['end'];
        }
        $totalShowrooms = "";
        $newShowrooms = "";
        $newShowroomsPercentage = "";

        $totalOrdersQuery = $this->Orders->find()
            ->where($baseConditions);

        // Get the total number of orders for the current month
        $totalOrders = $totalOrdersQuery->count();

        // // Total sales amount for the current month
        // // Calculate total sales amount for the current month
        // $totalSalesAmountQuery = $this->Orders->find()
        //     ->where([
        //         'Orders.status !=' => 'Cancelled', // Exclude canceled orders
        //         'Orders.order_date >=' => $currentMonthStart,
        //         'Orders.order_date <=' => $currentMonthEnd
        //     ])
        //     ->select([
        //         'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
        //     ])
        //     ->first();

        // $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

        // // Get the total number of all-time orders (excluding Cancelled orders)
        // $allTimeOrdersQuery = $this->Orders->find()
        //     ->where(['Orders.status !=' => 'Cancelled']);

        // $totalAllTimeOrders = $allTimeOrdersQuery->count();

        // // Calculate the percentage of orders in the current month
        // $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

        // Base conditions for the query
        $baseConditions = [
            'Orders.status !=' => 'Cancelled',
            'Orders.order_date >=' => $currentMonthStart,
            'Orders.order_date <=' => $currentMonthEnd
        ];

        // Add country filter if specified
        if ($countryId && $countryId !== 'all') {
            $baseConditions['Orders.country_id'] = $countryId;
        }

        // Get count of Online Orders
        // $onlineOrders = $this->Orders->find()
        //     ->where(array_merge($baseConditions, ['Orders.order_type' => 'Online']))
        //     ->count();

        // // Get count of Showroom Orders
        // $showroomOrders = $this->Orders->find()
        //     ->where(array_merge($baseConditions, ['Orders.order_type' => 'Showroom']))
        //     ->count();

        // // Get total orders (Online + Showroom)
        // $totalOrders = $onlineOrders + $showroomOrders;

        // Get total sales amount
        $totalSalesAmountQuery = $this->Orders->find()
            ->where($baseConditions)
            ->select([
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
            ])
            ->first();

        $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

        // Get total all-time orders (excluding Cancelled)
        $allTimeOrders = $this->Orders->find()
            ->where(['Orders.status !=' => 'Cancelled'])
            ->count();

        // Calculate percentage of orders in the current month
        $percentageOrders = ($allTimeOrders > 0) ? ($totalOrders / $allTimeOrders) * 100 : 0;

        // Base query for active users
        $userQuery = $this->Users->find()
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->order(['Users.first_name' => 'ASC']);

        // Filter by role if provided
        $roleId = $this->request->getQuery('role');
        if ($roleId) {
            $userQuery->where(['Users.role_id' => $roleId]);
        }

        // Get the total count of active users
        $totalUsers = $userQuery->count();

        $newUsers = 0;
        if ($currentMonthStart && $currentMonthEnd) {
            $users = $userQuery->all(); // Retrieve all users

            foreach ($users as $user) {
                $createdDate = $user->created->format('Y-m-d');

                if ($createdDate >= $currentMonthStart->format('Y-m-d')) {
                    $newUsers++;
                }
            }
        } else {
            // If no date range is provided, new users count is zero
            $newUsers = 0;
        }

        // Calculate the percentage of new users
        $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

        // Fetch total active products
        $totalActiveProducts = $this->Products->find()
            ->where([
                'status' => 'A'
                // 'created >=' => $currentMonthStart,
                // 'created <=' => $currentMonthEnd
            ])
            ->count();

        // Fetch number of new products added in the current month
        $newProducts = $this->Products->find()
            ->where([
                'status' => 'A',
                'created >=' => $currentMonthStart,
                'created <=' => $currentMonthEnd
            ])
            ->count();

        // Calculate the percentage of new products
        $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;


        //ORDER TRENDS APEX CHART - Default to Current Month
        // $currentDate = FrozenTime::now();
        // $currentMonthStart = $currentDate->startOfMonth();
        // $currentMonthEnd = $currentDate->endOfMonth();

        // Get current month data by days
        // $days = [];
        // $orderCounts = [];

        // Create array for all days in current month
        // $current = FrozenTime::parse($currentMonthStart->format('Y-m-d'));
        // while ($current <= $currentMonthEnd) {
        //     $days[$current->format('Y-m-d')] = [
        //         'day' => $current->format('M d'),
        //         'order_count' => 0
        //     ];
        //     $current = $current->addDays(1);
        // }

        // Query to get orders for current month, grouped by day
        // $query = $this->Orders->find();
        // $query->select([
        //     'date' => 'DATE(Orders.order_date)',
        //     'order_count' => $query->func()->count('Orders.id')
        // ])
        // ->where([
        //     'Orders.status !=' => 'Cancelled', // Exclude canceled orders
        //     'Orders.order_date >=' => $currentMonthStart,
        //     'Orders.order_date <=' => $currentMonthEnd
        // ])
        // ->group(['date'])
        // ->order(['date' => 'ASC']);

        // // Fetch the results and map them into the current month days array
        // $results = $query->all();

        // foreach ($results as $row) {
        //     if (isset($days[$row->date])) {
        //         $days[$row->date]['order_count'] = $row->order_count;
        //     }
        // }

        // Prepare data for the chart
        // $monthNames = array_column($days, 'day');
        // $orderCounts = array_column($days, 'order_count');

        // /**  REVENUE AND EXPENSES CHART **/
        // $revenueMonths = [];
        // for ($i = 5; $i >= 0; $i--) {
        //     $month = $currentDate->modify("-$i months");
        //     $monthKey = $month->format('Y-m');
        //     $revenueMonths[$monthKey] = [
        //         'month' => $month->format('F'),
        //         'revenue' => 0,
        //         'expenses' => 0
        //     ];
        // }

        // // Define date range
        // $startRevenueDate = FrozenTime::now()->subMonths(6)->startOfMonth();
        // $endRevenueDate = FrozenTime::now()->endOfMonth();

        // Fetch revenue for the last six months
        // $revenueQuery = $this->Transactions->find()
        //     ->select([
        //         'month' => 'MONTH(Transactions.created)',
        //         'year' => 'YEAR(Transactions.created)',
        //         'total_revenue' => $this->Transactions->find()->func()->sum('Transactions.amount')
        //     ])
        //     ->innerJoinWith('Orders', function ($q) {
        //         return $q->where(['Orders.status !=' => 'Cancelled']);
        //     })
        //     ->where([
        //         'Transactions.created >=' => $startRevenueDate,
        //         'Transactions.created <=' => $endRevenueDate
        //     ])
        //     ->group(['year', 'month'])
        //     ->order(['year' => 'DESC', 'month' => 'DESC'])
        //     ->toArray();

        // // Map revenue data to months
        // foreach ($revenueQuery as $data) {
        //     $monthKey = sprintf('%04d-%02d', $data->year, $data->month);
        //     if (isset($revenueMonths[$monthKey])) {
        //         $revenueMonths[$monthKey]['revenue'] = $data->total_revenue / 1000; // Convert to thousands
        //     }
        // }

        // // Prepare chart data
        // $revenueMonthNames = array_column($revenueMonths, 'month');
        // $revenueData = array_column($revenueMonths, 'revenue');
        // $expensesData = array_column($revenueMonths, 'expenses'); // Currently all zeros, can be enhanced later


        /** Fetch top 5 selling products for online and showroom sales **/
        // Get top 5 online products
        // $topOnlineProducts = $this->OrderItems->find()
        //     ->select([
        //         'Products.id',
        //         'Products.name',
        //         'total_sales_amount' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
        //         'total_units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
        //     ])
        //     ->contain([
        //         'Products' => [
        //             'ProductImages' => function ($q) {
        //                 return $q->where([
        //                     'ProductImages.image_default' => 1,
        //                     'ProductImages.status' => 'A'
        //                 ]);
        //             }
        //         ]
        //     ])
        //     ->matching('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
        //         return $q->where([
        //             'Orders.order_type' => 'Online',
        //             'Orders.order_date >=' => $currentMonthStart,
        //             'Orders.order_date <=' => $currentMonthEnd,
        //             'Orders.status !=' => 'Cancelled'
        //         ]);
        //     })
        //     ->group(['Products.id'])
        //     ->order(['total_units_sold' => 'DESC'])
        //     ->limit(5)
        //     ->toArray();

        // Fetch top 5 showroom products
        // $topShowroomProducts = $this->OrderItems->find()
        //     ->select([
        //         'Products.id',
        //         'Products.name',
        //         'total_sales_amount' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
        //         'total_units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
        //     ])
        //     ->contain([
        //         'Products' => [
        //             'ProductImages' => function ($q) {
        //                 return $q->where([
        //                     'ProductImages.image_default' => 1,
        //                     'ProductImages.status' => 'A'
        //                 ]);
        //             }
        //         ]
        //     ])
        //     ->matching('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
        //         return $q->where([
        //             'Orders.order_type' => 'Showroom',
        //             'Orders.order_date >=' => $currentMonthStart,
        //             'Orders.order_date <=' => $currentMonthEnd,
        //             'Orders.status !=' => 'Cancelled'
        //         ]);
        //     })
        //     ->group(['Products.id'])
        //     ->order(['total_units_sold' => 'DESC'])
        //     ->limit(5)
        //     ->toArray();

        // // Combine both lists
        // $topSellingProducts = [
        //     'online' => $topOnlineProducts,
        //     'showroom' => $topShowroomProducts
        // ];

        // foreach ($topSellingProducts as $channel => $products) {
        //     // Loop through each product in the channel
        //     foreach ($products as $product) {
        //         // Check if the product has images
        //         if (!empty($product->product->product_images)) {
        //             // Convert image URL to CloudFront URL
        //             $product->product->product_images[0]->image = $this->Media->getCloudFrontURL($product->product->product_images[0]->image);
        //         }
        //     }
        // }


        /** FETCH TOP 5 PRODUCT CATEGORIES **/
        // $totalCategorySalesQuery = $this->OrderItems->find()
        //     ->innerJoinWith('Products.ProductCategories.Categories')
        //     ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
        //         return $q->where([
        //             'Orders.created >=' => $currentMonthStart,
        //             'Orders.created <=' => $currentMonthEnd,
        //             'Orders.status !=' => 'Cancelled'
        //         ]);
        //     })
        //     ->select([
        //         'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity')
        //     ])
        //     ->first();

        // $totalCategorySales = $totalCategorySalesQuery ? $totalCategorySalesQuery->total_sales : 0;


        // // Step 2: Fetch top 5 product categories and calculate percentage
        // $topCategories = $this->OrderItems->find()
        //     ->select([
        //         'category_id' => 'Categories.id',
        //         'category_icon' => 'Categories.category_icon',
        //         'category_name' => 'Categories.name',
        //         'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
        //         'units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
        //     ])
        //     ->innerJoinWith('Products.ProductCategories.Categories') // Joining the necessary tables
        //     ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
        //         return $q->where([
        //             'Orders.created >=' => $currentMonthStart,
        //             'Orders.created <=' => $currentMonthEnd,
        //             'Orders.status !=' => 'Cancelled'
        //         ]);
        //     })
        //     ->group(['Categories.id', 'Categories.name']) // Grouping by category
        //     ->order(['total_sales' => 'DESC']) // Ordering by sales
        //     ->limit(5) // Limiting to top 5 categories
        //     ->toArray();

        // // Calculate percentage for progress bars
        // foreach ($topCategories as &$category) {
        //     $category['percentage'] = $totalCategorySales > 0 ? ($category['total_sales'] / $totalCategorySales) * 100 : 0;

        //     if ($category['category_icon']) {
        //         $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
        //     }
        // }

        // /** ORDER STATUS DISTRIBUTION DATA **/
        // $orderStatusData = $this->Orders->find()
        //     ->select([
        //         'status',
        //         'count' => $this->Orders->find()->func()->count('Orders.id')
        //     ])
        //     ->where([
        //         'Orders.order_date >=' => $currentMonthStart,
        //         'Orders.order_date <=' => $currentMonthEnd
        //     ])
        //     ->group(['status'])
        //     ->toArray();

        // // Prepare order status data for charts
        // $orderStatuses = ['Pending' => 0, 'Processing' => 0, 'Approved' => 0, 'Rejected' => 0, 'Cancelled' => 0];
        // foreach ($orderStatusData as $statusData) {
        //     if (isset($orderStatuses[$statusData->status])) {
        //         $orderStatuses[$statusData->status] = $statusData->count;
        //     }
        // }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        // Get all countries for country-based filtering
        $countries = $this->Countries->find()
            ->select(['id', 'name'])
            ->order(['name' => 'ASC'])
            ->toArray();

        // Get country-based statistics for initial load
        $countryStats = $this->getCountryBasedStats();

        // $this->set(compact('totalOrders', 'onlineOrders', 'showroomOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage', 'monthNames', 'orderCounts', 'revenueMonthNames', 'revenueData', 'expensesData', 'orderStatuses', 'topSellingProducts', 'topCategories', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'countries', 'countryStats'));
         $this->set(compact('totalOrders', 'totalSalesAmount','totalUsers', 'newUsers', 'totalActiveProducts', 'newProducts','currencySymbol', 'decimalSeparator', 'thousandSeparator', 'countries'));
    }

    /**
     * Get chart data based on duration filter
     */
    // public function getChartData()
    // {
    //     $this->request->allowMethod(['get']);

    //     $duration = $this->request->getQuery('duration', 'current_month');
    //     $fromDate = $this->request->getQuery('fromDate');
    //     $toDate = $this->request->getQuery('toDate');

    //     // Calculate date range based on duration
    //     $dateRange = $this->calculateDateRange($duration, $fromDate, $toDate);
    //     $startDate = $dateRange['start'];
    //     $endDate = $dateRange['end'];

    //     // Get order trends data
    //     $orderTrendsData = $this->getOrderTrendsData($startDate, $endDate, $duration);

    //     // Get revenue data
    //     $revenueData = $this->getRevenueData($startDate, $endDate, $duration);

    //     // Get order status data
    //     $orderStatusData = $this->getOrderStatusData($startDate, $endDate);

    //     // Get order type data (Online vs Showroom)
    //     $orderTypeData = $this->getOrderTypeData($startDate, $endDate);

    //     $response = [
    //         'orderTrends' => $orderTrendsData,
    //         'revenue' => $revenueData,
    //         'orderStatus' => $orderStatusData,
    //         'orderType' => $orderTypeData,
    //         'dateRange' => [
    //             'start' => $startDate->format('Y-m-d'),
    //             'end' => $endDate->format('Y-m-d'),
    //             'label' => $this->getDateRangeLabel($duration, $startDate, $endDate)
    //         ]
    //     ];

    //     return $this->response->withType('application/json')
    //                 ->withStringBody(json_encode($response));
    // }

    /**
     * Calculate date range based on duration
     */
    private function calculateDateRange($duration, $fromDate = null, $toDate = null)
    {
        $now = FrozenTime::now();

        switch ($duration) {
            case 'current_month':
                return [
                    'start' => $now->startOfMonth(),
                    'end' => $now->endOfMonth()
                ];

            // case 'financial_year':
            //     // Assuming financial year starts in April (adjust as needed)
            //     $currentYear = $now->year;
            //     $financialYearStart = $now->month >= 4 ?
            //         FrozenTime::create($currentYear, 4, 1) :
            //         FrozenTime::create($currentYear - 1, 4, 1);
            //     $financialYearEnd = $financialYearStart->addYear()->subDay();
            //     $currentEndOfMonth = $now->endOfMonth();

            //     return [
            //         'start' => $financialYearStart,
            //         'end' => $financialYearEnd < $currentEndOfMonth ? $financialYearEnd : $currentEndOfMonth
            //     ];

            case 'last_3_months':
                return [
                    'start' => $now->subMonths(3)->startOfMonth(),
                    'end' => $now->endOfMonth()
                ];

            case 'last_6_months':
                return [
                    'start' => $now->subMonths(6)->startOfMonth(),
                    'end' => $now->endOfMonth()
                ];

            case 'current_year':
                return [
                    'start' => $now->startOfYear(),
                    'end' => $now->endOfMonth()
                ];

            case 'custom':
                if ($fromDate && $toDate) {
                    return [
                        'start' => FrozenTime::parse($fromDate)->startOfDay(),
                        'end' => FrozenTime::parse($toDate)->endOfDay()
                    ];
                }
                // Fallback to current month if custom dates not provided
                return [
                    'start' => $now->startOfMonth(),
                    'end' => $now->endOfMonth()
                ];

            default:
                return [
                    'start' => $now->startOfMonth(),
                    'end' => $now->endOfMonth()
                ];
        }
    }

    /**
     * Get order trends data for charts
     */
    // private function getOrderTrendsData($startDate, $endDate, $duration)
    // {
    //     // Determine grouping based on duration
    //     $groupBy = $this->getGroupingPeriod($duration, $startDate, $endDate);

    //     if ($groupBy === 'day') {
    //         $query = $this->Orders->find();
    //         $query->select([
    //             'date' => 'DATE(Orders.order_date)',
    //             'order_count' => $query->func()->count('Orders.id')
    //         ]);
    //     } else {
    //         $query = $this->Orders->find();
    //         $query->select([
    //             'month' => $query->func()->month(['Orders.order_date' => 'literal']),
    //             'year' => $query->func()->year(['Orders.order_date' => 'literal']),
    //             'order_count' => $query->func()->count('Orders.id')
    //         ]);
    //     }

    //     $results = $query->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate
    //         ])
    //         ->group($groupBy === 'day' ? ['date'] : ['year', 'month'])
    //         ->order($groupBy === 'day' ? ['date' => 'ASC'] : ['year' => 'ASC', 'month' => 'ASC'])
    //         ->toArray();

    //     return $this->formatOrderTrendsData($results, $startDate, $endDate, $groupBy);
    // }

    /**
     * Get revenue data for charts
     */
    // private function getRevenueData($startDate, $endDate, $duration)
    // {
    //     $groupBy = $this->getGroupingPeriod($duration, $startDate, $endDate);

    //     if ($groupBy === 'day') {
    //         $query = $this->Transactions->find();
    //         $query->select([
    //             'date' => 'DATE(Transactions.created)',
    //             'total_revenue' => $query->func()->sum('Transactions.amount')
    //         ]);
    //     } else {
    //         $query = $this->Transactions->find();
    //         $query->select([
    //             'month' => 'MONTH(Transactions.created)',
    //             'year' => 'YEAR(Transactions.created)',
    //             'total_revenue' => $query->func()->sum('Transactions.amount')
    //         ]);
    //     }

    //     $results = $query->innerJoinWith('Orders', function ($q) {
    //             return $q->where(['Orders.status !=' => 'Cancelled']);
    //         })
    //         ->where([
    //             'Transactions.created >=' => $startDate,
    //             'Transactions.created <=' => $endDate
    //         ])
    //         ->group($groupBy === 'day' ? ['date'] : ['year', 'month'])
    //         ->order($groupBy === 'day' ? ['date' => 'ASC'] : ['year' => 'ASC', 'month' => 'ASC'])
    //         ->toArray();

    //     return $this->formatRevenueData($results, $startDate, $endDate, $groupBy);
    // }

    /**
     * Get order status data
     */
    // private function getOrderStatusData($startDate, $endDate)
    // {
    //     $orderStatusData = $this->Orders->find()
    //         ->select([
    //             'status',
    //             'count' => $this->Orders->find()->func()->count('Orders.id')
    //         ])
    //         ->where([
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate
    //         ])
    //         ->group(['status'])
    //         ->toArray();

    //     $orderStatuses = ['Pending' => 0, 'Processing' => 0, 'Approved' => 0, 'Rejected' => 0, 'Cancelled' => 0];
    //     foreach ($orderStatusData as $statusData) {
    //         if (isset($orderStatuses[$statusData->status])) {
    //             $orderStatuses[$statusData->status] = $statusData->count;
    //         }
    //     }

    //     return [
    //         'labels' => array_keys($orderStatuses),
    //         'data' => array_values($orderStatuses)
    //     ];
    // }

    /**
     * Get order type data (Online vs Showroom)
     */
    // private function getOrderTypeData($startDate, $endDate)
    // {
    //     $onlineOrders = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.order_type' => 'Online',
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate
    //         ])
    //         ->count();

    //     $showroomOrders = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.order_type' => 'Showroom',
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate
    //         ])
    //         ->count();

    //     return [
    //         'labels' => ['Online Orders', 'Showroom Orders'],
    //         'data' => [$onlineOrders, $showroomOrders]
    //     ];
    // }

    /**
     * Determine grouping period based on duration
     */
    // private function getGroupingPeriod($duration, $startDate, $endDate)
    // {
    //     $daysDiff = $startDate->diffInDays($endDate);

    //     // If less than 60 days, group by day, otherwise by month
    //     if ($daysDiff <= 60 || $duration === 'current_month') {
    //         return 'day';
    //     }

    //     return 'month';
    // }

    /**
     * Format order trends data for charts
     */
    // private function formatOrderTrendsData($results, $startDate, $endDate, $groupBy)
    // {
    //     if ($groupBy === 'day') {
    //         $labels = [];
    //         $data = [];
    //         $current = FrozenTime::parse($startDate->format('Y-m-d')); // Create a copy

    //         // Create array with all dates in range
    //         $dataMap = [];
    //         foreach ($results as $result) {
    //             $dataMap[$result->date] = $result->order_count;
    //         }

    //         while ($current <= $endDate) {
    //             $dateKey = $current->format('Y-m-d');
    //             $labels[] = $current->format('M d');
    //             $data[] = isset($dataMap[$dateKey]) ? $dataMap[$dateKey] : 0;
    //             $current = $current->addDays(1);
    //         }
    //     } else {
    //         $labels = [];
    //         $data = [];
    //         $dataMap = [];

    //         foreach ($results as $result) {
    //             $key = sprintf('%04d-%02d', $result->year, $result->month);
    //             $dataMap[$key] = $result->order_count;
    //         }

    //         $current = FrozenTime::parse($startDate->format('Y-m-01'))->startOfMonth(); // Create a copy and start of month
    //         while ($current <= $endDate) {
    //             $key = $current->format('Y-m');
    //             $labels[] = $current->format('M Y');
    //             $data[] = isset($dataMap[$key]) ? $dataMap[$key] : 0;
    //             $current = $current->addMonths(1);
    //         }
    //     }

    //     return [
    //         'labels' => $labels,
    //         'data' => $data
    //     ];
    // }

    /**
     * Format revenue data for charts
     */
    // private function formatRevenueData($results, $startDate, $endDate, $groupBy)
    // {
    //     if ($groupBy === 'day') {
    //         $labels = [];
    //         $data = [];
    //         $current = FrozenTime::parse($startDate->format('Y-m-d')); // Create a copy

    //         $dataMap = [];
    //         foreach ($results as $result) {
    //             $dataMap[$result->date] = $result->total_revenue / 1000; // Convert to thousands
    //         }

    //         while ($current <= $endDate) {
    //             $dateKey = $current->format('Y-m-d');
    //             $labels[] = $current->format('M d');
    //             $data[] = isset($dataMap[$dateKey]) ? $dataMap[$dateKey] : 0;
    //             $current = $current->addDays(1);
    //         }
    //     } else {
    //         $labels = [];
    //         $data = [];
    //         $dataMap = [];

    //         foreach ($results as $result) {
    //             $key = sprintf('%04d-%02d', $result->year, $result->month);
    //             $dataMap[$key] = $result->total_revenue / 1000; // Convert to thousands
    //         }

    //         $current = FrozenTime::parse($startDate->format('Y-m-01'))->startOfMonth(); // Create a copy and start of month
    //         while ($current <= $endDate) {
    //             $key = $current->format('Y-m');
    //             $labels[] = $current->format('M Y');
    //             $data[] = isset($dataMap[$key]) ? $dataMap[$key] : 0;
    //             $current = $current->addMonths(1);
    //         }
    //     }

    //     return [
    //         'labels' => $labels,
    //         'data' => $data
    //     ];
    // }

    /**
     * Get date range label for display
     */
    private function getDateRangeLabel($duration, $startDate, $endDate)
    {
        switch ($duration) {
            case 'current_month':
                return 'Current Month (' . $startDate->format('M Y') . ')';
            // case 'financial_year':
            //     return 'Financial Year (' . $startDate->format('M Y') . ' - ' . $endDate->format('M Y') . ')';
            case 'last_3_months':
                return 'Last 3 Months';
            case 'last_6_months':
                return 'Last 6 Months';
            case 'current_year':
                return 'Current Year (' . $startDate->format('Y') . ')';
            case 'custom':
                return 'Custom Range (' . $startDate->format('M d, Y') . ' - ' . $endDate->format('M d, Y') . ')';
            default:
                return 'Selected Period';
        }
    }

    // public function adminDashboard_OLD()
    // {
    //     // Get current month start and end date
    //     $currentMonthStart = FrozenTime::now()->startOfMonth();
    //     $currentMonthEnd = FrozenTime::now()->endOfMonth();

    //     // $totalOrdersQuery = $this->Orders->find()
    //     //     ->where([
    //     //         'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
    //     //         'Orders.order_date >=' => $currentMonthStart,
    //     //         'Orders.order_date <=' => $currentMonthEnd
    //     //     ]);

    //     // // Get the total number of orders for the current month
    //     // $totalOrders = $totalOrdersQuery->count();

    //     // // Total sales amount for the current month
    //     // // Calculate total sales amount for the current month
    //     // $totalSalesAmountQuery = $this->Orders->find()
    //     //     ->where([
    //     //         'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //     //         'Orders.order_date >=' => $currentMonthStart,
    //     //         'Orders.order_date <=' => $currentMonthEnd
    //     //     ])
    //     //     ->select([
    //     //         'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
    //     //     ])
    //     //     ->first();

    //     // $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

    //     // // Get the total number of all-time orders (excluding Cancelled orders)
    //     // $allTimeOrdersQuery = $this->Orders->find()
    //     //     ->where(['Orders.status !=' => 'Cancelled']);

    //     // $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //     // // Calculate the percentage of orders in the current month
    //     // $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //     // Base conditions for the query
    //     $baseConditions = [
    //         'Orders.status !=' => 'Cancelled',
    //         'Orders.order_date >=' => $currentMonthStart,
    //         'Orders.order_date <=' => $currentMonthEnd
    //     ];

    //     // Get count of Online Orders
    //     $onlineOrders = $this->Orders->find()
    //         ->where(array_merge($baseConditions, ['Orders.order_type' => 'Online']))
    //         ->count();

    //     // Get count of Showroom Orders
    //     $showroomOrders = $this->Orders->find()
    //         ->where(array_merge($baseConditions, ['Orders.order_type' => 'Showroom']))
    //         ->count();

    //     // Get total orders (Online + Showroom)
    //     $totalOrders = $onlineOrders + $showroomOrders;

    //     // Get total sales amount
    //     $totalSalesAmountQuery = $this->Orders->find()
    //         ->where($baseConditions)
    //         ->select([
    //             'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
    //         ])
    //         ->first();

    //     $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

    //     // Get total all-time orders (excluding Cancelled)
    //     $allTimeOrders = $this->Orders->find()
    //         ->where(['Orders.status !=' => 'Cancelled'])
    //         ->count();

    //     // Calculate percentage of orders in the current month
    //     $percentageOrders = ($allTimeOrders > 0) ? ($totalOrders / $allTimeOrders) * 100 : 0;

    //     // Base query for active users
    //     $userQuery = $this->Users->find()
    //         ->contain(['Roles'])
    //         ->where(['Users.status' => 'A'])
    //         ->order(['Users.first_name' => 'ASC']);

    //     // Filter by role if provided
    //     $roleId = $this->request->getQuery('role');
    //     if ($roleId) {
    //         $userQuery->where(['Users.role_id' => $roleId]);
    //     }

    //     // Get the total count of active users
    //     $totalUsers = $userQuery->count();

    //     $newUsers = 0;
    //     if ($currentMonthStart && $currentMonthEnd) {
    //         $users = $userQuery->all(); // Retrieve all users

    //         foreach ($users as $user) {
    //             $createdDate = $user->created->format('Y-m-d');

    //             if ($createdDate >= $currentMonthStart->format('Y-m-d')) {
    //                 $newUsers++;
    //             }
    //         }
    //     } else {
    //         // If no date range is provided, new users count is zero
    //         $newUsers = 0;
    //     }

    //     // Calculate the percentage of new users
    //     $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //     // Fetch total active products
    //     $totalActiveProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $currentMonthStart,
    //             // 'created <=' => $currentMonthEnd
    //         ])
    //         ->count();

    //     // Fetch number of new products added in the current month
    //     $newProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $currentMonthStart,
    //             'created <=' => $currentMonthEnd
    //         ])
    //         ->count();

    //     // Calculate the percentage of new products
    //     $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //     // Get the total number of active showrooms
    //     $totalShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $currentMonthStart,
    //             // 'created <=' => $currentMonthEnd
    //         ])
    //         ->count();

    //     $newShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $currentMonthStart,
    //             'created <=' => $currentMonthEnd
    //         ])
    //         ->count();

    //     // Calculate the percentage of new showrooms
    //     $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

    //     //ORDER TRENDS APEX CHART
    //     $currentDate = FrozenTime::now();

    //     $months = [];
    //     $orderCounts = [];
    //     for ($i = 5; $i >= 0; $i--) {
    //         $month = $currentDate->modify("-$i months");
    //         $months[$month->format('Y-m')] = [
    //             'month' => $month->format('F'),
    //             'order_count' => 0
    //         ];
    //     }

    //     // Query to get orders for the last six months, grouped by month
    //     $query = $this->Orders->find();
    //     $query->select([
    //         'month' => $query->func()->month(['Orders.created' => 'literal']),
    //         'year' => $query->func()->year(['Orders.created' => 'literal']),
    //         'order_count' => $query->func()->count('Orders.id')
    //     ])
    //     ->where([
    //         'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //         'Orders.created >=' => $currentDate->modify('-6 months')->i18nFormat('yyyy-MM-01 00:00:00')
    //     ])
    //     ->group(['year', 'month'])
    //     ->order(['year' => 'DESC', 'month' => 'DESC']);

    //     // Fetch the results and map them into the last six months' array
    //     $results = $query->all();
    //     foreach ($results as $row) {
    //         $formattedKey = sprintf('%04d-%02d', $row->year, $row->month);
    //         if (isset($months[$formattedKey])) {
    //             $months[$formattedKey]['order_count'] = $row->order_count;
    //         }
    //     }

    //     // Prepare data for the chart
    //     $monthNames = array_column($months, 'month');
    //     $orderCounts = array_column($months, 'order_count');

    //     /**  REVENUE AND EXPENSES CHART **/
    //     $months = [];
    //     for ($i = 5; $i >= 0; $i--) {
    //         $month = $currentDate->modify("-$i months");
    //         $monthKey = $month->format('Y-m');
    //         $months[$monthKey] = [
    //             'month' => $month->format('F'),
    //             'revenue' => 0,
    //             'expenses' => 0
    //         ];
    //     }

    //     // Define date range
    //     $startRevenueDate = FrozenTime::now()->subMonths(6)->startOfMonth();
    //     $endRevenueDate = FrozenTime::now()->endOfMonth();

    //     // Fetch revenue for the last six months
    //     $revenueQuery = $this->Transactions->find()
    //         ->select([
    //             'month' => 'MONTH(Transactions.created)',
    //             'year' => 'YEAR(Transactions.created)',
    //             'total_revenue' => $this->Transactions->find()->func()->sum('Transactions.amount')
    //         ])
    //         ->innerJoinWith('Orders', function ($q) {
    //             return $q->where(['Orders.status !=' => 'Cancelled']);
    //         })
    //         ->where([
    //             'Transactions.created >=' => $startRevenueDate,
    //             'Transactions.created <=' => $endRevenueDate
    //         ])
    //         ->group(['year', 'month'])
    //         ->order(['year' => 'DESC', 'month' => 'DESC'])
    //         ->toArray();

    //     // Map revenue data to months
    //     foreach ($revenueQuery as $data) {
    //         $monthKey = sprintf('%04d-%02d', $data->year, $data->month);
    //         if (isset($months[$monthKey])) {
    //             $months[$monthKey]['revenue'] = $data->total_revenue / 1000; // Convert to thousands
    //         }
    //     }

    //     // Fetch expenses for the last six months
    //     $expensesQuery = $this->SupplierPayment->find()
    //         ->select([
    //             'month' => 'MONTH(SupplierPayment.created)',
    //             'year' => 'YEAR(SupplierPayment.created)',
    //             'total_expenses' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
    //         ])
    //         ->innerJoinWith('SupplierPurchaseOrders', function ($q) {
    //             return $q->where(['SupplierPurchaseOrders.status' => 'A']);
    //         })
    //         ->where([
    //             'SupplierPayment.created >=' => $startRevenueDate,
    //             'SupplierPayment.created <=' => $endRevenueDate
    //         ])
    //         ->group(['year', 'month'])
    //         ->order(['year' => 'DESC', 'month' => 'DESC'])
    //         ->toArray();

    //     // Fetch ShowroomExpenses for the last six months
    //     $showroomExpensesQuery = $this->ShowroomExpenses->find()
    //         ->select([
    //             'month' => 'MONTH(ShowroomExpenses.created)',
    //             'year' => 'YEAR(ShowroomExpenses.created)',
    //             'total_expenses' => $this->ShowroomExpenses->find()->func()->sum('ShowroomExpenses.amount')
    //         ])
    //         ->where([
    //             'ShowroomExpenses.created >=' => $startRevenueDate,
    //             'ShowroomExpenses.created <=' => $endRevenueDate,
    //             'ShowroomExpenses.status' => 'A'
    //         ])
    //         ->group(['year', 'month'])
    //         ->order(['year' => 'DESC', 'month' => 'DESC'])
    //         ->toArray();

    //     // Map expense data to months
    //     foreach ($expensesQuery as $data) {
    //         $monthKey = sprintf('%04d-%02d', $data->year, $data->month);
    //         if (isset($months[$monthKey])) {
    //             $months[$monthKey]['expenses'] = $data->total_expenses / 1000; // Convert to thousands
    //         }
    //     }

    //     // Merge ShowroomExpenses into months
    //     foreach ($showroomExpensesQuery as $data) {
    //         $monthKey = sprintf('%04d-%02d', $data->year, $data->month);
    //         if (!isset($months[$monthKey])) {
    //             $months[$monthKey] = [
    //                 'expenses' => 0
    //             ];
    //         }
    //         $months[$monthKey]['expenses'] += $data->total_expenses / 1000; // Convert to thousands
    //     }

    //     // Prepare data for the chart
    //     $revenueMonthNames = [];
    //     $revenueData = [];
    //     $expensesData = [];
    //     foreach ($months as $data) {
    //         $revenueMonthNames[] = $data['month'];
    //         $revenueData[] = $data['revenue'];
    //         $expensesData[] = $data['expenses'];
    //     }

    //     $pendingBills = $this->SupplierPurchaseOrders->find()
    //         ->contain([
    //             'SupplierPurchaseOrdersItems' => function ($q) {
    //                 return $q->contain(['Products'])
    //                      ->select([
    //                         'SupplierPurchaseOrdersItems.product_id',
    //                         'SupplierPurchaseOrdersItems.product_variant_id',
    //                         'SupplierPurchaseOrdersItems.product_attribute_id',
    //                         'SupplierPurchaseOrdersItems.approved_quantity',
    //                         'SupplierPurchaseOrdersItems.quantity', // Add quantity to select for 'P' status
    //                         'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
    //                     ]);
    //             }
    //         ])
    //         ->where([
    //             'SupplierPurchaseOrders.payment_status' => 'Pending',
    //             'SupplierPurchaseOrders.status IN' => ['A', 'P']
    //         ])
    //         ->all();

    //     $supplier_payment_pendings = [];

    //     // Loop through each purchase order and calculate pending amount
    //     foreach ($pendingBills as $purchaseOrder) {
    //         $totalPendingAmount = 0;

    //         foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {
    //             // Fetch supplier price
    //             // $supplierPrice = $this->SupplierProducts->find()
    //             //     ->select(['supplier_price'])
    //             //     ->where([
    //             //         'supplier_id' => $purchaseOrder->supplier_id,
    //             //         'product_id' => $product->product_id
    //             //     ])
    //             //     ->first();

    //             $supplier_price_conditions = [
    //                 'supplier_id' => $purchaseOrder->supplier_id,
    //                 'product_id' => $product->product_id
    //             ];

    //             // Check if product_variant_id exists and is not null
    //             if (!empty($product->product_variant_id)) {
    //                 $supplier_price_conditions['product_variant_id'] = $product->product_variant_id;
    //             }

    //             $supplierPrice = $this->SupplierProducts->find()
    //                 ->select(['supplier_price'])
    //                 ->where($supplier_price_conditions)
    //                 ->first();

    //             // Calculate total pending amount for each product
    //             if ($supplierPrice) {
    //                 // Check purchase order status to determine the quantity field to use
    //                 $quantity = ($purchaseOrder->status === 'A')
    //                     ? $product->approved_quantity
    //                     : $product->quantity;

    //                 $totalPendingAmount += $supplierPrice->supplier_price * $quantity;
    //             }
    //         }

    //         $totalPendingAmount = number_format((float)$totalPendingAmount, 2, '.', '');

    //         // Store Bill No and pending amount
    //         $supplier_payment_pendings[] = [
    //             'bill_number' => $purchaseOrder->bill_no,
    //             'pending_amount' => $totalPendingAmount
    //         ];
    //     }

    //     //TOP 5 supplier Payments
    //     $supplier_payment = $this->SupplierPayment->find()
    //         ->contain([
    //             'Suppliers','SupplierPurchaseOrders'
    //             ])
    //         ->order(['SupplierPayment.id' => 'DESC'])->limit(5)->toArray();

    //     /** Fetch top 5 selling products for online and showroom sales **/
    //     // Get top 5 online products
    //     $topOnlineProducts = $this->OrderItems->find()
    //         ->select([
    //             'Products.id',
    //             'Products.name',
    //             'total_sales_amount' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
    //             'total_units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
    //         ])
    //         ->contain([
    //             'Products' => [
    //                 'ProductImages' => function ($q) {
    //                     return $q->where([
    //                         'ProductImages.image_default' => 1,
    //                         'ProductImages.status' => 'A'
    //                     ]);
    //                 }
    //             ]
    //         ])
    //         ->matching('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
    //             return $q->where([
    //                 'Orders.order_type' => 'Online',
    //                 'Orders.order_date >=' => $currentMonthStart,
    //                 'Orders.order_date <=' => $currentMonthEnd,
    //                 'Orders.status !=' => 'Cancelled'
    //             ]);
    //         })
    //         ->group(['Products.id'])
    //         ->order(['total_units_sold' => 'DESC'])
    //         ->limit(5)
    //         ->toArray();

    //     // Fetch top 5 showroom products
    //     $topShowroomProducts = $this->OrderItems->find()
    //         ->select([
    //             'Products.id',
    //             'Products.name',
    //             'total_sales_amount' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
    //             'total_units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
    //         ])
    //         ->contain([
    //             'Products' => [
    //                 'ProductImages' => function ($q) {
    //                     return $q->where([
    //                         'ProductImages.image_default' => 1,
    //                         'ProductImages.status' => 'A'
    //                     ]);
    //                 }
    //             ]
    //         ])
    //         ->matching('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
    //             return $q->where([
    //                 'Orders.order_type' => 'Showroom',
    //                 'Orders.order_date >=' => $currentMonthStart,
    //                 'Orders.order_date <=' => $currentMonthEnd,
    //                 'Orders.status !=' => 'Cancelled'
    //             ]);
    //         })
    //         ->group(['Products.id'])
    //         ->order(['total_units_sold' => 'DESC'])
    //         ->limit(5)
    //         ->toArray();

    //     // Combine both lists
    //     $topSellingProducts = [
    //         'online' => $topOnlineProducts,
    //         'showroom' => $topShowroomProducts
    //     ];

    //     foreach ($topSellingProducts as $channel => $products) {
    //         // Loop through each product in the channel
    //         foreach ($products as $product) {
    //             // Check if the product has images
    //             if (!empty($product->product->product_images)) {
    //                 // Convert image URL to CloudFront URL
    //                 $product->product->product_images[0]->image = $this->Media->getCloudFrontURL($product->product->product_images[0]->image);
    //             }
    //         }
    //     }


    //     /** FETCH TOP 5 PRODUCT CATEGORIES **/
    //     $totalCategorySalesQuery = $this->OrderItems->find()
    //         ->innerJoinWith('Products.ProductCategories.Categories')
    //         ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
    //             return $q->where([
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.status !=' => 'Cancelled'
    //             ]);
    //         })
    //         ->select([
    //             'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity')
    //         ])
    //         ->first();

    //     $totalCategorySales = $totalCategorySalesQuery ? $totalCategorySalesQuery->total_sales : 0;


    //     // Step 2: Fetch top 5 product categories and calculate percentage
    //     $topCategories = $this->OrderItems->find()
    //         ->select([
    //             'category_id' => 'Categories.id',
    //             'category_icon' => 'Categories.category_icon',
    //             'category_name' => 'Categories.name',
    //             'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
    //             'units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
    //         ])
    //         ->innerJoinWith('Products.ProductCategories.Categories') // Joining the necessary tables
    //         ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
    //             return $q->where([
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.status !=' => 'Cancelled'
    //             ]);
    //         })
    //         ->group(['Categories.id', 'Categories.name']) // Grouping by category
    //         ->order(['total_sales' => 'DESC']) // Ordering by sales
    //         ->limit(5) // Limiting to top 5 categories
    //         ->toArray();

    //     // Calculate percentage for progress bars
    //     foreach ($topCategories as &$category) {
    //         $category['percentage'] = $totalCategorySales > 0 ? ($category['total_sales'] / $totalCategorySales) * 100 : 0;

    //         if ($category['category_icon']) {
    //             $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
    //         }
    //     }

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $this->set(compact('totalOrders', 'onlineOrders', 'showroomOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage', 'monthNames', 'orderCounts', 'revenueMonthNames', 'revenueData', 'expensesData', 'supplier_payment', 'supplier_payment_pendings', 'topSellingProducts', 'topCategories', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    // }

    // public function showroomManagerDashboard()
    // {
    //     $user = $this->Authentication->getIdentity();

    //     /** GET SHOWROOM DETAILS **/
    //     $showroom_detail = $this->Showrooms->find()
    //         ->where(['Showrooms.showroom_manager' => $user->id])
    //         ->first();

    //     if(!empty($showroom_detail))
    //     {
    //         $noActiveShowroomsMessage = null;

    //         /** Get current month start and end date **/
    //         $currentMonthStart = FrozenTime::now()->startOfMonth();
    //         $currentMonthEnd = FrozenTime::now()->endOfMonth();

    //         $totalOrdersQuery = $this->Orders->find()
    //             ->where([
    //                 'Orders.status !=' => 'Cancelled',
    //                 'Orders.showroom_id' => $showroom_detail->id,
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.order_type' => 'Showroom'
    //             ]);

    //         /** Get the total number of orders for the current month **/
    //         $totalOrders = $totalOrdersQuery->count();

    //         // Total sales amount for the current month
    //         // Calculate total sales amount for the current month
    //         $totalSalesAmountQuery = $this->Orders->find()
    //             ->where([
    //                 'Orders.status !=' => 'Cancelled',
    //                 'Orders.showroom_id' => $showroom_detail->id,
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.order_type' => 'Showroom'
    //             ])
    //             ->select([
    //                 'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
    //             ])
    //             ->first();

    //         $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

    //         // Get the total number of all-time orders (excluding Cancelled orders)
    //         $allTimeOrdersQuery = $this->Orders->find()
    //             ->where([
    //                 'Orders.status !=' => 'Cancelled',
    //                 'Orders.showroom_id' => $showroom_detail->id
    //             ]);

    //         $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //         // Calculate the percentage of orders in the current month
    //         $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //         $userQuery = $this->Users->find()
    //             ->contain(['Roles'])
    //             ->where(['Users.status' => 'A'])
    //             ->order(['Users.first_name' => 'ASC']);

    //         // Filter by role if provided
    //         $roleId = $this->request->getQuery('role');
    //         if ($roleId) {
    //             $userQuery->where(['Users.role_id' => $roleId]);
    //         }

    //         // Get the total count of active users
    //         $totalUsers = $userQuery->count();

    //         $newUsers = 0;
    //         if ($currentMonthStart && $currentMonthEnd) {
    //             $users = $userQuery->all(); // Retrieve all users
    //             // echo "<pre>";print_r($users);die;
    //             foreach ($users as $user) {
    //                 $createdDate = $user->created->format('Y-m-d');
    //                 // echo "<pre>";print_r($createdDate);
    //                 // echo "<pre>";print_r($startDate->format('Y-m-d'));
    //                 if ($createdDate >= $currentMonthStart->format('Y-m-d')) {
    //                     $newUsers++;
    //                 }
    //             }
    //         } else {
    //             // If no date range is provided, new users count is zero
    //             $newUsers = 0;
    //         }

    //         // Calculate the percentage of new users
    //         $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //         /** Fetch total active products **/
    //         $totalActiveProducts = $this->ProductStocks->find()
    //             ->contain(['Products'])
    //             ->where([
    //                 'ProductStocks.showroom_id' => $showroom_detail->id,
    //                 'Products.status' => 'A' // Assuming 'status' is in the 'Products' table
    //             ])
    //             ->count();

    //         $todayDate = FrozenTime::now();
    //         $startOfMonth = $todayDate->startOfMonth();

    //         $newProducts = $this->ProductStocks->find()
    //             ->contain(['Products'])
    //             ->where([
    //                 'ProductStocks.showroom_id' => $showroom_detail->id,
    //                 'Products.status' => 'A', // Filter active products
    //                 'Products.created >=' => $startOfMonth
    //             ])
    //             ->count();

    //         /** Calculate the percentage of new products **/
    //         $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //         /** FETCH Order Trends: **/

    //         // Get the current date and calculate the date 14 days ago
    //         // Get the current date (start of today)
    //         $startDate = FrozenTime::today()->subDays(13)->startOfDay(); // 13 days ago from today
    //         $endDate = FrozenTime::today()->endOfDay(); // Include today's end of day

    //         $showroomId = $showroom_detail->id;

    //         // Query to get the number of orders per day for the last 14 days
    //         $orderData = $this->Orders->find()
    //             ->select([
    //                 'created' => 'Orders.created',
    //                 'order_count' => $this->Orders->find()->func()->count('Orders.id')
    //             ])
    //             ->where([
    //                 'Orders.created >=' => $startDate,
    //                 'Orders.created <=' => $endDate,
    //                 'Orders.status !=' => 'Cancelled',
    //                 'Orders.showroom_id' => $showroom_detail->id
    //             ])
    //             ->group(['created'])  // Group by the date
    //             ->order(['created' => 'ASC'])  // Sort by date ascending
    //             ->enableHydration(false)  // Fetch as an array instead of entities
    //             ->toArray();

    //         $mergedData = [];

    //         // Loop through the fetched data to merge date and order count
    //         foreach ($orderData as $data) {
    //             // Ensure that the created date is formatted as YYYY-MM-DD
    //             $dateKey = (new FrozenTime($data['created']))->i18nFormat('yyyy-MM-dd');  // Format the date

    //             // Accumulate the order count for the same date
    //             if (isset($mergedData[$dateKey])) {
    //                 $mergedData[$dateKey] += (int)$data['order_count'];  // Add to existing count
    //             } else {
    //                 $mergedData[$dateKey] = (int)$data['order_count'];  // Set initial count
    //             }
    //         }

    //         // Prepare the final data structure for the graph
    //         $finalOrderTrendsData = [];
    //         $currentDate = FrozenTime::today();

    //         // Loop through the last 14 days to fill in the counts
    //         for ($i = 13; $i >= 0; $i--) {
    //             $dateKey = $currentDate->subDays($i)->i18nFormat('yyyy-MM-dd');  // Get the date key
    //             // Set the count to either the value found or 0 if not found
    //             $finalOrderTrendsData[$dateKey] = $mergedData[$dateKey] ?? 0;
    //         }


    //         /** FETCH REVENUE AND EXPENSES **/
    //         $startRevenueDate = FrozenTime::now()->subMonths(6)->startOfMonth();
    //         $endRevenueDate = FrozenTime::now()->endOfMonth();

    //         // Define the last 6 months from the current month, only month names
    //         $monthData = [];
    //         for ($i = 5; $i >= 0; $i--) {
    //             $month = $currentDate->subMonths($i)->format('F'); // Format as 'Month' only
    //             $monthData[] = $month;
    //         }

    //         // Fetch revenue data
    //         $revenueQuery = $this->Transactions->find()
    //             ->select([
    //                 'month' => 'MONTH(Transactions.created)',
    //                 'total_revenue' => $this->Transactions->find()->func()->sum('Transactions.amount')
    //             ])
    //             ->innerJoinWith('Orders', function ($q) use ($showroomId) {
    //                 return $q->where([
    //                     'Orders.status !=' => 'Cancelled',
    //                     'Orders.showroom_id' => $showroomId
    //                 ]);
    //             })
    //             ->where([
    //                 'Transactions.created >=' => $startRevenueDate,
    //                 'Transactions.created <=' => $endRevenueDate
    //             ])
    //             ->group(['month'])
    //             ->order(['month' => 'DESC'])
    //             ->toArray();

    //         // Fetch expenses data
    //         $expensesQuery = $this->SupplierPayment->find()
    //             ->select([
    //                 'month' => 'MONTH(SupplierPayment.created)',
    //                 'total_expenses' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')
    //             ])
    //             ->innerJoinWith('SupplierPurchaseOrders', function ($q) {
    //                 return $q->where(['SupplierPurchaseOrders.status' => 'A']);
    //             })
    //             ->where([
    //                 'SupplierPayment.created >=' => $startRevenueDate,
    //                 'SupplierPayment.created <=' => $endRevenueDate,
    //                 'SupplierPayment.showroom_id' => $showroomId
    //             ])
    //             ->group(['month'])
    //             ->order(['month' => 'DESC'])
    //             ->toArray();

    //         // Fetch ShowroomExpenses
    //         $showroomExpensesQuery = $this->ShowroomExpenses->find()
    //             ->select([
    //                 'month' => 'MONTH(ShowroomExpenses.created)',
    //                 'total_expenses' => $this->ShowroomExpenses->find()->func()->sum('ShowroomExpenses.amount')
    //             ])
    //             ->where([
    //                 'ShowroomExpenses.created >=' => $startRevenueDate,
    //                 'ShowroomExpenses.created <=' => $endRevenueDate,
    //                 'ShowroomExpenses.showroom_id' => $showroomId,
    //                 'ShowroomExpenses.status' => 'A'
    //             ])
    //             ->group(['month'])
    //             ->order(['month' => 'DESC'])
    //             ->toArray();

    //         // Process revenue data and map to each month
    //         $revenueData = array_fill(0, 6, 0); // Initialize with zeros
    //         foreach ($revenueQuery as $data) {
    //             $monthName = \DateTime::createFromFormat('!m', $data->month)->format('F');
    //             $monthIndex = array_search($monthName, $monthData);
    //             if ($monthIndex !== false) {
    //                 $revenueData[$monthIndex] = $data->total_revenue / 1000;  // Convert to thousands
    //             }
    //         }

    //         // Process expenses data and map to each month
    //         $expensesData = array_fill(0, 6, 0); // Initialize with zeros
    //         foreach ($expensesQuery as $data) {
    //             $monthName = \DateTime::createFromFormat('!m', $data->month)->format('F');
    //             $monthIndex = array_search($monthName, $monthData);
    //             if ($monthIndex !== false) {
    //                 $expensesData[$monthIndex] = $data->total_expenses / 1000;  // Convert to thousands
    //             }
    //         }

    //         // Merge ShowroomExpenses into expenses data
    //         // foreach ($showroomExpensesQuery as $data) {
    //         //     $monthName = \DateTime::createFromFormat('!m', $data->month)->format('F');
    //         //     $monthIndex = array_search($monthName, $monthData);

    //         //     echo "<pre>";print_r($data);
    //         //     echo "<pre>";print_r($monthIndex);

    //         //     if ($monthIndex !== false) {
    //         //         $expensesData[$monthIndex] += $data->total_expenses / 1000; // Add and convert to thousands
    //         //     }
    //         // }

    //         $monthIndexMap = array_flip($monthData); // Create lookup map for month names

    //         foreach ($showroomExpensesQuery as $data) {
    //             $monthName = \DateTime::createFromFormat('!m', $data->month)->format('F');
    //             $monthIndex = $monthIndexMap[$monthName] ?? null;

    //             if ($monthIndex !== null) {
    //                 // Overwrite instead of accumulating
    //                 $expensesData[$monthIndex] = $data->total_expenses / 1000;
    //             }
    //         }

    //         /** PENDING ACTIONS **/
    //         /** 1) SUPPLIER PAYMENTS **/

    //         // Fetch pending purchase orders

    //         // Fetch pending bills using the model directly
    //         $pendingBills = $this->SupplierPurchaseOrders->find()
    //             ->contain([
    //                 'SupplierPurchaseOrdersItems' => function ($q) {
    //                     return $q->contain(['Products'])
    //                          ->select([
    //                             'SupplierPurchaseOrdersItems.product_id',
    //                             'SupplierPurchaseOrdersItems.product_variant_id',
    //                             'SupplierPurchaseOrdersItems.product_attribute_id',
    //                             'SupplierPurchaseOrdersItems.approved_quantity',
    //                             'SupplierPurchaseOrdersItems.quantity',
    //                             'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
    //                         ]);
    //                 }
    //             ])
    //             ->where([
    //                 'SupplierPurchaseOrders.payment_status' => 'Pending',
    //                 'SupplierPurchaseOrders.status IN' => ['A', 'P'],
    //                 'SupplierPurchaseOrders.deliver_to' => 'showroom',
    //                 'SupplierPurchaseOrders.id_deliver_to' => $showroomId
    //             ])
    //             ->all();

    //         $supplier_payment_pendings = [];

    //         // Loop through each purchase order and calculate pending amount
    //         foreach ($pendingBills as $purchaseOrder) {
    //             $totalPendingAmount = 0;

    //             foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {

    //                 // Fetch supplier price
    //                 // $supplierPrice = $this->SupplierProducts->find()
    //                 //     ->select(['supplier_price'])
    //                 //     ->where([
    //                 //         'supplier_id' => $purchaseOrder->supplier_id,
    //                 //         'product_id' => $product->product_id
    //                 //     ])
    //                 //     ->first();

    //                 $supplier_price_conditions = [
    //                     'supplier_id' => $purchaseOrder->supplier_id,
    //                     'product_id' => $product->product_id
    //                 ];

    //                 // Check if product_variant_id exists and is not null
    //                 if (!empty($product->product_variant_id)) {
    //                     $supplier_price_conditions['product_variant_id'] = $product->product_variant_id;
    //                 }

    //                 $supplierPrice = $this->SupplierProducts->find()
    //                     ->select(['supplier_price'])
    //                     ->where($supplier_price_conditions)
    //                     ->first();

    //                 // Calculate total pending amount for each product
    //                 if ($supplierPrice) {
    //                     $quantity = ($purchaseOrder->status === 'A')
    //                         ? $product->approved_quantity
    //                         : $product->quantity;

    //                     $totalPendingAmount += $supplierPrice->supplier_price * $quantity;
    //                 }
    //             }

    //             $totalPendingAmount = number_format((float)$totalPendingAmount, 2, '.', '');

    //             // Store Bill No and pending amount
    //             $supplier_payment_pendings[] = [
    //                 'bill_number' => $purchaseOrder->bill_no,
    //                 'pending_amount' => $totalPendingAmount
    //             ];
    //         }

    //         /** TOP 5 supplier Payments **/
    //         $supplier_payment = $this->SupplierPayment->find()
    //             ->contain([
    //                 'Suppliers','SupplierPurchaseOrders'
    //                 ])
    //             ->where([
    //                 'SupplierPayment.showroom_id' => $showroomId
    //             ])
    //             ->order([
    //                 'SupplierPayment.id' => 'DESC'
    //             ])->limit(5)->toArray();

    //         /** TOP 3 SHOWROOMS **/

    //         $totalSalesQuery = $this->Orders->find()
    //             ->select([
    //                 'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
    //             ])
    //             ->where([
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.status !=' => 'Cancelled'
    //             ])
    //             ->first();

    //         $totalSales = $totalSalesQuery ? $totalSalesQuery->total_sales : 0;

    //         // Step 2: Get the top 3 showrooms with percentage
    //         $topShowrooms = $this->Orders->find()
    //             ->select([
    //                 'showroom_id',
    //                 'Showrooms.name',
    //                 'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount'),
    //                 'order_count' => $this->Orders->find()->func()->count('Orders.id')
    //                 // 'sales_percentage' => 'SUM(Orders.total_amount) * 100 / ' . $totalSales
    //             ])
    //             ->where([
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.status !=' => 'Cancelled',
    //                 'Orders.showroom_id IS NOT NULL'
    //             ])
    //             ->group('Orders.showroom_id')
    //             ->order(['total_sales' => 'DESC'])
    //             ->limit(3)
    //             ->contain(['Showrooms'])
    //             ->toArray();

    //         foreach ($topShowrooms as &$showroom) {
    //             $showroom->sales_percentage = $totalSales > 0 ? ($showroom->total_sales * 100) / $totalSales : 0;
    //         }

    //         /** FETCH TOP 5 PRODUCT CATEGORIES **/
    //         $totalCategorySalesQuery = $this->OrderItems->find()
    //             ->innerJoinWith('Products.ProductCategories.Categories')
    //             ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd, $showroomId) {
    //                 return $q->where([
    //                     'Orders.created >=' => $currentMonthStart,
    //                     'Orders.created <=' => $currentMonthEnd,
    //                     'Orders.status !=' => 'Cancelled',
    //                     'Orders.showroom_id' => $showroomId
    //                 ]);
    //             })
    //             ->select([
    //                 'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity')
    //             ])
    //             ->first();

    //         $totalCategorySales = $totalCategorySalesQuery ? $totalCategorySalesQuery->total_sales : 0;

    //         // Step 2: Fetch top 5 product categories and calculate percentage
    //         $topCategories = $this->OrderItems->find()
    //             ->select([
    //                 'category_id' => 'Categories.id',
    //                 'category_icon' => 'Categories.category_icon',
    //                 'category_name' => 'Categories.name',
    //                 'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
    //                 'units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
    //             ])
    //             ->innerJoinWith('Products.ProductCategories.Categories') // Joining the necessary tables
    //             ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd, $showroomId) {
    //                 return $q->where([
    //                     'Orders.created >=' => $currentMonthStart,
    //                     'Orders.created <=' => $currentMonthEnd,
    //                     'Orders.status !=' => 'Cancelled',
    //                     'Orders.showroom_id' => $showroomId
    //                 ]);
    //             })
    //             ->group(['Categories.id', 'Categories.name']) // Grouping by category
    //             ->order(['total_sales' => 'DESC']) // Ordering by sales
    //             ->limit(5) // Limiting to top 5 categories
    //             ->toArray();

    //         // Calculate percentage for progress bars
    //         foreach ($topCategories as &$category) {
    //             $category['percentage'] = $totalCategorySales > 0 ? ($category['total_sales'] / $totalCategorySales) * 100 : 0;

    //             if ($category['category_icon']) {
    //                 $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
    //             }
    //         }
    //     }
    //     else
    //     {
    //         $noActiveShowroomsMessage = __('No showroom are assigned to you.');
    //         $showroom_detail = null;
    //         $totalOrders = 0;
    //         $totalSalesAmount = 0;
    //         $percentageOrders = 0;
    //         $totalUsers = 0;
    //         $newUsers = 0;
    //         $newUsersPercentage = 0;
    //         $totalActiveProducts = 0;
    //         $newProducts = 0;
    //         $newProductsPercentage = 0;
    //         $finalOrderTrendsData = [];
    //         $monthData = [];
    //         $revenueData = [];
    //         $expensesData = [];
    //         $supplier_payment = [];
    //         $supplier_payment_pendings = [];
    //         $topShowrooms = [];
    //         $topCategories = [];
    //     }

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $this->set(compact('showroom_detail', 'totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'finalOrderTrendsData', 'monthData', 'revenueData', 'expensesData', 'supplier_payment_pendings', 'supplier_payment', 'topShowrooms', 'topCategories', 'currencySymbol', 'noActiveShowroomsMessage', 'decimalSeparator', 'thousandSeparator'));
    // }

    // public function supervisorManagerDashboard()
    // {
    //     $user = $this->Authentication->getIdentity();

    //     $supervisorId = $user->id;

    //     /** GET SUPERVISOR DETAILS **/
    //     $supervisor_detail = $this->Showrooms->find()
    //         ->where(['Showrooms.showroom_manager' => $user->id])
    //         ->first();

    //     // Get current month start and end date
    //     $currentMonthStart = FrozenTime::now()->startOfMonth();
    //     $currentMonthEnd = FrozenTime::now()->endOfMonth();

    //     // Fetch the active zones managed by the supervisor
    //     $activeShowrooms = $this->Showrooms->find()
    //         ->select(['id'])
    //         ->where(['showroom_supervisor' => $supervisorId])
    //         ->toArray();

    //     // Check if there are no active showrooms
    //     if (!empty($activeShowrooms))
    //     {

    //         $noActiveShowroomsMessage = null;
    //         // Extract zone IDs
    //         $showroomIds = Hash::extract($activeShowrooms, '{n}.id');

    //         // Query for total orders and total sales amount for the current month
    //         $totalOrdersQuery = $this->Orders->find()
    //             ->where([
    //                 'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.order_type' => 'Showroom',
    //                 'Orders.showroom_id IN' => $this->Showrooms->find()
    //                     ->select(['id'])
    //                     ->where(['Showrooms.id IN' => $showroomIds])
    //             ]);

    //         // Get the total number of orders for the current month
    //         $totalOrders = $totalOrdersQuery->count();

    //         // Total sales amount for the current month
    //         $totalSalesAmountQuery = $this->Orders->find()
    //             ->select([
    //                 'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
    //             ])
    //             ->where([
    //                 'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.order_type' => 'Showroom',
    //                 'Orders.showroom_id IN' => $this->Showrooms->find()
    //                     ->select(['id'])
    //                     ->where(['Showrooms.id IN' => $showroomIds])
    //             ])
    //             ->first();


    //         $totalSalesAmount = $totalSalesAmountQuery ? $totalSalesAmountQuery->total_sales : 0;

    //         // Get the total number of all-time orders (excluding Cancelled orders)
    //         $allTimeOrdersQuery = $this->Orders->find()
    //             ->where(['Orders.status !=' => 'Cancelled', 'Orders.showroom_id IN' => $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.id IN' => $showroomIds])
    //             ]);


    //         $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //         // Calculate the percentage of orders in the current month
    //         $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //         $userQuery = $this->Users->find()
    //             ->contain(['Roles'])
    //             ->where(['Users.status' => 'A'])
    //             ->order(['Users.first_name' => 'ASC']);

    //         // Filter by role if provided
    //         $roleId = $this->request->getQuery('role');
    //         if ($roleId) {
    //             $userQuery->where(['Users.role_id' => $roleId]);
    //         }

    //         // Get the total count of active users
    //         $totalUsers = $userQuery->count();

    //         $newUsers = 0;
    //         if ($currentMonthStart && $currentMonthEnd) {
    //             $users = $userQuery->all(); // Retrieve all users

    //             foreach ($users as $user) {
    //                 $createdDate = $user->created->format('Y-m-d');

    //                 if ($createdDate >= $currentMonthStart->format('Y-m-d')) {
    //                     $newUsers++;
    //                 }
    //             }
    //         } else {
    //             // If no date range is provided, new users count is zero
    //             $newUsers = 0;
    //         }

    //         // Calculate the percentage of new users
    //         $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //         // Fetch total active products
    //         $totalActiveProducts = $this->Products->find()
    //             ->where([
    //                 'status' => 'A'
    //                 // 'created >=' => $currentMonthStart,
    //                 // 'created <=' => $currentMonthEnd
    //             ])
    //             ->count();

    //         // Fetch number of new products added in the current month
    //         $newProducts = $this->Products->find()
    //             ->where([
    //                 'status' => 'A',
    //                 'created >=' => $currentMonthStart,
    //                 'created <=' => $currentMonthEnd
    //             ])
    //             ->count();

    //         // Calculate the percentage of new products
    //         $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //         // Get the total number of active showrooms
    //         $totalShowrooms = $this->Showrooms->find()
    //             ->where([
    //                 'status' => 'A'
    //                 // 'created >=' => $currentMonthStart,
    //                 // 'created <=' => $currentMonthEnd
    //             ])
    //             ->count();

    //         $newShowrooms = $this->Showrooms->find()
    //             ->where([
    //                 'status' => 'A',
    //                 'created >=' => $currentMonthStart,
    //                 'created <=' => $currentMonthEnd
    //             ])
    //             ->count();

    //         // Calculate the percentage of new showrooms
    //         $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

    //         $currentDate = new \DateTime(); // Current date
    //         $startDate = (clone $currentDate)->modify('-5 months')->format('Y-m-01'); // Start from the first day of 6 months ago

    //         $showrooms = $this->Showrooms->find()
    //             ->where(['Showrooms.showroom_supervisor' => $supervisorId, 'Showrooms.status' => 'A'])
    //             ->all()
    //             ->toArray();

    //         $orderData = [];
    //         foreach ($showrooms as $showroom) {
    //             $orders = $this->Orders->find()
    //                 ->select([
    //                     'month' => 'MONTH(Orders.created)',
    //                     'year' => 'YEAR(Orders.created)',
    //                     'order_count' => $this->Orders->find()->func()->count('*')
    //                 ])
    //                 ->where([
    //                     'Orders.showroom_id' => $showroom->id,
    //                     'Orders.status !=' => 'Cancelled',
    //                     'Orders.created >=' => $startDate
    //                 ])
    //                 ->group(['year', 'month'])
    //                 ->order(['year' => 'ASC', 'month' => 'ASC'])
    //                 ->toArray();

    //             $orderData[$showroom->name] = $orders;
    //         }

    //         $orderTrendsgraphData = [];
    //         $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    //         $currentDate = new \DateTime();
    //         $monthLabels = [];
    //         for ($i = 5; $i >= 0; $i--) {
    //             $date = (clone $currentDate)->modify("-$i months");
    //             $monthLabels[] = $months[$date->format('n') - 1]; // Get month name and year
    //         }

    //         foreach ($orderData as $showroomName => $orders) {
    //             $orderCounts = array_fill(0, 6, 0); // Initialize an array with 6 slots (one for each month)

    //             foreach ($orders as $order) {
    //                 // Get the month index for the current order (0 = current month, 1 = previous month, etc.)
    //                 $orderMonthIndex = array_search($months[$order['month'] - 1], $monthLabels);
    //                 if ($orderMonthIndex !== false) {
    //                     $orderCounts[$orderMonthIndex] += $order['order_count'];
    //                 }
    //             }

    //             $orderTrendsgraphData[] = [
    //                 'name' => $showroomName,
    //                 'data' => $orderCounts
    //             ];
    //         }

    //         /** FETCH REVENUE TRENDS **/
    //         $revenueStartDate = (new \DateTime())->modify('-5 months')->format('Y-m-01');
    //         $revenueEndDate = (new \DateTime())->format('Y-m-t');

    //         // Fetch revenue trends for the last 6 months for each showroom
    //         $revenueData = $this->Orders->find()
    //             ->select([
    //                 'month' => 'MONTH(Orders.created)',
    //                 'year' => 'YEAR(Orders.created)',
    //                 'showroom_id',
    //                 'total_revenue' => $this->Orders->find()->func()->sum('Orders.total_amount')
    //             ])
    //             ->where([
    //                 'Orders.created >=' => $revenueStartDate,
    //                 'Orders.created <=' => $revenueEndDate,
    //                 'Orders.status !=' => 'Cancelled',
    //                 'Orders.showroom_id IN' => array_column($showrooms, 'id')
    //             ])
    //             ->group(['year', 'month', 'showroom_id'])
    //             ->order(['year' => 'ASC', 'month' => 'ASC'])
    //             ->toArray();

    //         $revenueFormattedData = [];
    //         $revenueMonths = [];

    //         // Create the month labels (Jan, Feb, etc.)
    //         for ($i = 5; $i >= 0; $i--) {
    //             $revenueMonths[] = (new \DateTime())->modify("-{$i} months")->format('M');
    //         }

    //         foreach ($showrooms as $showroom) {
    //             $revenueForShowroom = [];
    //             foreach ($revenueMonths as $month) {
    //                 // Find revenue for each month and showroom
    //                 $revenue = 0;
    //                 foreach ($revenueData as $data) {
    //                     if ($data['showroom_id'] == $showroom->id && date('M', mktime(0, 0, 0, $data['month'], 10)) === $month) {
    //                         $revenue = $data['total_revenue'];
    //                         break;
    //                     }
    //                 }
    //                 $revenueForShowroom[] = $revenue;
    //             }
    //             $revenueFormattedData[] = [
    //                 'showroom' => $showroom->name,
    //                 'revenue' => $revenueForShowroom
    //             ];
    //         }

    //         // Get the showroom IDs
    //         $showroomIds = array_column($showrooms, 'id');

    //         // Step 2: Fetch payment data from SupplierPurchaseOrders and SupplierPayments
    //         $supplier_payments = $this->SupplierPayment->find()
    //             ->select([
    //                 'Suppliers.name',
    //                 'SupplierPurchaseOrders.payment_status',
    //                 'SupplierPayment.amount',
    //                 'SupplierPayment.showroom_id'
    //             ])
    //             ->contain([
    //                 'Suppliers',
    //                 'SupplierPurchaseOrders' => [
    //                     'conditions' => [
    //                         'SupplierPurchaseOrders.id = SupplierPayment.supplier_purchase_order_id',
    //                         'SupplierPurchaseOrders.status' => 'A'
    //                     ]
    //                 ]
    //             ])
    //             ->where([
    //                 'SupplierPayment.showroom_id IN' => $showroomIds,
    //             ])
    //             ->order(['SupplierPayment.id' => 'DESC'])
    //             ->toArray();


    //         /** PENDING ACTIONS **/

    //         /** 1) SUPPLIER PAYMENTS **/
    //         $pendingBills = $this->SupplierPurchaseOrders->find()
    //             ->contain([
    //                 'SupplierPurchaseOrdersItems' => function ($q) {
    //                     return $q->contain(['Products'])
    //                          ->select([
    //                             'SupplierPurchaseOrdersItems.product_id',
    //                             'SupplierPurchaseOrdersItems.product_variant_id',
    //                             'SupplierPurchaseOrdersItems.product_attribute_id',
    //                             'SupplierPurchaseOrdersItems.approved_quantity',
    //                             'SupplierPurchaseOrdersItems.quantity',
    //                             'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
    //                         ]);
    //                 }
    //             ])
    //             ->where([
    //                 'SupplierPurchaseOrders.payment_status' => 'Pending',
    //                 'SupplierPurchaseOrders.status IN' => ['A', 'P'],
    //                 'SupplierPurchaseOrders.deliver_to' => 'showroom',
    //                 'SupplierPurchaseOrders.id_deliver_to IN' => $showroomIds
    //             ])
    //             ->all();

    //         $supplier_payment_pendings = [];

    //         // Loop through each purchase order and calculate pending amount
    //         foreach ($pendingBills as $purchaseOrder) {
    //             $totalPendingAmount = 0;

    //             foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {


    //                 // Fetch supplier price
    //                 // $supplierPrice = $this->SupplierProducts->find()
    //                 //     ->select(['supplier_price'])
    //                 //     ->where([
    //                 //         'supplier_id' => $purchaseOrder->supplier_id,
    //                 //         'product_id' => $product->product_id
    //                 //     ])
    //                 //     ->first();

    //                 $supplier_price_conditions = [
    //                     'supplier_id' => $purchaseOrder->supplier_id,
    //                     'product_id' => $product->product_id
    //                 ];

    //                 // Check if product_variant_id exists and is not null
    //                 if (!empty($product->product_variant_id)) {
    //                     $supplier_price_conditions['product_variant_id'] = $product->product_variant_id;
    //                 }

    //                 $supplierPrice = $this->SupplierProducts->find()
    //                     ->select(['supplier_price'])
    //                     ->where($supplier_price_conditions)
    //                     ->first();

    //                 // Calculate total pending amount for each product
    //                 if ($supplierPrice) {
    //                     $quantity = ($purchaseOrder->status === 'A')
    //                         ? $product->approved_quantity
    //                         : $product->quantity;

    //                     $totalPendingAmount += $supplierPrice->supplier_price * $quantity;
    //                 }
    //             }

    //             $totalPendingAmount = number_format((float)$totalPendingAmount, 2, '.', '');

    //             // Store Bill No and pending amount
    //             $supplier_payment_pendings[] = [
    //                 'bill_number' => $purchaseOrder->bill_no,
    //                 'pending_amount' => $totalPendingAmount
    //             ];
    //         }

    //         /** TOP 3 SHOWROOMS **/

    //         $totalSalesQuery = $this->Orders->find()
    //             ->select([
    //                 'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
    //             ])
    //             ->where([
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.status !=' => 'Cancelled'
    //             ])
    //             ->first();

    //         $totalSales = $totalSalesQuery ? $totalSalesQuery->total_sales : 0;

    //         // Step 2: Get the top 3 showrooms with percentage
    //         $topShowrooms = $this->Orders->find()
    //             ->select([
    //                 'showroom_id',
    //                 'Showrooms.name',
    //                 'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount'),
    //                 'order_count' => $this->Orders->find()->func()->count('Orders.id')
    //                 // 'sales_percentage' => 'SUM(Orders.total_amount) * 100 / ' . $totalSales
    //             ])
    //             ->where([
    //                 'Orders.created >=' => $currentMonthStart,
    //                 'Orders.created <=' => $currentMonthEnd,
    //                 'Orders.status !=' => 'Cancelled',
    //                 'Orders.showroom_id IS NOT NULL'
    //             ])
    //             ->group('Orders.showroom_id')
    //             ->order(['total_sales' => 'DESC'])
    //             ->limit(3)
    //             ->contain(['Showrooms'])
    //             ->toArray();

    //         foreach ($topShowrooms as &$showroom) {
    //             $showroom->sales_percentage = $totalSales > 0 ? ($showroom->total_sales * 100) / $totalSales : 0;
    //         }

    //         /** FETCH TOP 5 PRODUCT CATEGORIES **/
    //         $totalCategorySalesQuery = $this->OrderItems->find()
    //             ->innerJoinWith('Products.ProductCategories.Categories')
    //             ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
    //                 return $q->where([
    //                     'Orders.created >=' => $currentMonthStart,
    //                     'Orders.created <=' => $currentMonthEnd,
    //                     'Orders.status !=' => 'Cancelled'
    //                 ]);
    //             })
    //             ->select([
    //                 'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity')
    //             ])
    //             ->first();

    //         $totalCategorySales = $totalCategorySalesQuery ? $totalCategorySalesQuery->total_sales : 0;

    //         // Step 2: Fetch top 5 product categories and calculate percentage
    //         $topCategories = $this->OrderItems->find()
    //             ->select([
    //                 'category_id' => 'Categories.id',
    //                 'category_icon' => 'Categories.category_icon',
    //                 'category_name' => 'Categories.name',
    //                 'total_sales' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity'),
    //                 'units_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
    //             ])
    //             ->innerJoinWith('Products.ProductCategories.Categories')
    //             ->innerJoinWith('Orders', function ($q) use ($currentMonthStart, $currentMonthEnd) {
    //                 return $q->where([
    //                     'Orders.created >=' => $currentMonthStart,
    //                     'Orders.created <=' => $currentMonthEnd,
    //                     'Orders.status !=' => 'Cancelled'
    //                 ]);
    //             })
    //             ->group(['Categories.id', 'Categories.name']) // Grouping by category
    //             ->order(['total_sales' => 'DESC']) // Ordering by sales
    //             ->limit(5) // Limiting to top 5 categories
    //             ->toArray();

    //         // Calculate percentage for progress bars
    //         foreach ($topCategories as &$category) {
    //             $category['percentage'] = $totalCategorySales > 0 ? ($category['total_sales'] / $totalCategorySales) * 100 : 0;

    //             if ($category['category_icon']) {
    //                 $category['category_icon'] = $this->Media->getCloudFrontURL($category['category_icon']);
    //             }
    //         }
    //     }
    //     else
    //     {
    //         $noActiveShowroomsMessage = __('No showrooms are assigned to you.');
    //         $totalOrders = 0;
    //         $totalSalesAmount = 0;
    //         $percentageOrders = 0;
    //         $totalUsers = 0;
    //         $newUsers = 0;
    //         $newUsersPercentage = 0;
    //         $totalActiveProducts = 0;
    //         $newProducts = 0;
    //         $newProductsPercentage = 0;
    //         $totalShowrooms = 0;
    //         $newShowrooms = 0;
    //         $newShowroomsPercentage = 0;
    //         $orderTrendsgraphData = [];
    //         $monthLabels = [];
    //         $revenueMonths = [];
    //         $revenueFormattedData = [];
    //         $supplier_payments = [];
    //         $supplier_payment_pendings = [];
    //         $topShowrooms = [];
    //         $topCategories = [];
    //     }

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $this->set(compact('totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage', 'orderTrendsgraphData', 'monthLabels', 'revenueMonths', 'revenueFormattedData' ,'supplier_payments', 'supplier_payment_pendings', 'topShowrooms' ,'topCategories', 'currencySymbol', 'noActiveShowroomsMessage', 'decimalSeparator', 'thousandSeparator'));
    // }

    // public function warehouseManagerDashboard()
    // {

    //     $user = $this->Authentication->getIdentity();

    //     /** GET WAREHOUSE DETAILS **/
    //     $warehouse_detail = $this->Warehouses->find()
    //         ->where(['Warehouses.manager_id' => $user->id, 'Warehouses.status' => 'A'])
    //         ->first();

    //     if(!empty($warehouse_detail))
    //     {
    //         $noActiveWarehouseMessage = null;

    //         // Fetch warehouse_id for the manager (e.g., from session or role assignment)
    //         $warehouseId = $warehouse_detail->id;

    //         // Query to calculate total stocks and low-stock items
    //         $totalStocks = $this->ProductStocks->find()
    //             ->select(['total' => 'SUM(quantity)'])
    //             ->where(['warehouse_id' => $warehouseId])
    //             ->first()
    //             ->total;

    //         $lowStockCount = $this->ProductStocks->find()
    //             ->where(['warehouse_id' => $warehouseId, 'quantity <' => 10]) // Threshold for low stock
    //             ->count();

    //         // Calculate percentage of low-stock items
    //         $totalProducts = $this->ProductStocks->find()
    //             ->where(['warehouse_id' => $warehouseId])
    //             ->count();

    //         $percentageStocks = $totalProducts > 0 ? ($lowStockCount / $totalProducts) * 100 : 0;

    //         // Fetch pending stock requests
    //         $pendingRequests = $this->StockRequests->find()
    //             ->where([
    //                 'StockRequests.requestor_type' => 'Warehouse',
    //                 'StockRequests.status' => 'A',
    //                 'StockRequests.request_status' => 'Pending',
    //                 'StockRequests.warehouse_id' => $warehouseId
    //             ])
    //             ->count();

    //         // Fetch completed stock requests
    //         $completedRequests = $this->StockRequests->find()
    //             ->where([
    //                 'StockRequests.requestor_type' => 'Warehouse',
    //                 'StockRequests.status' => 'A',
    //                 'StockRequests.request_status' => 'Completed',
    //                 'StockRequests.warehouse_id' => $warehouseId
    //             ])
    //             ->count();

    //         // Calculate total requests
    //         $totalRequests = $pendingRequests + $completedRequests;

    //         // Percentage of completed requests
    //         $percentageCompleted = $totalRequests > 0 ? ($completedRequests / $totalRequests) * 100 : 0;

    //         /** OUTGOING STOCKS **/
    //         // Fetch dispatched products from StockMovements and StockMovementItems
    //         $dispatchedProducts = $this->StockMovements->find()
    //             ->contain(['StockMovementItems'])
    //             ->where([
    //                 'StockMovements.movement_type' => 'Outgoing',
    //                 'StockMovements.warehouse_id' => $warehouseId
    //             ])
    //             ->all();

    //         // Calculate total dispatched items
    //         $totalDispatchedItems = 0;
    //         foreach ($dispatchedProducts as $movement) {
    //             foreach ($movement->stock_movement_items as $item) {
    //                 $totalDispatchedItems += $item->quantity;
    //             }
    //         }

    //         // Define the total capacity or target for outgoing items (you can adjust this as per your need)
    //         $totalCapacity = 1000; // Example total capacity or target

    //         // Calculate dispatch percentage
    //         $dispatchPercentage = ($totalDispatchedItems / $totalCapacity) * 100;
    //         $dispatchPercentage = min(100, round($dispatchPercentage, 2)); // Ensure percentage does not exceed 100%

    //         // Fetch the total number of dispatched movements (you can modify the logic as needed)
    //         $totalDispatchedMovements = count($dispatchedProducts);

    //         // Step 1: Fetch stock_request_ids for the given warehouse
    //         $stockRequestIds = $this->StockRequests->find()
    //             ->select(['id'])
    //             ->where([
    //                 'requestor_type' => 'Warehouse',
    //                 'warehouse_id' => $warehouseId,
    //                 'status' => 'A'
    //             ])
    //             ->all()
    //             ->extract('id')
    //             ->toArray();

    //         // Step 2: Fetch supplier_purchase_orders linked to these stock_request_ids
    //         $supplierPurchaseOrders = $this->SupplierPurchaseOrders->find()
    //             ->contain([
    //                 'SupplierPurchaseOrdersItems' => function ($q) {
    //                     return $q->contain(['Products'])
    //                         ->select([
    //                             'SupplierPurchaseOrdersItems.product_id',
    //                             'SupplierPurchaseOrdersItems.product_variant_id',
    //                             'SupplierPurchaseOrdersItems.product_attribute_id',
    //                             'SupplierPurchaseOrdersItems.approved_quantity',
    //                             'SupplierPurchaseOrdersItems.quantity',
    //                             'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
    //                         ]);
    //                 }
    //             ])
    //             ->where([
    //                 'SupplierPurchaseOrders.stock_request_id IN' => $stockRequestIds,
    //                 'SupplierPurchaseOrders.payment_status' => 'Pending',
    //                 'SupplierPurchaseOrders.status IN' => ['A', 'P']
    //             ])
    //             ->all();

    //         // Step 3: Process the fetched purchase orders to calculate pending amounts
    //         $supplier_payment_pendings = [];

    //         foreach ($supplierPurchaseOrders as $purchaseOrder) {
    //             $totalPendingAmount = 0;

    //             foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {
    //                 // Fetch supplier price
    //                 // $supplierPrice = $this->SupplierProducts->find()
    //                 //     ->select(['supplier_price'])
    //                 //     ->where([
    //                 //         'supplier_id' => $purchaseOrder->supplier_id,
    //                 //         'product_id' => $product->product_id
    //                 //     ])
    //                 //     ->first();

    //                 $supplier_price_conditions = [
    //                     'supplier_id' => $purchaseOrder->supplier_id,
    //                     'product_id' => $product->product_id
    //                 ];

    //                 // Check if product_variant_id exists and is not null
    //                 if (!empty($product->product_variant_id)) {
    //                     $supplier_price_conditions['product_variant_id'] = $product->product_variant_id;
    //                 }

    //                 $supplierPrice = $this->SupplierProducts->find()
    //                     ->select(['supplier_price'])
    //                     ->where($supplier_price_conditions)
    //                     ->first();

    //                 // Calculate total pending amount for each product
    //                 if ($supplierPrice) {
    //                     $quantity = ($purchaseOrder->status === 'A')
    //                         ? $product->approved_quantity
    //                         : $product->quantity;

    //                     $totalPendingAmount += $supplierPrice->supplier_price * $quantity;
    //                 }
    //             }

    //             $totalPendingAmount = number_format((float)$totalPendingAmount, 2, '.', '');

    //             // Store Bill No and pending amount
    //             $supplier_payment_pendings[] = [
    //                 'bill_number' => $purchaseOrder->bill_no,
    //                 'pending_amount' => $totalPendingAmount
    //             ];

    //         }

    //         // TOP 5 SUPPLIER PAYMENTS: Collect supplier_purchase_order_ids for fetching payments
    //         $supplierPurchaseOrderIds = [];
    //         foreach ($supplierPurchaseOrders as $purchaseOrder) {
    //             $supplierPurchaseOrderIds[] = $purchaseOrder->id;
    //         }

    //         // Fetch supplier_payments linked to these purchase orders
    //         $supplier_payment = [];
    //         if (!empty($supplierPurchaseOrderIds)) {
    //             $supplier_payment = $this->SupplierPayment->find()
    //                 ->contain([
    //                     'Suppliers','SupplierPurchaseOrders'
    //                 ])
    //                 ->where([
    //                     'SupplierPayment.supplier_purchase_order_id IN' => $supplierPurchaseOrderIds
    //                 ])
    //                 ->order([
    //                     'SupplierPayment.id' => 'DESC'
    //                 ])->limit(5)->toArray();
    //         }

    //         // Calculate the start of the current month
    //         $currentMonthStart = FrozenDate::now()->startOfMonth();
    //         $currentMonthEnd = FrozenDate::now()->endOfMonth();

    //         // Fetch stock data for the current month
    //         $stockData = $this->StockMovements->find()
    //             ->select([
    //                 'month' => 'DATE_FORMAT(StockMovements.created, "%b")', // Month name (e.g., Jan, Feb)
    //                 'total_quantity' => 'SUM(StockMovementItems.quantity)'
    //             ])
    //             ->where([
    //                 'StockMovements.movement_type' => 'Incoming',
    //                 'StockMovements.warehouse_id' => $warehouseId,
    //                 'StockMovements.created >=' => $currentMonthStart,
    //                 'StockMovements.created <=' => $currentMonthEnd,
    //             ])
    //             ->innerJoinWith('StockMovementItems') // Ensures StockMovementItems is joined
    //             ->group('month')
    //             ->first(); // Only one row since it's for the current month

    //         // Initialize data for the last 6 months
    //         $lastSixMonths = [];
    //         for ($i = 5; $i >= 0; $i--) {
    //             $month = FrozenDate::now()->subMonths($i)->format('M'); // Get month name
    //             $lastSixMonths[$month] = 0; // Default to zero
    //         }

    //         // Populate the current month's data if available
    //         if ($stockData) {
    //             $lastSixMonths[$stockData->month] = $stockData->total_quantity;
    //         }

    //         // Prepare data for the graph
    //         $incomingStockGraphData = [
    //             'categories' => array_keys($lastSixMonths), // Month names as labels
    //             'data' => array_values($lastSixMonths),    // Quantities
    //         ];

    //         $incomingStockGraphData['data'] = array_map('intval', $incomingStockGraphData['data']);

    //         // Fetch stock data for the current month
    //         $outgoingStockData = $this->StockMovements->find()
    //             ->select([
    //                 'month' => 'DATE_FORMAT(StockMovements.created, "%b")', // Month name (e.g., Jan, Feb)
    //                 'total_quantity' => 'SUM(StockMovementItems.quantity)'
    //             ])
    //             ->where([
    //                 'StockMovements.movement_type' => 'Outgoing',
    //                 'StockMovements.warehouse_id' => $warehouseId,
    //                 'StockMovements.created >=' => $currentMonthStart,
    //                 'StockMovements.created <=' => $currentMonthEnd,
    //             ])
    //             ->innerJoinWith('StockMovementItems') // Ensures StockMovementItems is joined
    //             ->group('month')
    //             ->first(); // Only one row since it's for the current month

    //         // Initialize data for the last 6 months
    //         $lastSixMonthsOutgoing = [];
    //         for ($i = 5; $i >= 0; $i--) {
    //             $month = FrozenDate::now()->subMonths($i)->format('M'); // Get month name
    //             $lastSixMonthsOutgoing[$month] = 0; // Default to zero
    //         }

    //         // Populate the current month's data if available
    //         if ($outgoingStockData) {
    //             $lastSixMonthsOutgoing[$outgoingStockData->month] = $outgoingStockData->total_quantity;
    //         }

    //         // Prepare data for the graph
    //         $outgoingStockGraphData = [
    //             'categories' => array_keys($lastSixMonthsOutgoing), // Month names as labels
    //             'data' => array_values($lastSixMonthsOutgoing),    // Quantities
    //         ];

    //         $outgoingStockGraphData['data'] = array_map('intval', $outgoingStockGraphData['data']);

    //         // echo "<pre>";print_r($graphData);die;

    //     }
    //     else
    //     {
    //         $noActiveWarehouseMessage = __('No warehouse are assigned to you.');
    //         $totalStocks = 0;
    //         $lowStockCount = 0;
    //         $totalProducts = 0;
    //         $percentageStocks = 0;
    //         $pendingRequests = 0;
    //         $completedRequests = 0;
    //         $totalRequests = 0;
    //         $percentageCompleted = 0;
    //         $totalDispatchedItems = 0;
    //         $dispatchPercentage = 0;
    //         $totalDispatchedMovements = 0;
    //         $supplier_payment_pendings = [];
    //         $supplier_payment = [];
    //         $incomingStockGraphData = [];
    //         $outgoingStockGraphData = [];
    //     }

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $this->set(compact('warehouse_detail', 'noActiveWarehouseMessage', 'totalStocks', 'lowStockCount', 'totalProducts', 'percentageStocks', 'pendingRequests', 'completedRequests', 'totalRequests', 'percentageCompleted', 'totalDispatchedItems', 'dispatchPercentage', 'totalDispatchedMovements', 'supplier_payment_pendings', 'supplier_payment', 'incomingStockGraphData', 'outgoingStockGraphData', 'currencySymbol', 'decimalSeparator', 'thousandSeparator'));
    // }


    // public function filterWarehouseDashboardGraphData()
    // {
    //     $this->request->allowMethod(['post']);

    //     $startDate = $this->request->getData('startDate');
    //     $endDate = $this->request->getData('endDate');
    //     $months = $this->request->getData('months');
    //     $warehouse_id = $this->request->getData('warehouse_id');

    //     // Get the number of months specified (3 or 6)
    //     $numberOfMonths = count($months);

    //     // Fetch incoming stock data from stock_movements table
    //     $stockMovementsQuery = $this->StockMovements->find()
    //         ->select([
    //             'month' => 'MONTH(StockMovements.created)',
    //             'year' => 'YEAR(StockMovements.created)',
    //             'total_quantity' => $this->StockMovements->find()->func()->sum('StockMovementItems.quantity')
    //         ])
    //         ->where([
    //             'StockMovements.movement_type' => 'Incoming',
    //             'StockMovements.warehouse_id' => $warehouse_id,
    //             'DATE(StockMovements.created) >=' => $startDate,
    //             'DATE(StockMovements.created) <=' => $endDate
    //         ])
    //         ->innerJoinWith('StockMovementItems')
    //         ->group(['year', 'month'])
    //         ->order(['year' => 'ASC', 'month' => 'ASC']);

    //     $stockMovements = $stockMovementsQuery->toArray();

    //     // Prepare data for the graph
    //     $stockData = [];
    //     $monthLabels = [];
    //     $currentDate = new \DateTime();
    //     $monthLabels[] = $currentDate->format('M');
    //     for ($i = 1; $i < $numberOfMonths; $i++) {
    //         $currentDate->modify('-1 month');
    //         $monthLabels[] = $currentDate->format('M');
    //     }
    //     $monthLabels = array_reverse($monthLabels);

    //     $stockCounts = array_fill(0, count($monthLabels), 0);

    //     foreach ($stockMovements as $movement) {
    //         $movementMonthAbbr = date('M', mktime(0, 0, 0, $movement['month'], 1));
    //         $movementMonthIndex = array_search($movementMonthAbbr, $monthLabels);

    //         if ($movementMonthIndex !== false) {
    //             $stockCounts[$movementMonthIndex] += $movement['total_quantity'];
    //         }
    //     }

    //     // Prepare the final graph data
    //     $stockTrendsgraphData = [
    //         'name' => 'Stocks',
    //         'data' => $stockCounts
    //     ];

    //     return $this->response->withType('application/json')->withStringBody(json_encode([
    //         'graph_data' => [$stockTrendsgraphData],
    //         'month_labels' => $monthLabels
    //     ]));
    // }


    // public function filterWarehouseDashboardGraphData()
    // {
    //     $this->request->allowMethod(['post']);

    //     $startDate = $this->request->getData('startDate');
    //     $endDate = $this->request->getData('endDate');
    //     $months = $this->request->getData('months');
    //     $warehouse_id = $this->request->getData('warehouse_id');

    //     // Get the number of months specified (3 or 6)
    //     $numberOfMonths = count($months);

    //     // Fetch incoming stock data from stock_movements table
    //     $incomingStockMovementsQuery = $this->StockMovements->find()
    //         ->select([
    //             'month' => 'MONTH(StockMovements.created)',
    //             'year' => 'YEAR(StockMovements.created)',
    //             'total_quantity' => $this->StockMovements->find()->func()->sum('StockMovementItems.quantity')
    //         ])
    //         ->where([
    //             'StockMovements.movement_type' => 'Incoming',
    //             'StockMovements.warehouse_id' => $warehouse_id,
    //             'DATE(StockMovements.created) >=' => $startDate,
    //             'DATE(StockMovements.created) <=' => $endDate
    //         ])
    //         ->innerJoinWith('StockMovementItems')
    //         ->group(['year', 'month'])
    //         ->order(['year' => 'ASC', 'month' => 'ASC']);

    //     $outgoingStockMovementsQuery = $this->StockMovements->find()
    //         ->select([
    //             'month' => 'MONTH(StockMovements.created)',
    //             'year' => 'YEAR(StockMovements.created)',
    //             'total_quantity' => $this->StockMovements->find()->func()->sum('StockMovementItems.quantity')
    //         ])
    //         ->where([
    //             'StockMovements.movement_type' => 'Outgoing',
    //             'StockMovements.warehouse_id' => $warehouse_id,
    //             'DATE(StockMovements.created) >=' => $startDate,
    //             'DATE(StockMovements.created) <=' => $endDate
    //         ])
    //         ->innerJoinWith('StockMovementItems')
    //         ->group(['year', 'month'])
    //         ->order(['year' => 'ASC', 'month' => 'ASC']);

    //     $incomingStockMovements = $incomingStockMovementsQuery->toArray();
    //     $outgoingStockMovements = $outgoingStockMovementsQuery->toArray();

    //     // Prepare data for the graph
    //     $stockData = [];
    //     $monthLabels = [];
    //     $currentDate = new \DateTime();
    //     $monthLabels[] = $currentDate->format('M');
    //     for ($i = 1; $i < $numberOfMonths; $i++) {
    //         $currentDate->modify('-1 month');
    //         $monthLabels[] = $currentDate->format('M');
    //     }
    //     $monthLabels = array_reverse($monthLabels);

    //     $incomingStockCounts = array_fill(0, count($monthLabels), 0);
    //     $outgoingStockCounts = array_fill(0, count($monthLabels), 0);

    //     foreach ($incomingStockMovements as $movement) {
    //         $movementMonthAbbr = date('M', mktime(0, 0, 0, $movement['month'], 1));
    //         $movementMonthIndex = array_search($movementMonthAbbr, $monthLabels);

    //         if ($movementMonthIndex !== false) {
    //             $incomingStockCounts[$movementMonthIndex] += $movement['total_quantity'];
    //         }
    //     }

    //     foreach ($outgoingStockMovements as $movement) {
    //         $movementMonthAbbr = date('M', mktime(0, 0, 0, $movement['month'], 1));
    //         $movementMonthIndex = array_search($movementMonthAbbr, $monthLabels);

    //         if ($movementMonthIndex !== false) {
    //             $outgoingStockCounts[$movementMonthIndex] += $movement['total_quantity'];
    //         }
    //     }

    //     // Prepare the final graph data
    //     $incomingStockGraphData = [
    //         'name' => 'Stocks',
    //         'data' => $incomingStockCounts
    //     ];

    //     $outgoingStockGraphData = [
    //         'name' => 'Stocks',
    //         'data' => $outgoingStockCounts
    //     ];

    //     return $this->response->withType('application/json')->withStringBody(json_encode([
    //         'incoming_stock_graph_data' => [$incomingStockGraphData],
    //         'outgoing_stock_graph_data' => [$outgoingStockGraphData],
    //         'month_labels' => $monthLabels
    //     ]));
    // }


    // public function filterDashboardCard()
    // {
    //     $this->request->allowMethod(['get']);

    //     $dateRange = $this->request->getQuery('dateRange');

    //     // Get the date ranges based on the selected option
    //     switch ($dateRange) {
    //         case 'current_month':
    //             $startDate = FrozenTime::now()->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'last_3_months':
    //             $startDate = FrozenTime::now()->subMonths(3)->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'last_6_months':
    //             $startDate = FrozenTime::now()->subMonths(6)->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'current_year':
    //             $startDate = FrozenTime::now()->startOfYear();
    //             $endDate = FrozenTime::now()->endOfYear();
    //             break;
    //         default:
    //             return $this->response->withStatus(400, 'Invalid date range');
    //     }

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $totalOrdersQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate
    //         ]);

    //     // Online Orders
    //     $onlineOrders = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate,
    //             'Orders.order_type' => 'Online'
    //         ])->count();

    //     // Showroom Orders
    //     $showroomOrders = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate,
    //             'Orders.order_type' => 'Showroom'
    //         ])->count();

    //     // Get the total number of orders for the current month
    //     $totalOrders = $totalOrdersQuery->count();

    //     // Total sales amount for the current month
    //     // Calculate total sales amount for the current month
    //     $totalSalesAmountQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate
    //         ])
    //         ->select([
    //             'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
    //         ])
    //         ->first();

    //     $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol : 0;

    //     // Get the total number of all-time orders (excluding Cancelled orders)
    //     $allTimeOrdersQuery = $this->Orders->find()
    //         ->where(['Orders.status !=' => 'Cancelled']);

    //     $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //     // Calculate the percentage of orders in the current month
    //     $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //     // Base query for active users
    //     $userQuery = $this->Users->find()
    //         ->contain(['Roles'])
    //         ->where(['Users.status' => 'A'])
    //         ->order(['Users.first_name' => 'ASC']);

    //     // Filter by role if provided
    //     $roleId = $this->request->getQuery('role');
    //     if ($roleId) {
    //         $userQuery->where(['Users.role_id' => $roleId]);
    //     }

    //     // Get the total count of active users
    //     $totalUsers = $userQuery->count();

    //     $newUsers = 0;
    //     if ($startDate && $endDate) {
    //         $users = $userQuery->all(); // Retrieve all users
    //         // echo "<pre>";print_r($users);die;
    //         foreach ($users as $user) {
    //             $createdDate = $user->created->format('Y-m-d');
    //             // echo "<pre>";print_r($startDate->format('Y-m-d'));
    //             if ($createdDate >= $startDate->format('Y-m-d')) {
    //                 $newUsers++;
    //             }
    //         }
    //     } else {
    //         // If no date range is provided, new users count is zero
    //         $newUsers = 0;
    //     }

    //     // Calculate the percentage of new users
    //     $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //     // Fetch total active products
    //     $totalActiveProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $startDate,
    //             // 'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Fetch number of new products added in the current month
    //     $newProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $startDate,
    //             'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new products
    //     $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //     // Get the total number of active showrooms
    //     $totalShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $startDate,
    //             // 'created <=' => $endDate
    //         ])
    //         ->count();

    //     $newShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $startDate,
    //             'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new showrooms
    //     $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

    //     // Return data as JSON
    //     $this->set([
    //         'totalOrders' => $totalOrders,
    //         'onlineOrders' => $onlineOrders,
    //         'showroomOrders' => $showroomOrders,
    //         'totalSalesAmount' => $totalSalesAmount,
    //         'percentageOrders' => $percentageOrders,
    //         'totalUsers' => $totalUsers,
    //         'newUsers' => $newUsers,
    //         'newUsersPercentage' => $newUsersPercentage,
    //         'totalActiveProducts' => $totalActiveProducts,
    //         'newProducts' => $newProducts,
    //         'newProductsPercentage' => $newProductsPercentage,
    //         'totalShowrooms' => $totalShowrooms,
    //         'newShowrooms' => $newShowrooms,
    //         'newShowroomsPercentage' => $newShowroomsPercentage,
    //         '_serialize' => ['totalOrders', 'onlineOrders', 'showroomOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage']
    //     ]);

    //     return $this->response->withType('application/json')
    //                 ->withStringBody(json_encode([

    //                     'totalOrders' => $totalOrders,
    //                     'onlineOrders' => $onlineOrders,
    //                     'showroomOrders' => $showroomOrders,
    //                     'totalSalesAmount' => $totalSalesAmount,
    //                     'percentageOrders' => $percentageOrders,
    //                     'totalUsers' => $totalUsers,
    //                     'newUsers' => $newUsers,
    //                     'newUsersPercentage' => $newUsersPercentage,
    //                     'totalActiveProducts' => $totalActiveProducts,
    //                     'newProducts' => $newProducts,
    //                     'newProductsPercentage' => $newProductsPercentage,
    //                     'totalShowrooms' => $totalShowrooms,
    //                     'newShowrooms' => $newShowrooms,
    //                     'newShowroomsPercentage' => $newShowroomsPercentage

    //                 ]));

    // }

    // public function filterShowroomDashboardCard()
    // {
    //     $this->request->allowMethod(['get']);

    //     $dateRange = $this->request->getQuery('dateRange');

    //     // Get the date ranges based on the selected option
    //     switch ($dateRange) {
    //         case 'current_month':
    //             $startDate = FrozenTime::now()->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'last_3_months':
    //             $startDate = FrozenTime::now()->subMonths(3)->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'last_6_months':
    //             $startDate = FrozenTime::now()->subMonths(6)->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'current_year':
    //             $startDate = FrozenTime::now()->startOfYear();
    //             $endDate = FrozenTime::now()->endOfYear();
    //             break;
    //         default:
    //             return $this->response->withStatus(400, 'Invalid date range');
    //     }

    //     $user = $this->Authentication->getIdentity();

    //     /** GET SHOWROOM DETAILS **/
    //     $showroom_detail = $this->Showrooms->find()
    //         ->where(['Showrooms.showroom_manager' => $user->id])
    //         ->first();

    //     $showroomId = $showroom_detail->id;

    //     $totalOrdersQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate,
    //             'Orders.showroom_id' => $showroomId,
    //             'Orders.order_type' => 'Showroom'
    //         ]);

    //     // Get the total number of orders for the current month
    //     $totalOrders = $totalOrdersQuery->count();

    //     // Total sales amount for the current month
    //     // Calculate total sales amount for the current month
    //     $totalSalesAmountQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate,
    //             'Orders.showroom_id' => $showroomId,
    //             'Orders.order_type' => 'Showroom'
    //         ])
    //         ->select([
    //             'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
    //         ])
    //         ->first();

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol : 0;

    //     // Get the total number of all-time orders (excluding Cancelled orders)
    //     $allTimeOrdersQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.showroom_id' => $showroomId
    //         ]);

    //     $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //     // Calculate the percentage of orders in the current month
    //     $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //     $userQuery = $this->Users->find()
    //         ->contain(['Roles'])
    //         ->where(['Users.status' => 'A'])
    //         ->order(['Users.first_name' => 'ASC']);

    //     // Filter by role if provided
    //     $roleId = $this->request->getQuery('role');
    //     if ($roleId) {
    //         $userQuery->where(['Users.role_id' => $roleId]);
    //     }

    //     // Get the total count of active users
    //     $totalUsers = $userQuery->count();

    //     $newUsers = 0;
    //     if ($startDate && $endDate) {
    //         $users = $userQuery->all(); // Retrieve all users
    //         // echo "<pre>";print_r($users);die;
    //         foreach ($users as $user) {
    //             $createdDate = $user->created->format('Y-m-d');
    //             // echo "<pre>";print_r($createdDate);
    //             // echo "<pre>";print_r($startDate->format('Y-m-d'));
    //             if ($createdDate >= $startDate->format('Y-m-d')) {
    //                 $newUsers++;
    //             }
    //         }
    //     } else {
    //         // If no date range is provided, new users count is zero
    //         $newUsers = 0;
    //     }

    //     // Calculate the percentage of new users
    //     $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //     // Fetch total active products
    //     $totalActiveProducts = $this->ProductStocks->find()
    //         ->contain(['Products'])
    //         ->where([
    //             'ProductStocks.showroom_id' => $showroom_detail->id,
    //             'Products.status' => 'A'
    //         ])
    //         ->count();

    //      // echo "<pre>";print_r($startOfMonth);die;
    //     $newProducts = $this->ProductStocks->find()
    //         ->contain(['Products'])
    //         ->where([
    //             'ProductStocks.showroom_id' => $showroom_detail->id,
    //             'Products.status' => 'A',
    //             'Products.created >=' => $startDate,
    //             'Products.created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new products
    //     $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //     // Return data as JSON
    //     $this->set([
    //         'totalOrders' => $totalOrders,
    //         'totalSalesAmount' => $totalSalesAmount,
    //         'percentageOrders' => $percentageOrders,
    //         'totalUsers' => $totalUsers,
    //         'newUsers' => $newUsers,
    //         'newUsersPercentage' => $newUsersPercentage,
    //         'totalActiveProducts' => $totalActiveProducts,
    //         'newProducts' => $newProducts,
    //         'newProductsPercentage' => $newProductsPercentage,
    //         '_serialize' => ['totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage']
    //     ]);

    //     return $this->response->withType('application/json')
    //                 ->withStringBody(json_encode([

    //                     'totalOrders' => $totalOrders,
    //                     'totalSalesAmount' => $totalSalesAmount,
    //                     'percentageOrders' => $percentageOrders,
    //                     'totalUsers' => $totalUsers,
    //                     'newUsers' => $newUsers,
    //                     'newUsersPercentage' => $newUsersPercentage,
    //                     'totalActiveProducts' => $totalActiveProducts,
    //                     'newProducts' => $newProducts,
    //                     'newProductsPercentage' => $newProductsPercentage

    //                 ]));

    // }

    // public function filterSupervisorDashboardCard()
    // {
    //     $this->request->allowMethod(['get']);

    //     $user = $this->Authentication->getIdentity();

    //     $supervisorId = $user->id;

    //     $dateRange = $this->request->getQuery('dateRange');

    //     // Get the date ranges based on the selected option
    //     switch ($dateRange) {
    //         case 'current_month':
    //             $startDate = FrozenTime::now()->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'last_3_months':
    //             $startDate = FrozenTime::now()->subMonths(3)->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'last_6_months':
    //             $startDate = FrozenTime::now()->subMonths(6)->startOfMonth();
    //             $endDate = FrozenTime::now()->endOfMonth();
    //             break;
    //         case 'current_year':
    //             $startDate = FrozenTime::now()->startOfYear();
    //             $endDate = FrozenTime::now()->endOfYear();
    //             break;
    //         default:
    //             return $this->response->withStatus(400, 'Invalid date range');
    //     }

    //     // Fetch the active zones managed by the supervisor
    //     $activeShowrooms = $this->Showrooms->find()
    //         ->select(['id'])
    //         ->where(['showroom_supervisor' => $supervisorId])
    //         ->toArray();

    //     // Extract zone IDs
    //     $showroomIds = Hash::extract($activeShowrooms, '{n}.id');

    //     // Query for total orders and total sales amount for the current month
    //     $totalOrdersQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate,
    //             'Orders.order_type' => 'Showroom',
    //             'Orders.showroom_id IN' => $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.id IN' => $showroomIds])
    //         ]);

    //     // Get the total number of orders for the current month
    //     $totalOrders = $totalOrdersQuery->count();

    //     // Total sales amount for the current month
    //     $totalSalesAmountQuery = $this->Orders->find()
    //         ->select([
    //             'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
    //         ])
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate,
    //             'Orders.order_type' => 'Showroom',
    //             'Orders.showroom_id IN' => $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.id IN' => $showroomIds])
    //         ])
    //         ->first();

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol : 0;

    //     // Get the total number of all-time orders (excluding Cancelled orders)
    //     $allTimeOrdersQuery = $this->Orders->find()
    //         ->where(['Orders.status !=' => 'Cancelled', 'Orders.showroom_id IN' => $this->Showrooms->find()
    //             ->select(['id'])
    //             ->where(['Showrooms.id IN' => $showroomIds])
    //         ]);


    //     $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //     // Calculate the percentage of orders in the current month
    //     $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //     // Base query for active users
    //     $userQuery = $this->Users->find()
    //         ->contain(['Roles'])
    //         ->where(['Users.status' => 'A'])
    //         ->order(['Users.first_name' => 'ASC']);

    //     // Filter by role if provided
    //     $roleId = $this->request->getQuery('role');
    //     if ($roleId) {
    //         $userQuery->where(['Users.role_id' => $roleId]);
    //     }

    //     // Get the total count of active users
    //     $totalUsers = $userQuery->count();

    //     $newUsers = 0;
    //     if ($startDate && $endDate) {
    //         $users = $userQuery->all(); // Retrieve all users
    //         // echo "<pre>";print_r($users);die;
    //         foreach ($users as $user) {
    //             $createdDate = $user->created->format('Y-m-d');
    //             // echo "<pre>";print_r($startDate->format('Y-m-d'));
    //             if ($createdDate >= $startDate->format('Y-m-d')) {
    //                 $newUsers++;
    //             }
    //         }
    //     } else {
    //         // If no date range is provided, new users count is zero
    //         $newUsers = 0;
    //     }

    //     // Calculate the percentage of new users
    //     $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //     // Fetch total active products
    //     $totalActiveProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $startDate,
    //             // 'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Fetch number of new products added in the current month
    //     $newProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $startDate,
    //             'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new products
    //     $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //     // Get the total number of active showrooms
    //     $totalShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $startDate,
    //             // 'created <=' => $endDate
    //         ])
    //         ->count();

    //     $newShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $startDate,
    //             'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new showrooms
    //     $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

    //     // Return data as JSON
    //     $this->set([
    //         'totalOrders' => $totalOrders,
    //         'totalSalesAmount' => $totalSalesAmount,
    //         'percentageOrders' => $percentageOrders,
    //         'totalUsers' => $totalUsers,
    //         'newUsers' => $newUsers,
    //         'newUsersPercentage' => $newUsersPercentage,
    //         'totalActiveProducts' => $totalActiveProducts,
    //         'newProducts' => $newProducts,
    //         'newProductsPercentage' => $newProductsPercentage,
    //         'totalShowrooms' => $totalShowrooms,
    //         'newShowrooms' => $newShowrooms,
    //         'newShowroomsPercentage' => $newShowroomsPercentage,
    //         '_serialize' => ['totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage']
    //     ]);

    //     return $this->response->withType('application/json')
    //                 ->withStringBody(json_encode([

    //                     'totalOrders' => $totalOrders,
    //                     'totalSalesAmount' => $totalSalesAmount,
    //                     'percentageOrders' => $percentageOrders,
    //                     'totalUsers' => $totalUsers,
    //                     'newUsers' => $newUsers,
    //                     'newUsersPercentage' => $newUsersPercentage,
    //                     'totalActiveProducts' => $totalActiveProducts,
    //                     'newProducts' => $newProducts,
    //                     'newProductsPercentage' => $newProductsPercentage,
    //                     'totalShowrooms' => $totalShowrooms,
    //                     'newShowrooms' => $newShowrooms,
    //                     'newShowroomsPercentage' => $newShowroomsPercentage

    //                 ]));

    // }

    // public function filterDashboardCardByDate()
    // {
    //     $this->request->allowMethod(['get']);

    //     // Get the fromDate and toDate from the query parameters
    //     $fromDate = $this->request->getQuery('fromDate');
    //     $toDate = $this->request->getQuery('toDate');

    //     // Validate date inputs
    //     if (!$fromDate || !$toDate) {
    //         return $this->response->withStatus(400, 'Both dates are required.');
    //     }

    //     // Convert dates to FrozenTime objects for comparison
    //     $startDate = FrozenTime::parse($fromDate);
    //     $endDate = FrozenTime::parse($toDate);

    //     $totalOrdersQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate
    //         ]);

    //     // Get the total number of orders for the current month
    //     $totalOrders = $totalOrdersQuery->count();

    //     // Online Orders
    //     $onlineOrders = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate,
    //             'Orders.order_type' => 'Online'
    //         ])->count();

    //     // Showroom Orders
    //     $showroomOrders = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.order_date >=' => $startDate,
    //             'Orders.order_date <=' => $endDate,
    //             'Orders.order_type' => 'Showroom'
    //         ])->count();

    //     // Total sales amount for the current month
    //     // Calculate total sales amount for the current month
    //     $totalSalesAmountQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate
    //         ])
    //         ->select([
    //             'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
    //         ])
    //         ->first();

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol : 0;

    //     // Get the total number of all-time orders (excluding Cancelled orders)
    //     $allTimeOrdersQuery = $this->Orders->find()
    //         ->where(['Orders.status !=' => 'Cancelled']);

    //     $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //     // Calculate the percentage of orders in the current month
    //     $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //     $userQuery = $this->Users->find()
    //         ->contain(['Roles'])
    //         ->where(['Users.status' => 'A'])
    //         ->order(['Users.first_name' => 'ASC']);

    //     // Filter by role if provided
    //     $roleId = $this->request->getQuery('role');
    //     if ($roleId) {
    //         $userQuery->where(['Users.role_id' => $roleId]);
    //     }

    //     // Get the total count of active users
    //     $totalUsers = $userQuery->count();

    //     $newUsers = 0;
    //     if ($startDate && $endDate) {
    //         $users = $userQuery->all(); // Retrieve all users

    //         foreach ($users as $user) {
    //             $createdDate = $user->created->format('Y-m-d');

    //             if ($createdDate >= $startDate->format('Y-m-d')) {
    //                 $newUsers++;
    //             }
    //         }
    //     } else {
    //         // If no date range is provided, new users count is zero
    //         $newUsers = 0;
    //     }

    //     // Calculate the percentage of new users
    //     $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //     // Fetch total active products
    //     $totalActiveProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $startDate,
    //             // 'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Fetch number of new products added in the current month
    //     $newProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $startDate,
    //             'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new products
    //     $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //     // Get the total number of active showrooms
    //     $totalShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $startDate,
    //             // 'created <=' => $endDate
    //         ])
    //         ->count();

    //     $newShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $startDate,
    //             'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new showrooms
    //     $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

    //     // Return data as JSON
    //     $this->set([
    //         'totalOrders' => $totalOrders,
    //         'onlineOrders' => $onlineOrders,
    //         'showroomOrders' => $showroomOrders,
    //         'totalSalesAmount' => $totalSalesAmount,
    //         'percentageOrders' => $percentageOrders,
    //         'totalUsers' => $totalUsers,
    //         'newUsers' => $newUsers,
    //         'newUsersPercentage' => $newUsersPercentage,
    //         'totalActiveProducts' => $totalActiveProducts,
    //         'newProducts' => $newProducts,
    //         'newProductsPercentage' => $newProductsPercentage,
    //         'totalShowrooms' => $totalShowrooms,
    //         'newShowrooms' => $newShowrooms,
    //         'newShowroomsPercentage' => $newShowroomsPercentage,
    //         '_serialize' => ['totalOrders', 'onlineOrders', 'showroomOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage']
    //     ]);

    //     return $this->response->withType('application/json')
    //                 ->withStringBody(json_encode([

    //                     'totalOrders' => $totalOrders,
    //                     'onlineOrders' => $onlineOrders,
    //                     'showroomOrders' => $showroomOrders,
    //                     'totalSalesAmount' => $totalSalesAmount,
    //                     'percentageOrders' => $percentageOrders,
    //                     'totalUsers' => $totalUsers,
    //                     'newUsers' => $newUsers,
    //                     'newUsersPercentage' => $newUsersPercentage,
    //                     'totalActiveProducts' => $totalActiveProducts,
    //                     'newProducts' => $newProducts,
    //                     'newProductsPercentage' => $newProductsPercentage,
    //                     'totalShowrooms' => $totalShowrooms,
    //                     'newShowrooms' => $newShowrooms,
    //                     'newShowroomsPercentage' => $newShowroomsPercentage

    //                 ]));

    // }

    // public function filterShowroomDashboardCardByDate()
    // {
    //     $this->request->allowMethod(['get']);

    //     $user = $this->Authentication->getIdentity();

    //     /** GET SHOWROOM DETAILS **/
    //     $showroom_detail = $this->Showrooms->find()
    //         ->where(['Showrooms.showroom_manager' => $user->id])
    //         ->first();

    //     $showroomId = $showroom_detail->id;

    //     // Get the fromDate and toDate from the query parameters
    //     $fromDate = $this->request->getQuery('fromDate');
    //     $toDate = $this->request->getQuery('toDate');

    //     // Validate date inputs
    //     if (!$fromDate || !$toDate) {
    //         return $this->response->withStatus(400, 'Both dates are required.');
    //     }

    //     // Convert dates to FrozenTime objects for comparison
    //     $startDate = FrozenTime::parse($fromDate);
    //     $endDate = FrozenTime::parse($toDate);

    //     $totalOrdersQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate,
    //             'Orders.showroom_id' => $showroomId,
    //             'Orders.order_type' => 'Showroom'
    //         ]);

    //     // Get the total number of orders for the current month
    //     $totalOrders = $totalOrdersQuery->count();

    //     // Total sales amount for the current month
    //     // Calculate total sales amount for the current month
    //     $totalSalesAmountQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate,
    //             'Orders.showroom_id' => $showroomId,
    //             'Orders.order_type' => 'Showroom'
    //         ])
    //         ->select([
    //             'total_sales' => $totalOrdersQuery->func()->sum('Orders.total_amount')
    //         ])
    //         ->first();

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol : 0;

    //     // Get the total number of all-time orders (excluding Cancelled orders)
    //     $allTimeOrdersQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled',
    //             'Orders.showroom_id' => $showroomId
    //         ]);

    //     $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //     // Calculate the percentage of orders in the current month
    //     $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //     $userQuery = $this->Users->find()
    //         ->contain(['Roles'])
    //         ->where(['Users.status' => 'A'])
    //         ->order(['Users.first_name' => 'ASC']);

    //     // Filter by role if provided
    //     $roleId = $this->request->getQuery('role');
    //     if ($roleId) {
    //         $userQuery->where(['Users.role_id' => $roleId]);
    //     }

    //     // Get the total count of active users
    //     $totalUsers = $userQuery->count();

    //     $newUsers = 0;
    //     if ($startDate && $endDate) {
    //         $users = $userQuery->all(); // Retrieve all users

    //         foreach ($users as $user) {
    //             $createdDate = $user->created->format('Y-m-d');

    //             if ($createdDate >= $startDate->format('Y-m-d')) {
    //                 $newUsers++;
    //             }
    //         }
    //     } else {
    //         // If no date range is provided, new users count is zero
    //         $newUsers = 0;
    //     }

    //     // Calculate the percentage of new users
    //     $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //     // Fetch total active products
    //     // Fetch total active products
    //     $totalActiveProducts = $this->ProductStocks->find()
    //         ->contain(['Products'])
    //         ->where([
    //             'ProductStocks.showroom_id' => $showroomId,
    //             'Products.status' => 'A'
    //         ])
    //         ->count();

    //     $newProducts = $this->ProductStocks->find()
    //         ->contain(['Products'])
    //         ->where([
    //             'ProductStocks.showroom_id' => $showroomId,
    //             'Products.status' => 'A',
    //             'Products.created >=' => $startDate,
    //             'Products.created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new products
    //     $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //     // Return data as JSON
    //     $this->set([
    //         'totalOrders' => $totalOrders,
    //         'totalSalesAmount' => $totalSalesAmount,
    //         'percentageOrders' => $percentageOrders,
    //         'totalUsers' => $totalUsers,
    //         'newUsers' => $newUsers,
    //         'newUsersPercentage' => $newUsersPercentage,
    //         'totalActiveProducts' => $totalActiveProducts,
    //         'newProducts' => $newProducts,
    //         'newProductsPercentage' => $newProductsPercentage,
    //         '_serialize' => ['totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage']
    //     ]);

    //     return $this->response->withType('application/json')
    //                 ->withStringBody(json_encode([

    //                     'totalOrders' => $totalOrders,
    //                     'totalSalesAmount' => $totalSalesAmount,
    //                     'percentageOrders' => $percentageOrders,
    //                     'totalUsers' => $totalUsers,
    //                     'newUsers' => $newUsers,
    //                     'newUsersPercentage' => $newUsersPercentage,
    //                     'totalActiveProducts' => $totalActiveProducts,
    //                     'newProducts' => $newProducts,
    //                     'newProductsPercentage' => $newProductsPercentage

    //                 ]));

    // }

    // public function filterSupervisorDashboardCardByDate()
    // {
    //     $this->request->allowMethod(['get']);

    //     $user = $this->Authentication->getIdentity();

    //     $supervisorId = $user->id;

    //     // Get the fromDate and toDate from the query parameters
    //     $fromDate = $this->request->getQuery('fromDate');
    //     $toDate = $this->request->getQuery('toDate');

    //     // Validate date inputs
    //     if (!$fromDate || !$toDate) {
    //         return $this->response->withStatus(400, 'Both dates are required.');
    //     }

    //     // Convert dates to FrozenTime objects for comparison
    //     $startDate = FrozenTime::parse($fromDate);
    //     $endDate = FrozenTime::parse($toDate);

    //     // Fetch the active zones managed by the supervisor
    //     $activeShowrooms = $this->Showrooms->find()
    //         ->select(['id'])
    //         ->where(['showroom_supervisor' => $supervisorId])
    //         ->toArray();

    //     // Extract zone IDs
    //     $showroomIds = Hash::extract($activeShowrooms, '{n}.id');

    //     // Query for total orders and total sales amount for the current month
    //     $totalOrdersQuery = $this->Orders->find()
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude Cancelled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate,
    //             'Orders.order_type' => 'Showroom',
    //             'Orders.showroom_id IN' => $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.id IN' => $showroomIds])
    //         ]);

    //     // Get the total number of orders for the current month
    //     $totalOrders = $totalOrdersQuery->count();

    //     // Total sales amount for the current month
    //     $totalSalesAmountQuery = $this->Orders->find()
    //         ->select([
    //             'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
    //         ])
    //         ->where([
    //             'Orders.status !=' => 'Cancelled', // Exclude canceled orders
    //             'Orders.created >=' => $startDate,
    //             'Orders.created <=' => $endDate,
    //             'Orders.order_type' => 'Showroom',
    //             'Orders.showroom_id IN' => $this->Showrooms->find()
    //                 ->select(['id'])
    //                 ->where(['Showrooms.id IN' => $showroomIds])
    //         ])
    //         ->first();

    //     $currencyConfig = Configure::read('Settings.Currency.format');
    //     $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
    //     $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
    //     $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

    //     $totalSalesAmount = $totalSalesAmountQuery ? number_format((float)$totalSalesAmountQuery->total_sales, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol : 0;

    //     // Get the total number of all-time orders (excluding Cancelled orders)
    //     $allTimeOrdersQuery = $this->Orders->find()
    //         ->where(['Orders.status !=' => 'Cancelled', 'Orders.showroom_id IN' => $this->Showrooms->find()
    //             ->select(['id'])
    //             ->where(['Showrooms.id IN' => $showroomIds])
    //         ]);

    //     $totalAllTimeOrders = $allTimeOrdersQuery->count();

    //     // Calculate the percentage of orders in the current month
    //     $percentageOrders = ($totalAllTimeOrders > 0) ? ($totalOrders / $totalAllTimeOrders) * 100 : 0;

    //     $userQuery = $this->Users->find()
    //         ->contain(['Roles'])
    //         ->where(['Users.status' => 'A'])
    //         ->order(['Users.first_name' => 'ASC']);

    //     // Filter by role if provided
    //     $roleId = $this->request->getQuery('role');
    //     if ($roleId) {
    //         $userQuery->where(['Users.role_id' => $roleId]);
    //     }

    //     // Get the total count of active users
    //     $totalUsers = $userQuery->count();

    //     $newUsers = 0;
    //     if ($startDate && $endDate) {
    //         $users = $userQuery->all(); // Retrieve all users

    //         foreach ($users as $user) {
    //             $createdDate = $user->created->format('Y-m-d');

    //             if ($createdDate >= $startDate->format('Y-m-d')) {
    //                 $newUsers++;
    //             }
    //         }
    //     } else {
    //         // If no date range is provided, new users count is zero
    //         $newUsers = 0;
    //     }

    //     // Calculate the percentage of new users
    //     $newUsersPercentage = $totalUsers > 0 ? ($newUsers / $totalUsers) * 100 : 0;

    //     // Fetch total active products
    //     $totalActiveProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $startDate,
    //             // 'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Fetch number of new products added in the current month
    //     $newProducts = $this->Products->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $startDate,
    //             'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new products
    //     $newProductsPercentage = $totalActiveProducts > 0 ? ($newProducts / $totalActiveProducts) * 100 : 0;

    //     // Get the total number of active showrooms
    //     $totalShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A'
    //             // 'created >=' => $startDate,
    //             // 'created <=' => $endDate
    //         ])
    //         ->count();

    //     $newShowrooms = $this->Showrooms->find()
    //         ->where([
    //             'status' => 'A',
    //             'created >=' => $startDate,
    //             'created <=' => $endDate
    //         ])
    //         ->count();

    //     // Calculate the percentage of new showrooms
    //     $newShowroomsPercentage = $totalShowrooms > 0 ? ($newShowrooms / $totalShowrooms) * 100 : 0;

    //     // Return data as JSON
    //     $this->set([
    //         'totalOrders' => $totalOrders,
    //         'totalSalesAmount' => $totalSalesAmount,
    //         'percentageOrders' => $percentageOrders,
    //         'totalUsers' => $totalUsers,
    //         'newUsers' => $newUsers,
    //         'newUsersPercentage' => $newUsersPercentage,
    //         'totalActiveProducts' => $totalActiveProducts,
    //         'newProducts' => $newProducts,
    //         'newProductsPercentage' => $newProductsPercentage,
    //         'totalShowrooms' => $totalShowrooms,
    //         'newShowrooms' => $newShowrooms,
    //         'newShowroomsPercentage' => $newShowroomsPercentage,
    //         '_serialize' => ['totalOrders', 'totalSalesAmount', 'percentageOrders', 'totalUsers', 'newUsers', 'newUsersPercentage', 'totalActiveProducts', 'newProducts', 'newProductsPercentage', 'totalShowrooms', 'newShowrooms', 'newShowroomsPercentage']
    //     ]);

    //     return $this->response->withType('application/json')
    //                 ->withStringBody(json_encode([

    //                     'totalOrders' => $totalOrders,
    //                     'totalSalesAmount' => $totalSalesAmount,
    //                     'percentageOrders' => $percentageOrders,
    //                     'totalUsers' => $totalUsers,
    //                     'newUsers' => $newUsers,
    //                     'newUsersPercentage' => $newUsersPercentage,
    //                     'totalActiveProducts' => $totalActiveProducts,
    //                     'newProducts' => $newProducts,
    //                     'newProductsPercentage' => $newProductsPercentage,
    //                     'totalShowrooms' => $totalShowrooms,
    //                     'newShowrooms' => $newShowrooms,
    //                     'newShowroomsPercentage' => $newShowroomsPercentage

    //                 ]));

    // }

    public function noaccess()
    {
        $noaccess = '';
        $this->set(compact('noaccess'));
    }

    /**
     * Get country-based order trends data for Highcharts
     */
    public function getCountryOrderTrends()
    {
        $this->request->allowMethod(['get']);

        $countryId = $this->request->getQuery('country_id');
        $duration = $this->request->getQuery('duration', 'current_month');
        $fromDate = $this->request->getQuery('fromDate');
        $toDate = $this->request->getQuery('toDate');

        // Calculate date range based on duration
        $dateRange = $this->calculateDateRange($duration, $fromDate, $toDate);
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        // Base query conditions
        $conditions = [
            'Orders.status !=' => 'Cancelled',
            'Orders.order_date >=' => $startDate,
            'Orders.order_date <=' => $endDate
        ];

        // Determine grouping based on duration
        $groupBy = $this->getGroupByFormat($duration);

        if ($countryId && $countryId !== 'all') {
            // Single country data
            $conditions['Orders.country_id'] = $countryId;

            $query = $this->Orders->find();
            $query->select([
                'date_group' => $groupBy,
                'order_count' => $query->func()->count('Orders.id'),
                'total_amount' => $query->func()->sum('Orders.total_amount')
            ])
            ->where($conditions)
            ->group(['date_group'])
            ->order(['date_group' => 'ASC']);

            $results = $query->all();

            // Prepare data for single country
            $categories = [];
            $orderCounts = [];
            $totalAmounts = [];

            foreach ($results as $row) {
                $categories[] = $this->formatDateLabel($row->date_group, $duration);
                $orderCounts[] = (int) $row->order_count;
                $totalAmounts[] = (float) $row->total_amount;
            }

            $country = $this->Countries->get($countryId);
            $countryName = $country->name;

            $response = [
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'series' => [
                        [
                            'name' => 'Order Count',
                            'data' => $orderCounts,
                            'type' => 'column',
                            'yAxis' => 0,
                            'color' => '#667eea'
                        ],
                        [
                            'name' => 'Total Amount',
                            'data' => $totalAmounts,
                            'type' => 'line',
                            'yAxis' => 1,
                            'color' => '#f093fb'
                        ]
                    ],
                    'title' => "Order Trends - {$countryName}",
                    'subtitle' => $this->getDateRangeLabel($duration, $startDate, $endDate),
                    'multiCountry' => false
                ]
            ];
        } else {
            // All countries - show each country as separate series
            $query = $this->Orders->find();
            $query->select([
                'date_group' => $groupBy,
                'country_id' => 'Orders.country_id',
                'country_name' => 'Countries.name',
                'order_count' => $query->func()->count('Orders.id'),
                'total_amount' => $query->func()->sum('Orders.total_amount')
            ])
            ->contain(['Countries'])
            ->where($conditions)
            ->group(['date_group', 'Orders.country_id', 'Countries.name'])
            ->order(['date_group' => 'ASC', 'Countries.name' => 'ASC']);

            $results = $query->all();

            // Organize data by country and date
            $countriesData = [];
            $allCategories = [];

            foreach ($results as $row) {
                $dateLabel = $this->formatDateLabel($row->date_group, $duration);
                $countryName = $row->country_name ?: 'Unknown';

                if (!isset($countriesData[$countryName])) {
                    $countriesData[$countryName] = [];
                }

                $countriesData[$countryName][$dateLabel] = [
                    'order_count' => (int) $row->order_count,
                    'total_amount' => (float) $row->total_amount
                ];

                if (!in_array($dateLabel, $allCategories)) {
                    $allCategories[] = $dateLabel;
                }
            }

            // Sort categories chronologically
            sort($allCategories);

            // Prepare series data for each country
            $series = [];
            $colors = [
                '#667eea', '#f093fb', '#4facfe', '#43e97b', '#fa709a',
                '#feb47b', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4',
                '#a8e6cf', '#ffd93d', '#6c5ce7', '#fd79a8', '#00b894',
                '#e17055', '#0984e3', '#6c5ce7', '#fd79a8', '#00cec9'
            ];
            $colorIndex = 0;

            foreach ($countriesData as $countryName => $countryData) {
                $orderCounts = [];
                $totalAmounts = [];

                foreach ($allCategories as $category) {
                    $orderCounts[] = isset($countryData[$category]) ? $countryData[$category]['order_count'] : 0;
                    $totalAmounts[] = isset($countryData[$category]) ? $countryData[$category]['total_amount'] : 0;
                }

                $color = $colors[$colorIndex % count($colors)];

                $series[] = [
                    'name' => $countryName . ' (Orders)',
                    'data' => $orderCounts,
                    'type' => 'column',
                    'yAxis' => 0,
                    'color' => $color
                ];

                $colorIndex++;
            }

            $response = [
                'success' => true,
                'data' => [
                    'categories' => $allCategories,
                    'series' => $series,
                    'title' => "Order Trends - All Countries",
                    'subtitle' => $this->getDateRangeLabel($duration, $startDate, $endDate),
                    'multiCountry' => true
                ]
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

   

    /**
     * Helper method to get GROUP BY format based on duration
     */
    private function getGroupByFormat($duration)
    {
        switch ($duration) {
            case 'current_month':
            case 'custom':
                return 'DATE(Orders.order_date)';
            case 'last_3_months':
            case 'last_6_months':
                return 'DATE_FORMAT(Orders.order_date, "%Y-%m")';
            case 'current_year':
                return 'DATE_FORMAT(Orders.order_date, "%Y-%m")';
            default:
                return 'DATE(Orders.order_date)';
        }
    }

    /**
     * Helper method to format date labels
     */
    private function formatDateLabel($dateGroup, $duration)
    {
        switch ($duration) {
            case 'current_month':
            case 'custom':
                return FrozenTime::parse($dateGroup)->format('M d');
            case 'last_3_months':
            case 'last_6_months':
            case 'current_year':
                return FrozenTime::parse($dateGroup . '-01')->format('M Y');
            default:
                return $dateGroup;
        }
    }

    /**
     * Get global dashboard data for AJAX filtering
     */
    public function getGlobalDashboardData()
    {
        $this->request->allowMethod(['get']);

        $countryId = $this->request->getQuery('country_id', 'all');
        $duration = $this->request->getQuery('duration', 'current_month');
        $fromDate = $this->request->getQuery('fromDate');
        $toDate = $this->request->getQuery('toDate');

        // Calculate date range
        $dateRange = $this->calculateDateRange($duration, $fromDate, $toDate);
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        // Base conditions
        $baseConditions = [
            'Orders.status !=' => 'Cancelled',
            'Orders.order_date >=' => $startDate,
            'Orders.order_date <=' => $endDate
        ];

        // Add country filter if specified
        if ($countryId && $countryId !== 'all') {
            $baseConditions['Orders.country_id'] = $countryId;
        }

        try {
            // Get orders data
            $totalOrders = $this->Orders->find()->where($baseConditions)->count();

            $totalSalesQuery = $this->Orders->find()
                ->where($baseConditions)
                ->select(['total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')])
                ->first();
            $totalSales = $totalSalesQuery ? $totalSalesQuery->total_sales : 0;

            $onlineOrders = $this->Orders->find()
                ->where(array_merge($baseConditions, ['Orders.order_type' => 'Online']))
                ->count();

            $showroomOrders = $this->Orders->find()
                ->where(array_merge($baseConditions, ['Orders.order_type' => 'Showroom']))
                ->count();

            // Get users data (country-based if specified)
            $userConditions = ['Users.status' => 'A'];
            $userQuery = $this->Users->find()->where($userConditions);

            // if ($countryId && $countryId !== 'all') {
            //     $userQuery = $userQuery->matching('CustomerAddresses', function ($q) use ($countryId) {
            //         return $q->where(['CustomerAddresses.country_id' => $countryId]);
            //     });
            // }

            $totalUsers = $userQuery->count();

            $newUsers = $userQuery->where([
                'Users.created >=' => $startDate,
                'Users.created <=' => $endDate
            ])->count();

            // Get products data
            $totalActiveProducts = $this->Products->find()
                ->where(['status' => 'A'])
                ->count();

            $newProducts = $this->Products->find()
                ->where([
                    'status' => 'A',
                    'created >=' => $startDate,
                    'created <=' => $endDate
                ])
                ->count();

            // Get detailed orders data for table
            $ordersData = $this->getDetailedOrdersData($baseConditions, $startDate, $endDate);

            $countryName = $this->getCountryName($countryId);

            $response = [
                'success' => true,
                'data' => [
                    'totalOrders' => $totalOrders,
                    'totalSales' => $totalSales,
                    'onlineOrders' => $onlineOrders,
                    'showroomOrders' => $showroomOrders,
                    'totalUsers' => $totalUsers,
                    'newUsers' => $newUsers,
                    'totalActiveProducts' => $totalActiveProducts,
                    'newProducts' => $newProducts,
                    'ordersData' => $ordersData,
                    'countryName' => $countryName,
                    'dateRange' => [
                        'start' => $startDate->format('Y-m-d'),
                        'end' => $endDate->format('Y-m-d'),
                        'label' => $this->getDateRangeLabel($duration, $startDate, $endDate)
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    /**
     * Get detailed orders data for table display
     */
    private function getDetailedOrdersData($baseConditions, $startDate, $endDate)
    {
        $orders = $this->Orders->find()
            ->select([
                'Orders.id',
                'Orders.order_number',
                'Orders.order_date',
                'Orders.total_amount',
                'Orders.status',
                'Orders.order_type',
                'customer_name' => 'CONCAT(Users.first_name, " ", Users.last_name)',
                'country_name' => 'Countries.name'
            ])
            ->contain([
                'Customers.Users' => ['fields' => ['first_name', 'last_name']],
                'Countries' => ['fields' => ['name']]
            ])
            ->where($baseConditions)
            ->order(['Orders.order_date' => 'DESC'])
            ->limit(10)
            ->toArray();

        return $orders;
    }

    /**
     * Get country-based statistics for Orders, Users, and Products
     */
    public function getCountryBasedData()
    {
        $this->request->allowMethod(['get']);

        $countryId = $this->request->getQuery('country_id');
        $dataType = $this->request->getQuery('data_type'); // 'orders', 'users', 'products'
        $duration = $this->request->getQuery('duration', 'current_month');
        $fromDate = $this->request->getQuery('fromDate');
        $toDate = $this->request->getQuery('toDate');

        // Calculate date range
        $dateRange = $this->calculateDateRange($duration, $fromDate, $toDate);
        $startDate = $dateRange['start'];
        $endDate = $dateRange['end'];

        $response = ['success' => false, 'data' => []];

        try {
            switch ($dataType) {
                case 'orders':
                    $response = $this->getCountryOrdersData($countryId, $startDate, $endDate);
                    break;
                case 'users':
                    $response = $this->getCountryUsersData($countryId, $startDate, $endDate);
                    break;
                case 'products':
                    $response = $this->getCountryProductsData($countryId, $startDate, $endDate);
                    break;
                default:
                    $response = ['success' => false, 'error' => 'Invalid data type: ' . $dataType];
            }
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'error' => $e->getMessage(),
                'debug' => [
                    'countryId' => $countryId,
                    'dataType' => $dataType,
                    'duration' => $duration,
                    'startDate' => $startDate->format('Y-m-d'),
                    'endDate' => $endDate->format('Y-m-d')
                ]
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    /**
     * Get country-based orders data
     */
    private function getCountryOrdersData($countryId, $startDate, $endDate)
    {
        $conditions = [
            'Orders.order_date >=' => $startDate,
            'Orders.order_date <=' => $endDate
        ];

        if ($countryId && $countryId !== 'all') {
            $conditions['Orders.country_id'] = $countryId;
        }

        // Total orders
        $totalOrders = $this->Orders->find()
            ->where(array_merge($conditions, ['Orders.status !=' => 'Cancelled']))
            ->count();

        // Orders by status
        $ordersByStatus = $this->Orders->find()
            ->select([
                'status',
                'count' => $this->Orders->find()->func()->count('Orders.id')
            ])
            ->where($conditions)
            ->group(['status'])
            ->toArray();

        // Total sales amount
        $totalSalesQuery = $this->Orders->find()
            ->select([
                'total_sales' => $this->Orders->find()->func()->sum('Orders.total_amount')
            ])
            ->where(array_merge($conditions, ['Orders.status !=' => 'Cancelled']))
            ->first();

        $totalSales = $totalSalesQuery ? $totalSalesQuery->total_sales : 0;

        // Orders by type
        $onlineOrders = $this->Orders->find()
            ->where(array_merge($conditions, [
                'Orders.order_type' => 'Online',
                'Orders.status !=' => 'Cancelled'
            ]))
            ->count();

        $showroomOrders = $this->Orders->find()
            ->where(array_merge($conditions, [
                'Orders.order_type' => 'Showroom',
                'Orders.status !=' => 'Cancelled'
            ]))
            ->count();

        return [
            'success' => true,
            'data' => [
                'totalOrders' => $totalOrders,
                'totalSales' => $totalSales,
                'onlineOrders' => $onlineOrders,
                'showroomOrders' => $showroomOrders,
                'ordersByStatus' => $ordersByStatus,
                'countryName' => $this->getCountryName($countryId)
            ]
        ];
    }

    /**
     * Get country-based users data
     */
    private function getCountryUsersData($countryId, $startDate, $endDate)
    {
        // Users are typically associated with countries through their addresses
        // We'll join with customer_addresses to get country information
        $conditions = [
            'Users.created >=' => $startDate,
            'Users.created <=' => $endDate,
            'Users.status' => 'A'
        ];

        $query = $this->Users->find()
            ->contain(['Roles']);

        // if ($countryId && $countryId !== 'all') {
        //     // Join with customer addresses to filter by country
        //     $query = $query->matching('CustomerAddresses', function ($q) use ($countryId) {
        //         return $q->where(['CustomerAddresses.country_id' => $countryId]);
        //     });
        // }

        $totalUsers = $query->where($conditions)->count();

        // New users in the period
        $newUsers = $query->where($conditions)->count();

        // Users by role
        $usersByRole = $this->Users->find()
            ->select([
                'role_name' => 'Roles.name',
                'count' => $this->Users->find()->func()->count('Users.id')
            ])
            ->contain(['Roles'])
            ->where(['Users.status' => 'A'])
            ->group(['Roles.name'])
            ->toArray();

        if ($countryId && $countryId !== 'all') {
            $usersByRole = $this->Users->find()
                ->select([
                    'role_name' => 'Roles.name',
                    'count' => $this->Users->find()->func()->count('Users.id')
                ])
                ->contain(['Roles'])
                // ->matching('CustomerAddresses', function ($q) use ($countryId) {
                //     return $q->where(['CustomerAddresses.country_id' => $countryId]);
                // })
                ->where(['Users.status' => 'A'])
                ->group(['Roles.name'])
                ->toArray();
        }

        return [
            'success' => true,
            'data' => [
                'totalUsers' => $totalUsers,
                'newUsers' => $newUsers,
                'usersByRole' => $usersByRole,
                'countryName' => $this->getCountryName($countryId)
            ]
        ];
    }

    /**
     * Get country-based products data
     */
    private function getCountryProductsData($countryId, $startDate, $endDate)
    {
        // Products sold in specific countries (through orders)
        $conditions = [
            'Orders.order_date >=' => $startDate,
            'Orders.order_date <=' => $endDate,
            'Orders.status !=' => 'Cancelled'
        ];

        if ($countryId && $countryId !== 'all') {
            $conditions['Orders.country_id'] = $countryId;
        }

        // Total products sold
        $totalProductsSold = $this->OrderItems->find()
            ->select([
                'total_quantity' => $this->OrderItems->find()->func()->sum('OrderItems.quantity')
            ])
            ->matching('Orders', function ($q) use ($conditions) {
                return $q->where($conditions);
            })
            ->first();

        $totalQuantity = $totalProductsSold ? $totalProductsSold->total_quantity : 0;

        // Unique products sold
        $uniqueProducts = $this->OrderItems->find()
            ->select(['product_id'])
            ->distinct(['product_id'])
            ->matching('Orders', function ($q) use ($conditions) {
                return $q->where($conditions);
            })
            ->count();

        // Top selling products
        $topProducts = $this->OrderItems->find()
            ->select([
                'product_id' => 'Products.id',
                'product_name' => 'Products.name',
                'total_sold' => $this->OrderItems->find()->func()->sum('OrderItems.quantity'),
                'total_revenue' => $this->OrderItems->find()->func()->sum('OrderItems.price * OrderItems.quantity')
            ])
            ->contain(['Products'])
            ->matching('Orders', function ($q) use ($conditions) {
                return $q->where($conditions);
            })
            ->group(['Products.id', 'Products.name'])
            ->order(['total_sold' => 'DESC'])
            ->limit(10)
            ->toArray();

        // Total active products (not country-specific)
        $totalActiveProducts = $this->Products->find()
            ->where(['status' => 'A'])
            ->count();

        return [
            'success' => true,
            'data' => [
                'totalProductsSold' => $totalQuantity,
                'uniqueProducts' => $uniqueProducts,
                'totalActiveProducts' => $totalActiveProducts,
                'topProducts' => $topProducts,
                'countryName' => $this->getCountryName($countryId)
            ]
        ];
    }

    /**
     * Get initial country-based statistics for dashboard load
     */
    private function getCountryBasedStats()
    {
        $currentMonthStart = FrozenTime::now()->startOfMonth();
        $currentMonthEnd = FrozenTime::now()->endOfMonth();

        // Get stats for all countries
        $allCountriesOrders = $this->getCountryOrdersData('all', $currentMonthStart, $currentMonthEnd);
        $allCountriesUsers = $this->getCountryUsersData('all', $currentMonthStart, $currentMonthEnd);
        $allCountriesProducts = $this->getCountryProductsData('all', $currentMonthStart, $currentMonthEnd);

        return [
            'orders' => $allCountriesOrders['data'],
            'users' => $allCountriesUsers['data'],
            'products' => $allCountriesProducts['data']
        ];
    }

    /**
     * Helper method to get country name
     */
    private function getCountryName($countryId)
    {
        if (!$countryId || $countryId === 'all') {
            return 'All Countries';
        }

        $country = $this->Countries->find()
            ->select(['name'])
            ->where(['id' => $countryId])
            ->first();

        return $country ? $country->name : 'Unknown Country';
    }
}
