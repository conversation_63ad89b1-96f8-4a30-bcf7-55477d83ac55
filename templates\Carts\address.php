  <section class="cart-tab-head my-5">
        <div class="container">
            <ul class="nav nav-tabs" id="purchase-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link " id="home-tab"  data-bs-target="#home-tab-pane"
                        type="button"  aria-controls="home-tab-pane" aria-selected="true">
                        <div class="d-flex align-items-center "><div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                            Cart</div>
                    </button>
                    <!-- <hr> -->
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane"
                        type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false">
                        <div class="d-flex align-items-center "><div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                                Checkout</div>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contact-tab"  data-bs-target="#contact-tab-pane"
                        type="button"  aria-controls="contact-tab-pane" aria-selected="false">
                        <div class="d-flex align-items-center "><div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                                Order Complete</div>
                    </button>
                </li>
            </ul>
        </div>
    </section>
    <section>
        <div class="container">
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab"
                    tabindex="0">
                    <div class="cart-section">
                        <div class="container py-5">
                            <div class="row g-4">

                                <!-- Cart Item -->
                              <div class="col-lg-8">
                                    <div class="cart-card d-flex flex-column flex-md-row align-items-start">
                                        <div class="container my-3">
                                            <div class="bg-white address-card">

                                                <?php if (!empty($customerAddress)): ?>
                                                    <?php foreach ($customerAddress as $index => $address): ?>
                                                        <div class="address-card-enhanced mb-3 p-3 border rounded-2 position-relative <?= $index === 0 ? 'border-success bg-success-light' : 'border-secondary' ?>">
                                                            <!-- Radio Button -->
                                                            <div class="form-check position-absolute top-0 end-0 m-3">
                                                                <input class="form-check-input" type="radio" name="address"
                                                                    id="address<?= $index ?>" <?= $index === 0 ? 'checked' : '' ?>
                                                                    value="<?= h($address['id']) ?>">
                                                                <label class="form-check-label" for="address<?= $index ?>">
                                                                    <span class="visually-hidden">Select this address</span>
                                                                </label>
                                                            </div>

                                                            <!-- Address Header -->
                                                            <div class="address-header mb-2">
                                                                <div class="d-flex align-items-center mb-1">
                                                                    <span class="badge bg-success me-2 small">
                                                                        <i class="fas fa-<?= $address['type'] === 'Home' ? 'home' : 'building' ?> me-1"></i>
                                                                        <?= h($address['type'] ?? 'Home') ?>
                                                                    </span>
                                                                    <?php if ($index === 0): ?>
                                                                        <span class="badge bg-success text-white small">
                                                                            <i class="fas fa-star me-1"></i>Default
                                                                        </span>
                                                                    <?php endif; ?>
                                                                </div>
                                                                <h6 class="fw-bold mb-0 text-dark">
                                                                    <?= h($address['title'] ?? '') ?> <?= h(ucfirst($address['name'] ?? 'No Name')) ?>
                                                                </h6>
                                                            </div>

                                                            <!-- Complete Address -->
                                                            <div class="address-details mb-2">
                                                                <div class="d-flex align-items-start">
                                                                    <i class="fas fa-map-marker-alt text-success me-2 mt-1"></i>
                                                                    <div class="address-text">
                                                                        <?php
                                                                        $addressParts = [];
                                                                        if (!empty($address['house_no'])) $addressParts[] = $address['house_no'];
                                                                        if (!empty($address['address_line1'])) $addressParts[] = $address['address_line1'];
                                                                        if (!empty($address['address_line2'])) $addressParts[] = $address['address_line2'];
                                                                        if (!empty($address['landmark'])) $addressParts[] = 'Near ' . $address['landmark'];

                                                                        $fullAddress = implode(', ', array_filter($addressParts));
                                                                        ?>
                                                                        <p class="mb-1 text-dark small"><?= h($fullAddress) ?></p>

                                                                        <?php
                                                                        $locationParts = [];
                                                                        if (!empty($address['city']['city_name'])) $locationParts[] = $address['city']['city_name'];
                                                                        if (!empty($address['city']['state']['state_name'])) $locationParts[] = $address['city']['state']['state_name'];
                                                                        if (!empty($address['city']['state']['country']['name'])) $locationParts[] = $address['city']['state']['country']['name'];
                                                                        if (!empty($address['zipcode'])) $locationParts[] = $address['zipcode'];

                                                                        $location = implode(', ', array_filter($locationParts));
                                                                        ?>
                                                                        <?php if ($location): ?>
                                                                            <p class="mb-0 text-muted small"><?= h($location) ?></p>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <!-- Contact Information -->
                                                            <div class="contact-info mb-2">
                                                                <?php if (!empty($address['phone_no1'])): ?>
                                                                    <div class="d-flex align-items-start">
                                                                        <i class="fas fa-phone text-success me-2 mt-1"></i>
                                                                        <div class="address-text">
                                                                            <a href="tel:<?= h($address['country_code1'] ?? '') ?><?= h($address['phone_no1']) ?>"
                                                                               class="text-decoration-none text-dark">
                                                                                <p class="mb-1 text-dark small"><?= h($address['country_code1'] ?? '') ?> <?= h($address['phone_no1']) ?></p>
                                                                            </a>
                                                                        </div>
                                                                    </div>
                                                                <?php endif; ?>

                                                                <?php if (!empty($address['phone_no2'])): ?>
                                                                    <div class="d-flex align-items-start">
                                                                        <i class="fas fa-mobile-alt text-success me-2 mt-1"></i>
                                                                        <div class="address-text">
                                                                            <a href="tel:<?= h($address['country_code2'] ?? '') ?><?= h($address['phone_no2']) ?>"
                                                                               class="text-decoration-none text-dark">
                                                                                <p class="mb-0 text-dark small"><?= h($address['country_code2'] ?? '') ?> <?= h($address['phone_no2']) ?></p>
                                                                            </a>
                                                                        </div>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>

                                                            <!-- Action Buttons -->
                                                            <div class="address-actions d-flex justify-content-end gap-1">
                                                                <button class="btn btn-outline-success btn-sm edit-address-btn"
                                                                        data-address-id="<?= $address['id'] ?>">
                                                                    <i class="fas fa-edit me-1"></i>Edit
                                                                </button>
                                                                <?= $this->Form->postLink(
                                                                    '<i class="fas fa-trash me-1"></i>Remove',
                                                                    ['controller' => 'Cart', 'action' => 'removeAddress', $address['id']],
                                                                    [
                                                                        'confirm' => 'Are you sure you want to remove this address?',
                                                                        'class' => 'btn btn-outline-danger btn-sm',
                                                                        'escape' => false
                                                                    ]
                                                                ) ?>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <div class="text-center py-5">
                                                        <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                                                        <h5 class="text-muted">No addresses found</h5>
                                                        <p class="text-muted">Add your first delivery address to continue</p>
                                                    </div>
                                                <?php endif; ?>
                                                <hr>
                                                <!-- Add New Address -->
                                                <div class="text-start mt-4">
                                                    <button class="btn btn-add" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                                                        <span class="plus">+</span> Add New Address
                                                    </button>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                            </div>


                                <!-- Order Summary -->
                               <?php
                                    $subtotal = $cartData['totalPrice'] ?? '0.00';
                                    $region = 'Qatar';
                                    $shippingCost = 50;

                                ?>

                                <div class="col-lg-4">
                                    <div class="summary-card">
                                        <h5 class="fw-bold title pb-4">Order Summary</h5>

                                        <div class="d-flex justify-content-between mb-2">
                                            <span class="describtion">Subtotal</span>
                                            <span class="text-success">
                                                <!-- <?= $subtotal ?> QAR -->
                                                 <?= $this->Price->setPriceFormat($subtotal) ?>
                                            </span>
                                        </div>

                                        <?php if (!empty($orderSummary['discount_amount']) && $orderSummary['discount_amount'] > 0): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="describtion">Discount</span>
                                                <span class="text-success">
                                                    -<?= $this->Price->setPriceFormat($orderSummary['discount_amount']) ?>
                                                </span>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (!empty($orderSummary['shipping_discount']) && $orderSummary['shipping_discount'] > 0): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="describtion">Shipping Discount</span>
                                                <span class="text-success">
                                                    -<?= $this->Price->setPriceFormat($orderSummary['shipping_discount']) ?>
                                                </span>
                                            </div>
                                        <?php endif; ?>

                                        <div class="d-flex justify-content-between mb-4">
                                            <span class="describtion">Shipping Costs</span>
                                            <span class="text-success">
                                                <?= $this->Price->setPriceFormat($orderSummary['shipping_cost'] ?? $shippingCost) ?>
                                            </span>
                                        </div>

                                        <hr>

                                        <!-- Include Common Coupon Section -->
                                        <?= $this->element('coupon_section', [
                                            'orderSummary' => $orderSummary ?? [],
                                            'availableCoupons' => $availableCoupons ?? [],
                                            'appliedCoupon' => $appliedCoupon ?? null,
                                            'showAvailableCoupons' => true
                                        ]) ?>

                                        <hr>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" value="" id="acceptTerms">
                                            <label class="form-check-label small" for="termsCheck">
                                                Accept <a href="/terms-condtions" target="_blank">Terms and Conditions</a>
                                            </label>
                                        </div>

                                        <hr>

                                        <div class="d-flex justify-content-between mb-3 fw-bold">
                                            <span class="describtion">Total</span>
                                            <span class="text-success">
                                                <?= $this->Price->setPriceFormat($orderSummary['final_total'] ?? $subtotal) ?>
                                            </span>
                                        </div>

                                        <button class="btn btn-place-order my-2" onclick="proceedToCheckout()">
                                            Place Order | <?= $this->Price->setPriceFormat($orderSummary['final_total'] ?? $subtotal) ?>
                                        </button>
                                         <div id="codOrderMessage" class="mt-3" style="display: none;"></div>

                                        <p class="secure-pay-terms small mt-3">SECURE PAYMENTS PROVIDED BY</p>
                                        <div class="payment-icons d-flex align-items-center">
                                            <img src="../../img/ozone/mastercard-1.png" alt="Mastercard">
                                            <img src="../../img/ozone/mastercard-2.png" alt="Visa">
                                            <img src="../../img/ozone/mastercard-3.png" alt="Bitcoin">
                                            <img src="../../img/ozone/mastercard-4.png" alt="PayPal">
                                        </div>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="contact-tab-pane" role="tabpanel" aria-labelledby="contact-tab"
                    tabindex="0">
                    ...
                </div>
                <div class="tab-pane fade" id="disabled-tab-pane" role="tabpanel" aria-labelledby="disabled-tab"
                    tabindex="0">
                    ...</div>
            </div>
        </div>
    </section>

    <!-- Add Address Modal -->
    <div class="modal fade" id="addAddressModal" tabindex="-1" aria-labelledby="addAddressModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAddressModalLabel">Add New Address</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <?= $this->Form->create(null, [
                        'id' => 'addAddressForm',
                        'url' => ['controller' => 'Cart', 'action' => 'add-address'],
                        'class' => 'needs-validation',
                        'novalidate' => true
                    ]) ?>
                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]) ?>

                    <div class="row">
                        <!-- Title -->
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <?= $this->Form->control('title', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Title',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please provide a title.</div>
                        </div>

                        <!-- Name -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                            <?= $this->Form->control('name', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Enter your full name',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please provide a valid name.</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Address Type -->
                        <div class="col-12 mb-3">
                            <label class="form-label">Address Type <span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type" id="typeHome" value="Home" checked>
                                    <label class="form-check-label" for="typeHome">
                                        Home
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type" id="typeWork" value="Work">
                                    <label class="form-check-label" for="typeWork">
                                        Work
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Address Line 1 -->
                        <div class="col-md-8 mb-3">
                            <label for="address_line1" class="form-label">Address Line 1 <span class="text-danger">*</span></label>
                            <?= $this->Form->control('address_line1', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Street address, building name',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please provide a valid address.</div>
                        </div>

                        <!-- House Number -->
                        <div class="col-md-4 mb-3">
                            <label for="house_no" class="form-label">House/Flat No.</label>
                            <?= $this->Form->control('house_no', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'House/Flat number'
                            ]) ?>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Address Line 2 -->
                        <div class="col-12 mb-3">
                            <label for="address_line2" class="form-label">Address Line 2 (Optional)</label>
                            <?= $this->Form->control('address_line2', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Apartment, suite, unit, building, floor, etc.'
                            ]) ?>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Landmark -->
                        <div class="col-md-6 mb-3">
                            <label for="landmark" class="form-label">Landmark</label>
                            <?= $this->Form->control('landmark', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Nearby landmark'
                            ]) ?>
                        </div>

                        <!-- Zipcode -->
                        <div class="col-md-6 mb-3">
                            <label for="zipcode" class="form-label">Zipcode <span class="text-danger">*</span></label>
                            <?= $this->Form->control('zipcode', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Postal code',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please provide a valid zipcode.</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Country -->
                        <div class="col-md-4 mb-3">
                            <label for="country_id" class="form-label">Country <span class="text-danger">*</span></label>
                            <?= $this->Form->control('country_id', [
                                'type' => 'select',
                                'label' => false,
                                'class' => 'form-select',
                                'id' => 'addCountrySelect',
                                'options' => $countries ?? [],
                                'empty' => 'Select Country',
                                'required' => true,
                                'value' => !empty($countries) ? array_key_first($countries) : ''
                            ]) ?>
                            <div class="invalid-feedback">Please select a country.</div>
                        </div>

                        <!-- State -->
                        <div class="col-md-4 mb-3">
                            <label for="state_id" class="form-label">State <span class="text-danger">*</span></label>
                            <?= $this->Form->control('state_id', [
                                'type' => 'select',
                                'label' => false,
                                'class' => 'form-select',
                                'id' => 'addStateSelect',
                                'options' => [],
                                'empty' => 'Select State',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please select a state.</div>
                        </div>

                        <!-- City -->
                        <div class="col-md-4 mb-3">
                            <label for="city_id" class="form-label">City <span class="text-danger">*</span></label>
                            <?= $this->Form->control('city_id', [
                                'type' => 'select',
                                'label' => false,
                                'class' => 'form-select',
                                'id' => 'addCitySelect',
                                'options' => [],
                                'empty' => 'Select City',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please select a city.</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Phone Number 1 -->
                        <div class="col-md-6 mb-3">
                            <label for="phone_no1" class="form-label">Phone Number <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <?= $this->Form->control('country_code1', [
                                    'type' => 'select',
                                    'label' => false,
                                    'class' => 'form-select',
                                    'options' => ['+974' => '+974', '+91' => '+91', '+966' => '+966'],
                                    'style' => 'max-width: 120px;',
                                    'value' => '+974'
                                ]) ?>
                                <?= $this->Form->control('phone_no1', [
                                    'type' => 'tel',
                                    'label' => false,
                                    'class' => 'form-control',
                                    'placeholder' => 'Phone number',
                                    'required' => true
                                ]) ?>
                            </div>
                            <div class="invalid-feedback">Please provide a valid phone number.</div>
                        </div>

                        <!-- Phone Number 2 (Optional) -->
                        <div class="col-md-6 mb-3">
                            <label for="phone_no2" class="form-label">Alternate Phone (Optional)</label>
                            <div class="input-group">
                                <?= $this->Form->control('country_code2', [
                                    'type' => 'select',
                                    'label' => false,
                                    'class' => 'form-select',
                                    'options' => ['+974' => '+974', '+91' => '+91', '+966' => '+966'],
                                    'style' => 'max-width: 120px;',
                                    'value' => '+974'
                                ]) ?>
                                <?= $this->Form->control('phone_no2', [
                                    'type' => 'tel',
                                    'label' => false,
                                    'class' => 'form-control',
                                    'placeholder' => 'Alternate phone number'
                                ]) ?>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden fields -->
                    <?= $this->Form->hidden('customer_id', ['value' => $customerId]) ?>
                    <?= $this->Form->hidden('status', ['value' => 'A']) ?>
                    <?= $this->Form->hidden('latitude', ['value' => '0.0']) ?>
                    <?= $this->Form->hidden('longitude', ['value' => '0.0']) ?>
                    <?= $this->Form->hidden('municipality_id', ['value' => 1]) ?>

                    <?= $this->Form->end() ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="addAddressForm" class="btn btn-primary">Save Address</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Address Modal -->
    <div class="modal fade" id="editAddressModal" tabindex="-1" aria-labelledby="editAddressModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editAddressModalLabel">Edit Address</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <?= $this->Form->create(null, [
                        'id' => 'editAddressForm',
                        'url' => ['controller' => 'Cart', 'action' => 'edit-address'],
                        'class' => 'needs-validation',
                        'novalidate' => true
                    ]) ?>
                    <?= $this->Form->hidden('_csrfToken', ['value' => $this->request->getAttribute('csrfToken')]) ?>
                    <?= $this->Form->hidden('id', ['id' => 'editAddressId']) ?>

                    <div class="row">
                        <!-- Title -->
                        <div class="col-md-6 mb-3">
                            <label for="editTitle" class="form-label">Title <span class="text-danger">*</span></label>
                            <?= $this->Form->control('title', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => '',
                                'required' => true,
                                'id' => 'editTitle'
                            ]) ?>
                            <div class="invalid-feedback">Please provide a title.</div>
                        </div>

                        <!-- Name -->
                        <div class="col-md-6 mb-3">
                            <label for="editName" class="form-label">Full Name <span class="text-danger">*</span></label>
                            <?= $this->Form->control('name', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Enter your full name',
                                'required' => true,
                                'id' => 'editName'
                            ]) ?>
                            <div class="invalid-feedback">Please provide a valid name.</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Address Type -->
                        <div class="col-12 mb-3">
                            <label class="form-label">Address Type <span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type" id="editTypeHome" value="Home">
                                    <label class="form-check-label" for="editTypeHome">
                                        Home
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type" id="editTypeWork" value="Work">
                                    <label class="form-check-label" for="editTypeWork">
                                        Work
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Address Line 1 -->
                        <div class="col-md-8 mb-3">
                            <label for="editAddressLine1" class="form-label">Address Line 1 <span class="text-danger">*</span></label>
                            <?= $this->Form->control('address_line1', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Street address, building name',
                                'required' => true,
                                'id' => 'editAddressLine1'
                            ]) ?>
                            <div class="invalid-feedback">Please provide a valid address.</div>
                        </div>

                        <!-- House Number -->
                        <div class="col-md-4 mb-3">
                            <label for="editHouseNo" class="form-label">House/Flat No.</label>
                            <?= $this->Form->control('house_no', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'House/Flat number',
                                'id' => 'editHouseNo'
                            ]) ?>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Address Line 2 -->
                        <div class="col-12 mb-3">
                            <label for="editAddressLine2" class="form-label">Address Line 2 (Optional)</label>
                            <?= $this->Form->control('address_line2', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Apartment, suite, unit, building, floor, etc.',
                                'id' => 'editAddressLine2'
                            ]) ?>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Landmark -->
                        <div class="col-md-6 mb-3">
                            <label for="editLandmark" class="form-label">Landmark</label>
                            <?= $this->Form->control('landmark', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Nearby landmark',
                                'id' => 'editLandmark'
                            ]) ?>
                        </div>

                        <!-- Zipcode -->
                        <div class="col-md-6 mb-3">
                            <label for="editZipcode" class="form-label">Zipcode <span class="text-danger">*</span></label>
                            <?= $this->Form->control('zipcode', [
                                'type' => 'text',
                                'label' => false,
                                'class' => 'form-control',
                                'placeholder' => 'Postal code',
                                'required' => true,
                                'id' => 'editZipcode'
                            ]) ?>
                            <div class="invalid-feedback">Please provide a valid zipcode.</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Country -->
                        <div class="col-md-4 mb-3">
                            <label for="editCountryId" class="form-label">Country <span class="text-danger">*</span></label>
                            <?= $this->Form->control('country_id', [
                                'type' => 'select',
                                'label' => false,
                                'class' => 'form-select',
                                'id' => 'editCountrySelect',
                                'options' => $countries ?? [],
                                'empty' => 'Select Country',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please select a country.</div>
                        </div>

                        <!-- State -->
                        <div class="col-md-4 mb-3">
                            <label for="editStateId" class="form-label">State <span class="text-danger">*</span></label>
                            <?= $this->Form->control('state_id', [
                                'type' => 'select',
                                'label' => false,
                                'class' => 'form-select',
                                'id' => 'editStateSelect',
                                'options' => [],
                                'empty' => 'Select State',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please select a state.</div>
                        </div>

                        <!-- City -->
                        <div class="col-md-4 mb-3">
                            <label for="editCityId" class="form-label">City <span class="text-danger">*</span></label>
                            <?= $this->Form->control('city_id', [
                                'type' => 'select',
                                'label' => false,
                                'class' => 'form-select',
                                'id' => 'editCitySelect',
                                'options' => [],
                                'empty' => 'Select City',
                                'required' => true
                            ]) ?>
                            <div class="invalid-feedback">Please select a city.</div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Phone Number 1 -->
                        <div class="col-md-6 mb-3">
                            <label for="editPhoneNo1" class="form-label">Phone Number <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <?= $this->Form->control('country_code1', [
                                    'type' => 'select',
                                    'label' => false,
                                    'class' => 'form-select',
                                    'options' => ['+974' => '+974', '+91' => '+91', '+966' => '+966'],
                                    'style' => 'max-width: 120px;',
                                    'id' => 'editCountryCode1'
                                ]) ?>
                                <?= $this->Form->control('phone_no1', [
                                    'type' => 'tel',
                                    'label' => false,
                                    'class' => 'form-control',
                                    'placeholder' => 'Phone number',
                                    'required' => true,
                                    'id' => 'editPhoneNo1'
                                ]) ?>
                            </div>
                            <div class="invalid-feedback">Please provide a valid phone number.</div>
                        </div>

                        <!-- Phone Number 2 (Optional) -->
                        <div class="col-md-6 mb-3">
                            <label for="editPhoneNo2" class="form-label">Alternate Phone (Optional)</label>
                            <div class="input-group">
                                <?= $this->Form->control('country_code2', [
                                    'type' => 'select',
                                    'label' => false,
                                    'class' => 'form-select',
                                    'options' => ['+974' => '+974', '+91' => '+91', '+966' => '+966'],
                                    'style' => 'max-width: 120px;',
                                    'id' => 'editCountryCode2'
                                ]) ?>
                                <?= $this->Form->control('phone_no2', [
                                    'type' => 'tel',
                                    'label' => false,
                                    'class' => 'form-control',
                                    'placeholder' => 'Alternate phone number',
                                    'id' => 'editPhoneNo2'
                                ]) ?>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden fields -->
                    <?= $this->Form->hidden('customer_id', ['value' => $customerId]) ?>
                    <?= $this->Form->hidden('status', ['value' => 'A']) ?>
                    <?= $this->Form->hidden('latitude', ['value' => '0.0']) ?>
                    <?= $this->Form->hidden('longitude', ['value' => '0.0']) ?>
                    <?= $this->Form->hidden('municipality_id', ['value' => 1]) ?>

                    <?= $this->Form->end() ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="editAddressForm" class="btn btn-primary">Update Address</button>
                </div>
            </div>
        </div>
    </div>

    <style>
    /* Custom styles for the address modal */
    #addAddressModal .modal-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    #addAddressModal .modal-title {
        color: #355C3F;
        font-weight: 600;
    }

    #addAddressModal .form-label {
        font-weight: 500;
        color: #333;
        margin-bottom: 0.5rem;
    }

    #addAddressModal .form-control,
    #addAddressModal .form-select {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.75rem;
    }

    #addAddressModal .form-control:focus,
    #addAddressModal .form-select:focus {
        border-color: #355C3F;
        box-shadow: 0 0 0 0.2rem rgba(53, 92, 63, 0.25);
    }

    #addAddressModal .btn-primary {
        background-color: #355C3F;
        border-color: #355C3F;
    }

    #addAddressModal .btn-primary:hover {
        background-color: #2a4730;
        border-color: #2a4730;
    }

    #addAddressModal .text-danger {
        color: #dc3545 !important;
    }

    #addAddressModal .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #dc3545;
    }

    #addAddressModal .was-validated .form-control:invalid,
    #addAddressModal .was-validated .form-select:invalid {
        border-color: #dc3545;
    }

    #addAddressModal .was-validated .form-control:valid,
    #addAddressModal .was-validated .form-select:valid {
        border-color: #28a745;
    }

    #addAddressModal .form-check-input:checked {
        background-color: #355C3F;
        border-color: #355C3F;
    }

    #addAddressModal .input-group .form-select {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    #addAddressModal .input-group .form-control {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    /* Radio button styling */
    #addAddressModal .form-check {
        margin-right: 1rem;
    }

    #addAddressModal .form-check-input {
        margin-top: 0.25rem;
        margin-right: 0.5rem;
    }

    #addAddressModal .form-check-label {
        margin-bottom: 0;
        cursor: pointer;
    }

    #addAddressModal .d-flex.gap-4 {
        gap: 1.5rem !important;
    }

    /* Edit Address Modal - Same styles as Add Modal */
    #editAddressModal .modal-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    #editAddressModal .modal-title {
        color: #355C3F;
        font-weight: 600;
    }

    #editAddressModal .form-label {
        font-weight: 500;
        color: #333;
        margin-bottom: 0.5rem;
    }

    #editAddressModal .form-control,
    #editAddressModal .form-select {
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        padding: 0.75rem;
    }

    #editAddressModal .form-control:focus,
    #editAddressModal .form-select:focus {
        border-color: #355C3F;
        box-shadow: 0 0 0 0.2rem rgba(53, 92, 63, 0.25);
    }

    #editAddressModal .btn-primary {
        background-color: #355C3F;
        border-color: #355C3F;
    }

    #editAddressModal .btn-primary:hover {
        background-color: #2a4730;
        border-color: #2a4730;
    }

    #editAddressModal .text-danger {
        color: #dc3545 !important;
    }

    #editAddressModal .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #dc3545;
    }

    #editAddressModal .was-validated .form-control:invalid,
    #editAddressModal .was-validated .form-select:invalid {
        border-color: #dc3545;
    }

    #editAddressModal .was-validated .form-control:valid,
    #editAddressModal .was-validated .form-select:valid {
        border-color: #28a745;
    }

    #editAddressModal .form-check-input:checked {
        background-color: #355C3F;
        border-color: #355C3F;
    }

    #editAddressModal .input-group .form-select {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    #editAddressModal .input-group .form-control {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    #editAddressModal .form-check {
        margin-right: 1rem;
    }

    #editAddressModal .form-check-input {
        margin-top: 0.25rem;
        margin-right: 0.5rem;
    }

    #editAddressModal .form-check-label {
        margin-bottom: 0;
        cursor: pointer;
    }

    #editAddressModal .d-flex.gap-4 {
        gap: 1.5rem !important;
    }

    /* Enhanced Address Card Styles */
    .address-card-enhanced {
        transition: all 0.2s ease;
        background: #ffffff;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        border: 1px solid #e9ecef !important;
        max-width: 100%;
    }

    .address-card-enhanced:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
    }

    .address-card-enhanced.border-success {
        border-color: #28a745 !important;
        background: #ffffff;
    }

    .bg-success-light {
        background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%) !important;
    }

    .address-card-enhanced .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    .address-card-enhanced .address-header h6 {
        color: #2c3e50;
        font-size: 1rem;
    }

    .address-card-enhanced .address-text {
        line-height: 1.4;
    }

    .address-card-enhanced .address-text p {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .address-card-enhanced .contact-info {
        background: transparent;
        border-radius: 0;
        padding: 0;
        margin: 0;
    }

    .address-card-enhanced .contact-info a {
        transition: color 0.2s ease;
    }

    .address-card-enhanced .contact-info a:hover {
        color: #28a745 !important;
    }

    .address-card-enhanced .contact-info .address-text p {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .address-card-enhanced .badge {
        font-size: 0.7rem;
        padding: 0.3em 0.6em;
        border-radius: 12px;
    }

    .address-card-enhanced .address-actions .btn {
        border-radius: 15px;
        padding: 0.3rem 0.8rem;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .address-card-enhanced .address-actions .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 1px 4px rgba(40, 167, 69, 0.2);
    }

    .address-card-enhanced .fas {
        width: 14px;
        text-align: center;
    }

    /* Radio button styling */
    .address-card-enhanced .form-check-input {
        width: 1.1em;
        height: 1.1em;
        border-width: 2px;
        cursor: pointer;
    }

    .address-card-enhanced .form-check-input:focus {
        border-color: #28a745;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    /* Empty state styling */
    .text-center.py-5 {
        background: #f8fff9;
        border-radius: 8px;
        padding: 2rem 1rem !important;
        border: 1px dashed #28a745;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .address-card-enhanced {
            padding: 0.75rem !important;
        }

        .address-card-enhanced .form-check {
            position: static !important;
            margin: 0 0 0.5rem 0 !important;
        }

        .address-card-enhanced .address-actions {
            flex-direction: column;
            gap: 0.25rem !important;
        }

        .address-card-enhanced .address-actions .btn {
            width: 100%;
        }
    }

    /* Animation for selected address */
    .address-card-enhanced.border-success::before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(45deg, #28a745, #20c997, #28a745);
        border-radius: inherit;
        z-index: -1;
        opacity: 0.1;
        animation: borderGlow 3s ease-in-out infinite alternate;
    }

    @keyframes borderGlow {
        0% { opacity: 0.1; }
        100% { opacity: 0.2; }
    }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const addAddressForm = document.getElementById('addAddressForm');
        const addAddressModal = new bootstrap.Modal(document.getElementById('addAddressModal'));
        const editAddressForm = document.getElementById('editAddressForm');
        const editAddressModal = new bootstrap.Modal(document.getElementById('editAddressModal'));

        // Enhanced address card interactions
        const addressCards = document.querySelectorAll('.address-card-enhanced');
        const addressRadios = document.querySelectorAll('input[name="address"]');

        // Handle address card selection
        addressRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Remove selected styling from all cards
                addressCards.forEach(card => {
                    card.classList.remove('border-success', 'bg-success-light');
                    card.classList.add('border-secondary');
                });

                // Add selected styling to the selected card
                if (this.checked) {
                    const selectedCard = this.closest('.address-card-enhanced');
                    if (selectedCard) {
                        selectedCard.classList.remove('border-secondary');
                        selectedCard.classList.add('border-success', 'bg-success-light');
                    }
                }
            });
        });

        // Make entire card clickable (except action buttons)
        addressCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // Don't trigger if clicking on action buttons
                if (e.target.closest('.address-actions') || e.target.closest('.form-check-input')) {
                    return;
                }

                const radio = this.querySelector('input[name="address"]');
                if (radio && !radio.checked) {
                    radio.checked = true;
                    radio.dispatchEvent(new Event('change'));
                }
            });

            // Add hover effect
            card.addEventListener('mouseenter', function() {
                if (!this.querySelector('input[name="address"]').checked) {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';
                }
            });

            card.addEventListener('mouseleave', function() {
                if (!this.querySelector('input[name="address"]').checked) {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                }
            });
        });

        // Form validation
        addAddressForm.addEventListener('submit', function(event) {
            event.preventDefault();
            event.stopPropagation();

            if (addAddressForm.checkValidity()) {
                submitAddressForm();
            } else {
                addAddressForm.classList.add('was-validated');
            }
        });

        function submitAddressForm() {
            const formData = new FormData(addAddressForm);
            const submitButton = document.querySelector('button[form="addAddressForm"]');
            const originalText = submitButton.textContent;

            // Show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Saving...';

            fetch(addAddressForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    // Close modal
                    addAddressModal.hide();

                    // Reset form
                    addAddressForm.reset();
                    addAddressForm.classList.remove('was-validated');

                    // Show success message and reload page to show new address
                   // alert('Address added successfully!');
                    window.location.reload();
                } else {
                    // Show detailed error message
                    let errorMsg = 'Error: ' + data.message;
                    if (data.errors) {
                        errorMsg += '\n\nDetailed errors:\n';
                        Object.keys(data.errors).forEach(field => {
                            errorMsg += field + ': ' + Object.values(data.errors[field]).join(', ') + '\n';
                        });
                    }
                    alert(errorMsg);
                    console.error('Validation errors:', data.errors);
                    console.error('Form data sent:', data.data);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving the address. Please check the console for details.');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            });
        }

        // Reset form when modal is closed
        document.getElementById('addAddressModal').addEventListener('hidden.bs.modal', function() {
            addAddressForm.reset();
            addAddressForm.classList.remove('was-validated');
        });

        // Edit Address Functionality
        document.querySelectorAll('.edit-address-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const addressId = this.getAttribute('data-address-id');
                loadAddressForEdit(addressId);
            });
        });

        function loadAddressForEdit(addressId) {
            fetch(`/cart/edit-address/${addressId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    populateEditForm(data.address);
                    editAddressModal.show();
                } else {
                    alert('Error loading address: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while loading the address.');
            });
        }

        function populateEditForm(address) {
            // Set the address ID
            document.getElementById('editAddressId').value = address.id;

            // Update form action to include the address ID
            editAddressForm.action = `/cart/edit-address/${address.id}`;

            // Populate form fields
            document.getElementById('editTitle').value = address.title || '';
            document.getElementById('editName').value = address.name || '';
            document.getElementById('editAddressLine1').value = address.address_line1 || '';
            document.getElementById('editHouseNo').value = address.house_no || '';
            document.getElementById('editAddressLine2').value = address.address_line2 || '';
            document.getElementById('editLandmark').value = address.landmark || '';
            document.getElementById('editZipcode').value = address.zipcode || '';
            document.getElementById('editPhoneNo1').value = address.phone_no1 || '';
            document.getElementById('editPhoneNo2').value = address.phone_no2 || '';
            document.getElementById('editCountryCode1').value = address.country_code1 || '+974';
            document.getElementById('editCountryCode2').value = address.country_code2 || '+974';

            // Set address type radio buttons
            if (address.type === 'Home') {
                document.getElementById('editTypeHome').checked = true;
            } else if (address.type === 'Work') {
                document.getElementById('editTypeWork').checked = true;
            }

            // Set dynamic country, state, city selections
            setEditFormSelections(address.country_id || 1, address.state_id || '', address.city_id || '');
        }

        // Edit form validation and submission
        editAddressForm.addEventListener('submit', function(event) {
            event.preventDefault();
            event.stopPropagation();

            if (editAddressForm.checkValidity()) {
                submitEditAddressForm();
            } else {
                editAddressForm.classList.add('was-validated');
            }
        });

        function submitEditAddressForm() {
            const formData = new FormData(editAddressForm);
            const submitButton = document.querySelector('button[form="editAddressForm"]');
            const originalText = submitButton.textContent;

            // Show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Updating...';

            fetch(editAddressForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Edit response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Edit response data:', data);
                if (data.success) {
                    // Close modal
                    editAddressModal.hide();

                    // Reset form
                    editAddressForm.reset();
                    editAddressForm.classList.remove('was-validated');

                    // Show success message and reload page
                    //alert('Address updated successfully!');
                    window.location.reload();
                } else {
                    // Show detailed error message
                    let errorMsg = 'Error: ' + data.message;
                    if (data.errors) {
                        errorMsg += '\n\nDetailed errors:\n';
                        Object.keys(data.errors).forEach(field => {
                            errorMsg += field + ': ' + Object.values(data.errors[field]).join(', ') + '\n';
                        });
                    }
                    alert(errorMsg);
                    console.error('Validation errors:', data.errors);
                    console.error('Form data sent:', data.data);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the address. Please check the console for details.');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            });
        }

        // Reset edit form when modal is closed
        document.getElementById('editAddressModal').addEventListener('hidden.bs.modal', function() {
            editAddressForm.reset();
            editAddressForm.classList.remove('was-validated');
        });

        // Simple dynamic address data from model
        const allStates = <?= json_encode($statesWithCountries ?? []) ?>;
        const allCities = <?= json_encode($citiesWithStates ?? []) ?>;

        // Simple function to populate states
        function populateStates(countryId, stateSelect, citySelect) {
            stateSelect.innerHTML = '<option value="">Select State</option>';
            citySelect.innerHTML = '<option value="">Select City</option>';

            if (countryId && allStates) {
                allStates.forEach(state => {
                    if (state.country_id == countryId) {
                        const option = document.createElement('option');
                        option.value = state.id;
                        option.textContent = state.state_name;
                        stateSelect.appendChild(option);
                    }
                });
            }
        }

        // Simple function to populate cities
        function populateCities(stateId, citySelect) {
            citySelect.innerHTML = '<option value="">Select City</option>';

            if (stateId && allCities) {
                allCities.forEach(city => {
                    if (city.state_id == stateId) {
                        const option = document.createElement('option');
                        option.value = city.id;
                        option.textContent = city.city_name;
                        citySelect.appendChild(option);
                    }
                });
            }
        }

        // Add Address Modal events
        const addCountrySelect = document.getElementById('addCountrySelect');
        const addStateSelect = document.getElementById('addStateSelect');
        const addCitySelect = document.getElementById('addCitySelect');

        if (addCountrySelect) {
            addCountrySelect.addEventListener('change', function() {
                populateStates(this.value, addStateSelect, addCitySelect);
            });
        }

        if (addStateSelect) {
            addStateSelect.addEventListener('change', function() {
                populateCities(this.value, addCitySelect);
            });
        }

        // Edit Address Modal events
        const editCountrySelect = document.getElementById('editCountrySelect');
        const editStateSelect = document.getElementById('editStateSelect');
        const editCitySelect = document.getElementById('editCitySelect');

        if (editCountrySelect) {
            editCountrySelect.addEventListener('change', function() {
                populateStates(this.value, editStateSelect, editCitySelect);
            });
        }

        if (editStateSelect) {
            editStateSelect.addEventListener('change', function() {
                populateCities(this.value, editCitySelect);
            });
        }

        // Simple function for edit form selections
        window.setEditFormSelections = function(countryId, stateId, cityId) {
            if (editCountrySelect && countryId) {
                editCountrySelect.value = countryId;
                populateStates(countryId, editStateSelect, editCitySelect);

                setTimeout(() => {
                    if (editStateSelect && stateId) {
                        editStateSelect.value = stateId;
                        populateCities(stateId, editCitySelect);

                        setTimeout(() => {
                            if (editCitySelect && cityId) {
                                editCitySelect.value = cityId;
                            }
                        }, 100);
                    }
                }, 100);
            }
        }
    });

    // Function to proceed to checkout with selected address
    function proceedToCheckout() {

         const acceptTerms = document.getElementById('acceptTerms');
          if (!acceptTerms.checked) {
            showCodOrderMessage('Please accept the terms and conditions to proceed', 'error');
            return;
        }
        const selectedAddress = document.querySelector('input[name="address"]:checked');

        if (!selectedAddress) {
            alert('Please select an address to proceed with checkout.');
            return;
        }

        const addressId = selectedAddress.value;
        const checkoutUrl = '<?= $this->Url->build(['controller' => 'Cart', 'action' => 'checkout']) ?>';

        // Redirect to checkout with selected address ID
        window.location.href = checkoutUrl + '?address_id=' + addressId;
    }
      function showCodOrderMessage(message, type) {
        const codOrderMessage = document.getElementById('codOrderMessage');
        codOrderMessage.textContent = message;
        codOrderMessage.className = `mt-3 alert ${type === 'success' ? 'alert-success' : 'alert-danger'}`;
        codOrderMessage.style.display = 'block';

        // Hide message after 5 seconds (except for success messages)
        if (type !== 'success') {
            setTimeout(() => {
                codOrderMessage.style.display = 'none';
            }, 5000);
        }
    }
    </script>