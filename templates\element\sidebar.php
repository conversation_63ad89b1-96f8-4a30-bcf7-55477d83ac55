<div class="main-sidebar sidebar-style-2">
    <aside id="sidebar-wrapper">
        <div class="sidebar-brand">
            <a href="#"> <img alt="image" src="<?= $this->Url->webroot('img/admin_logo.png') ?>"
                    class="header-logo" />
            </a>
        </div>
        <div class="sidebar-user">
            <div class="sidebar-user-picture">
                <img alt="image" src="<?= $this->Url->webroot('img/user.png') ?>">
            </div>
            <div class="sidebar-user-details">
                <?php if (isset($user)): ?>
                    <div class="user-name"><?= h($user->first_name . ' ' . $user->last_name) ?></div>
                    <div class="user-role"><?= h($user->role->name) ?></div>
                <?php endif; ?>
                <div class="sidebar-userpic-btn">
                    <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'editProfile', $user->id]) ?>" data-bs-toggle="tooltip" title="Profile">
                        <i data-feather="user"></i>
                    </a>
                    <a href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'logout']) ?>"
                        data-bs-toggle="tooltip" title="Log Out">
                        <i data-feather="log-out"></i>
                    </a>
                </div>
            </div>
        </div>
        <?php $controller = $this->request->getParam('controller'); ?>
        <ul class="sidebar-menu">
            <li class="menu-header">Menus</li>
            <?php if (in_array('Dashboards', $accessibleModules)): ?>
                <li class="dropdown">
                    <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>"
                        class="nav-link"><i data-feather="clipboard"></i><span>Dashboard</span></a>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['Brands', 'Categories', 'Products', 'ComingSoon','ProductDeals'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Brands', 'Categories', 'Products', 'ComingSoon','ProductDeals']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="list"></i><span>Catalog</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Brands', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Brands' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'Brands', 'action' => 'index']) ?>" class="nav-link"><?= __('Brands') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Categories', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Categories' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'Categories', 'action' => 'index']) ?>" class="nav-link"><?= __('Categories') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (count(array_intersect($accessibleModules, ['Products', 'ProductDeals'])) > 0): ?>
                            <li class="dropdown <?= in_array($controller, ['Products', 'ProductDeals']) ? 'active' : '' ?>">
                                <a href="#" class="has-dropdown">Manage Products</a>

                                <ul class="dropdown-menu">
                                    <?php if (in_array('Products', $accessibleModules)): ?>
                                        <li class="<?= $controller === 'Products' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'Products', 'action' => 'index']) ?>">Products</a></li>
                                    <?php endif; ?>
                                    <?php if (in_array('ProductDeals', $accessibleModules)): ?>
                                        <li class="<?= $controller === 'ProductDeals' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'ProductDeals', 'action' => 'index']) ?>">Deals of the Day</a></li>
                                    <?php endif; ?>
                                </ul>
                            </li>
                        <?php endif; ?>
                       
                    </ul>
                </li>
            <?php endif; ?>
            <li class="menu-header"><?= __('Components') ?></li>
            <?php if (count(array_intersect($accessibleModules, ['Banners', 'Widgets', 'BannerAds', 'ContentPages', 'Faqs'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Banners', 'Widgets', 'BannerAds', 'ContentPages', 'Faqs']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="layout"></i><span>Design</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Banners', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Banners' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Banners', 'action' => 'index']) ?>"><?= __('Banners') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('BannerAds', $accessibleModules)): ?>
                            <li class="<?= $controller === 'BannerAds' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'BannerAds', 'action' => 'index']) ?>"><?= __('Ad Blocks') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (count(array_intersect($accessibleModules, ['ContentPages', 'Faqs'])) > 0): ?>
                            <!-- <li class="dropdown">
                            <a href="#" class="has-dropdown"><span>CMS Pages</span></a>
                            <ul class="dropdown-menu"> -->
                            <li class="dropdown <?= in_array($controller, ['ContentPages', 'Faqs']) ? 'active' : '' ?>">
                                <a href="#" class="has-dropdown">Website Content</a>

                                <ul class="dropdown-menu">
                                    <?php if (in_array('ContentPages', $accessibleModules)): ?>
                                        <li class="<?= $controller === 'ContentPages' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'ContentPages', 'action' => 'index']) ?>">CMS Pages</a></li>
                                    <?php endif; ?>
                                    <?php if (in_array('Faqs', $accessibleModules)): ?>
                                        <li class="<?= $controller === 'Faqs' ? 'active' : '' ?>"><a href="<?= $this->Url->build(['controller' => 'Faqs', 'action' => 'index']) ?>">FAQs</a></li>
                                    <?php endif; ?>
                                </ul>

                            </li>
                            <!-- </ul>
                        </li> -->
                        <?php endif; ?>
                        <?php if (in_array('Widgets', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Widgets' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Widgets', 'action' => 'index']) ?>"><?= __('Widgets') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['Users', 'Roles', 'Modules'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Users', 'Roles', 'Modules']) ? 'active' : '' ?>">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="briefcase"></i><span>Users</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Users', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Users' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'Users', 'action' => 'index']) ?>"><?= __('Users') ?></a></li>
                        <?php endif; ?>
                        <?php if (in_array('Roles', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Roles' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'Roles', 'action' => 'index']) ?>"><?= __('Roles') ?></a>
                            </li>
                        <?php endif; ?>
                        <!-- <li class="<?php //$controller === 'Modules' ? 'active' : ''
                                        ?>"><a class="nav-link"
                                href="<?php //$this->Url->build(['controller' => 'Modules', 'action' => 'index'])
                                        ?>">Manage
                                Modules</a>
                        </li> -->
                    </ul>
                </li>
            <?php endif; ?>

            <?php if (count(array_intersect($accessibleModules, ['Customers', 'CustomerGroups'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Customers']) ? 'active' : '' ?>">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="briefcase"></i><span>Customers</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Customers', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Customers' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'Customers', 'action' => 'index']) ?>"><?= __('Customers') ?></a></li>
                        <?php endif; ?>
                        <?php if (in_array('CustomerGroups', $accessibleModules)): ?>
                            <li class="<?= $controller === 'CustomerGroups' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'CustomerGroups', 'action' => 'index']) ?>"><?= __('Customer Groups') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>


            <?php if (count(array_intersect($accessibleModules, ['Showrooms', 'Expenses'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Showrooms', 'Expenses']) ? 'active' : '' ?>">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="home"></i><span>Showrooms</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Showrooms', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Showrooms' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'Showrooms', 'action' => 'index']) ?>" class="nav-link"><span>Showrooms</span></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Expenses', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Expenses' ? 'active' : '' ?>"><a class="nav-link"
                                    href="<?= $this->Url->build(['controller' => 'Expenses', 'action' => 'index']) ?>"><?= __('Expenses') ?></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (in_array('Showrooms', $accessibleModules)): ?>
            <?php endif; ?>
            <?php if (in_array('Warehouses', $accessibleModules)): ?>
                <li class="<?= in_array($controller, ['Warehouses']) ? 'active' : '' ?>">
                    <a href="<?= $this->Url->build(['controller' => 'Warehouses', 'action' => 'index']) ?>" class="nav-link"><i data-feather="home"></i><span>Warehouses</span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Suppliers', $accessibleModules)): ?>
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Suppliers', 'action' => 'index']) ?>" class="nav-link"><i data-feather="box"></i><span>Suppliers</span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Zones', $accessibleModules)): ?>
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Zones', 'action' => 'index']) ?>" class="nav-link"><i data-feather="map-pin"></i><span>Zones</span></a>
                </li>
            <?php endif; ?>
            <!-- < ?php if (in_array('ManageDeliveries', $accessibleModules)): ?>
                <li>
                    <a href="< ?= $this->Url->build(['controller' => 'ManageDeliveries', 'action' => 'index']) ?>" class="nav-link"><i data-feather="truck"></i><span>Deliveries</span></a>
                </li>
            < ?php endif; ?> -->
            <?php if (count(array_intersect($accessibleModules, ['Stocks'])) > 0): ?>
                <li class="dropdown">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="box"></i><span>Stock Management</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Stocks', $accessibleModules)): ?>
                            <li class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'Stocks', 'action' => 'index']) ?>">Product Stock List</a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('ShowroomStockRequests', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'ShowroomStockRequests', 'action' => 'index']) ?>">Stock Requests from Showroom</a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('WarehouseStockRequests', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'WarehouseStockRequests', 'action' => 'index']) ?>">Stock Requests from Warehouse</a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Stocks', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'ShowroomStockIncoming', 'action' => 'index']) ?>">Incoming Stock to Showroom</a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Stocks', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'WarehouseStockIncoming', 'action' => 'index']) ?>">Incoming Stock to Warehouse</a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Stocks', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'WarehouseStockOutgoing', 'action' => 'index']) ?>">Outgoing Stock from Warehouse</a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Stocks', $accessibleModules)): ?>
                            <li style="width: max-content;" class=""><a class="nav-link" href="<?= $this->Url->build(['controller' => 'ShowroomStockOutgoing', 'action' => 'index']) ?>">Outgoing Stock from Showroom</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['Reports'])) > 0): ?>
                <li class="dropdown <?= $controller === 'Reports' ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="file-text"></i><span>Reports</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Reports', $accessibleModules)): ?>
                            <!-- <li class="">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'orderReport']) ?>">
                                    <i data-feather="bar-chart-2"></i>Order Report
                                </a>
                            </li> -->
                            <li class="dropdown-divider"></li>
                            <li class="dropdown-header">Sales & Revenue Reports</li>
                            <li class="">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'salesReports']) ?>">
                                    <i data-feather="trending-up"></i>Sales & Revenue 
                                </a>
                            </li>
                            <li class="">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'revenueDetails']) ?>">
                                    <i data-feather="dollar-sign"></i>Revenue Details
                                </a>
                            </li>
                            <li class="">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'salesDetails']) ?>">
                                    <i data-feather="shopping-cart"></i>Sales Details
                                </a>
                            </li>
                            <!-- <li class="dropdown-divider"></li>
                            <li class="dropdown-header">Export Reports</li>
                            <li>
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'exportRevenue']) ?>" target="_blank">
                                    <i data-feather="download"></i>Export Revenue CSV
                                </a>
                            </li>
                            <li>
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Reports', 'action' => 'exportSales']) ?>" target="_blank">
                                    <i data-feather="download"></i>Export Sales CSV
                                </a>
                            </li> -->
                        <?php endif; ?>

                    </ul>
                </li>
            <?php endif; ?>
            <?php if (in_array('Drivers', $accessibleModules)): ?>
                <li>
                    <a href="<?= $this->Url->build(['controller' => 'Drivers', 'action' => 'index']) ?>" class="nav-link"><i data-feather="truck"></i><span>Drivers</span></a>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['Orders', 'AssignOrders'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['Orders']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="shopping-cart"></i><span>Orders</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('Orders', $accessibleModules)): ?>
                            <li class="<?= $controller === 'Orders' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Orders', 'action' => 'index']) ?>"><?= __('Orders') ?></a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('Shipment', $accessibleModules)): ?>
                            <!-- <li class="<?= $controller === 'Shipment' ? 'active' : '' ?>">
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'Shipment', 'action' => 'index']) ?>"><?= __('Shipments') ?></a>
                            </li> -->
                        <?php endif; ?>
                        <?php if (in_array('ShipmentsAssignments', $accessibleModules)): ?>
                            <!-- <li>
                                <a class="nav-link" href="<?= $this->Url->build(['controller' => 'ShipmentsAssignments', 'action' => 'index']) ?>">Order Assignment</a>
                            </li> -->
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (in_array('Reviews', $accessibleModules)): ?>
                <li>
                    <a href="#" class="nav-link"><i data-feather="star"></i><span>Reviews</span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Offers', $accessibleModules)): ?>
                <li class="<?= $controller === 'Offers' ? 'active' : '' ?>">
                    <a href="<?= $this->Url->build(['controller' => 'Offers', 'action' => 'index']) ?>" class="nav-link"><i data-feather="tag"></i><span><?= __('Coupons') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (in_array('Partners', $accessibleModules)): ?>
                <li class="<?= $controller === 'Partners' ? 'active' : '' ?>">
                    <a href="<?= $this->Url->build(['controller' => 'Partners', 'action' => 'index']) ?>" class="nav-link"><i data-feather="tag"></i><span><?= __('Partners') ?></span></a>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['SupportCategories'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['SupportCategories']) ? 'active' : '' ?>">
                    <a href="#" class="menu-toggle nav-link has-dropdown"><i
                            data-feather="headphones"></i><span>Manage Support</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('SupportCategories', $accessibleModules)): ?>
                            <li class="<?= $controller === 'SupportCategories' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'SupportCategories', 'action' => 'index']) ?>" class="nav-link"><span>Support Categories</span></a>
                            </li>

                            <li class="<?= $controller === 'SupportTickets' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'SupportTickets', 'action' => 'index']) ?>" class="nav-link"><span>Support Tickets</span></a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if (count(array_intersect($accessibleModules, ['SiteSettings', 'SiteThemes'])) > 0): ?>
                <li class="dropdown <?= in_array($controller, ['SiteSettings', 'SiteThemes']) ? 'active' : '' ?>">
                    <a href="#" class="nav-link has-dropdown menu-toggle" data-toggle="dropdown"><i
                            data-feather="shopping-cart"></i><span>Site Settings</span></a>
                    <ul class="dropdown-menu">
                        <?php if (in_array('SiteSettings', $accessibleModules)): ?>
                            <li class="<?= $controller === 'SiteSettings' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'SiteSettings', 'action' => 'index']) ?>" class="nav-link">Global Settings</a>
                            </li>
                        <?php endif; ?>
                        <?php if (in_array('SiteThemes', $accessibleModules)): ?>
                            <li class="<?= $controller === 'SiteThemes' ? 'active' : '' ?>">
                                <a href="<?= $this->Url->build(['controller' => 'SiteThemes', 'action' => 'index']) ?>" class="nav-link">Theme Settings</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
        </ul>
    </aside>
</div>

<style>
/* Reports Dropdown Styling */
.dropdown-menu .dropdown-header {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0.5rem 1rem 0.25rem;
    margin-top: 0.25rem;
}

.dropdown-menu .dropdown-divider {
    margin: 0.5rem 0;
    border-top: 1px solid #e9ecef;
}

.dropdown-menu .nav-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.2s ease;
}

.dropdown-menu .nav-link:hover {
    background-color: #f8f9fa;
    color: #007bff;
    transform: translateX(2px);
}

.dropdown-menu .nav-link i {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    opacity: 0.7;
}

.dropdown-menu .nav-link:hover i {
    opacity: 1;
}

/* Reports Section Active State */
.dropdown.active > .nav-link {
    background-color: #007bff;
    color: white;
}

.dropdown.active .dropdown-menu .nav-link {
    color: #495057;
}

.dropdown.active .dropdown-menu .nav-link:hover {
    background-color: #e3f2fd;
    color: #1976d2;
}
</style>