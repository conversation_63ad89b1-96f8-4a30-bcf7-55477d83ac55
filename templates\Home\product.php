<style>
div#ajaxReviewsContainer {
    max-height: 500px;
    overflow-y: auto;
}
</style>

    <section class="my-1 my-md-5">
        <div class="container">
            <nav aria-label="breadcrumb" class="">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Home</a></li>
                    <?php if (!empty($productData['product_categories'][0]['category'])): ?>
                        <li class="breadcrumb-item">
                            <a href="/product-list/<?= h($productData['product_categories'][0]['category']['url_key']) ?>">
                                <?= h($productData['product_categories'][0]['category']['name']) ?>
                            </a>
                        </li>
                    <?php endif; ?>
                    <li class="breadcrumb-item active" aria-current="page"><?= $productData['name'] ?></li>
                </ol>
            </nav>
        </div>
    </section>
    <!-- Brand Banner -->
    <section class="product-details-banner py-lg-2">
        <div class="container ">
            <div class="row mobile-reverse-product">
                <!-- Product Info -->
                <div class="col-md-5">
                    <h1 class="fw-bold product-title"><?= $productData['name'] ?></h1>
                    <div class="rating mb-2 d-flex">
                        <?php
                            $avg = floatval($productData['avg_rating']);
                            $fullStars = floor($avg);
                            $halfStar = ($avg - $fullStars) >= 0.5 ? 1 : 0;
                            $emptyStars = 5 - $fullStars - $halfStar;
                            for ($i = 0; $i < $fullStars; $i++) {
                                echo '<i class="fas fa-star"></i>';
                            }
                            if ($halfStar) {
                                echo '<i class="fas fa-star-half-alt"></i>';
                            }
                            for ($i = 0; $i < $emptyStars; $i++) {
                                echo '<i class="far fa-star"></i>';
                            }
                        ?>
                        <p class="rating-value"><?= $productData['avg_rating'] ?> / 5.0</p>
                        <span class="text-muted ms-2">(<?= $productData['avg_rating'] ?>)</span>
                    </div>
                    <p><?= $productData['details'] ?></p>

                    <!-- <div class="d-flex model-text">
                        <p>Model: <strong>ASG18CAXTA-U</strong> </p>
                    </div> -->
                  
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            
                            <div class="">
                                <h1 class="price-range"><?= $this->Price->setPriceFormat($productData['promotion_price']) ?></h1>
                                <label class="form-label">Unit(s) without installation</label>
                            </div>
                            <?php if ($productData->is_in_cart && $productData->cart_quantity >= 1): ?>
                            <div class="input-group increment-decrement">
                                <button class="btn" type="button" onclick="updateQuantityDynamic('unitInput2', -1)"><img
                                        src="../../img/ozone/minus.png" class="img-fluid" /></button>
                                <input type="text" id="unitInput2" class="form-control text-center"
                                       value="<?= $productData->is_in_cart ? $productData->cart_quantity : 1 ?>"
                                       data-product-id="<?= $productData->id ?>"
                                       data-original-value="<?= $productData->is_in_cart ? $productData->cart_quantity : 1 ?>">
                                <button class="btn" type="button" onclick="updateQuantityDynamic('unitInput2', 1)"><img
                                        src="../../img/ozone/plus.png" class="img-fluid" /></button>
                            </div>
                         <?php endif; ?>
                        </div>
                        <!-- Installation Charge Checkbox -->
                        <div class="form-check mt-2">
                            <input class="form-check-input" type="checkbox" value="1" id="installationChargeCheckbox">
                            <label class="form-check-label" for="installationChargeCheckbox">
                                Add Installation Charge (₹<?= $this->Price->setPriceFormat($productData['installation_charge']) ?>)
                            </label>
                        </div>
                    </div>
                    <div class="purchase-cta">
                        <?php if (!$productData->is_in_cart): ?>
                            <button class="btn btn-add-cart w-100" id="addToCartBtn" data-product-id="<?= $productData->id ?>">
                                <i class="fas fa-cart-plus me-2"></i> Add to Cart
                            </button>
                        <?php else: ?>
                            <button class="btn btn-add-cart w-100" onclick="window.location.href='<?= $this->Url->build(['controller' => 'Cart', 'action' => 'cart']) ?>'">
                                <i class="fas fa-shopping-cart me-2"></i> Go to Cart
                            </button>
                        <?php endif; ?>
                        <div class="wishlist-container" data-product-id="<?= $productData->id ?>">
                            <?php if ($productData->whishlist): ?>
                                <button class="btn btn-wishlist w-100 <?= !$productData->is_in_cart ? 'mt-2' : '' ?> d-flex justify-content-center align-items-center remove-wishlist-btn">
                                    <i class="fas fa-heart text-danger me-2"></i> Remove from Wishlist
                                </button>
                            <?php else: ?>
                                <button class="btn btn-wishlist w-100 <?= !$productData->is_in_cart ? 'mt-2' : '' ?> d-flex justify-content-center align-items-center add-wishlist-btn">
                                    <i class="far fa-heart text-success me-2"></i> Add to Wishlist
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-7 product-gallery">
                    <!-- Main Swiper -->
                    <div class="swiper-counter">
                        <span class="current-slide">01</span> / <span class="total-slides">04</span>
                    </div>

                    <!-- Navigation buttons -->
                    <div class="swiper-navigation">
                        <div class="swiper-button-prev custom-nav"></div>
                        <div class="swiper-button-next custom-nav"></div>
                    </div>
                    <div class="swiper main-swiper">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <div class="tile" data-scale="1.6">
                                    <div class="photo"><img src="../../img/ozone/Meryl_Lounge-view.png"
                                            class="img-fluid" alt="Zoom 2" /></div>
                                    <div class="txt">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="tile" data-scale="1.6">
                                    <div class="photo"><img src="../../img/ozone/Meryl_Lounge-view.png"
                                            class="img-fluid" alt="Zoom 2" /></div>
                                    <div class="txt">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="tile" data-scale="1.6">
                                    <div class="photo"><img src="../../img/ozone/Meryl_Lounge-view.png"
                                            class="img-fluid" alt="Zoom 2" /></div>
                                    <div class="txt">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="tile" data-scale="1.6">
                                    <div class="photo"><img src="../../img/ozone/Meryl_Lounge-view.png"
                                            class="img-fluid" alt="Zoom 2" /></div>
                                    <div class="txt">
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="tile" data-scale="1.6">
                                    <div class="photo"><img src="../../img/ozone/Meryl_Lounge-view.png"
                                            class="img-fluid" alt="Zoom 2" /></div>
                                    <div class="txt">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="thumbnail-images">
                        <!-- Thumbnails -->
                        <div class="swiper thumb-swiper mt-3">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <img src="../../img/ozone/Meryl_Lounge-view.png" class="img-fluid" alt="Thumb 1">
                                </div>
                                <div class="swiper-slide">
                                    <img src="../../img/ozone/Meryl_Lounge-view.png" class="img-fluid" alt="Thumb 2">
                                </div>
                                <div class="swiper-slide">
                                    <img src="../../img/ozone/Meryl_Lounge-view.png" class="img-fluid" alt="Thumb 3">
                                </div>
                                <div class="swiper-slide">
                                    <img src="../../img/ozone/Meryl_Lounge-view.png" class="img-fluid" alt="Thumb 4">
                                </div>
                                <div class="swiper-slide">
                                    <img src="../../img/ozone/Meryl_Lounge-view.png" class="img-fluid" alt="Thumb 5">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Thumbnails -->
                </div>
            </div>
        </div>
    </section>

    <section class="scroll-spy my-5 desktop">
        <!-- Nav tabs -->
        <div class="container nav-tabs-scroll">
            <ul class="nav justify-content-around" id="scrollNav">
                <li class="nav-item">
                    <a class="nav-link active" href="#features">Features</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#gallery">Gallery</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#specs">Specifications</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#review">Review</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#help">Need Help?</a>
                </li>
            </ul>
        </div>
    </section>
    <!-- Leading Company Section -->

    <section class="product-features" id="features">
        <!-- Full-width feature image -->
       <?= $productData['features'] ?>
    </section>

    <section class="gallery mb-5" id="gallery">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-3 gap-3">
                <h1 class="sec-title fw-bold m-0">Gallery</h1>
                <button class="zoom-btn" onclick="toggleZoomFromTitle()">
                    <img id="zoom-icon"  class="img-fluid" src="../../img/ozone/search-zoom-in.png" alt="Zoom" width="60">
                </button>
            </div>

            <div id="gallerycarousel" class="carousel slide">
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <img src="../../img/ozone/Meryl_Lounge.png" class="d-block w-100 mb-5 pb-5 zoomable" alt="...">
                    </div>
                    <div class="carousel-item">
                        <img src="../../img/ozone/Meryl_Lounge.png" class="d-block w-100 mb-5 pb-5 zoomable" alt="...">
                    </div>
                    <div class="carousel-item">
                        <img src="../../img/ozone/Meryl_Lounge.png" class="d-block w-100 mb-5 pb-5 zoomable" alt="...">
                    </div>
                </div>

                <button class="carousel-control-prev" type="button" data-bs-target="#gallerycarousel"
                    data-bs-slide="prev">
                    <span class="carousel-control-prev" aria-hidden="true"><img src="../../img/ozone/prv.png"
                            class="img-fluid" /></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#gallerycarousel"
                    data-bs-slide="next">
                    <span class="carousel-control-next" aria-hidden="true"><img src="../../img/ozone/nxt-pro.png"
                            class="img-fluid" /></span>
                    <span class="visually-hidden">Next</span>
                </button>
            </div>
        </div>
    </section>

    <section class="Specifications my-5" id="specs">
        <div class="container">
            <h1 class="sec-title fw-bold my-4">
                Specifications
            </h1>
     
            <?= $productData['description'] ?>
           
        </div>
    </section>

    <section class="reviews" id="review">
        <div class="container my-5">
            <h2 class="sec-title fw-bold">Review <span class="float-end text-success"><?= $productData['avg_rating'] ?> ★</span></h2>

            <div class="row my-4">
                <div class="col-md-5">
                    <div class="rounded p-3 bg-white">
                        <div class="d-flex align-items-center">
                            <h4><?= $productData['avg_rating'] ?> <span class="star-rating">★★★★★</span></h4>
                            <p class="mb-1 total-rating">Based on <?= count($productData['reviews']) ?> ratings</p>
                        </div>
                        <div>
                            <h3 class="mt-3">Rating</h3>
                            <?php
                                $totalRatings = (
                                    (int)($productData['rating_1_count'] ?? 0) +
                                    (int)($productData['rating_2_count'] ?? 0) +
                                    (int)($productData['rating_3_count'] ?? 0) +
                                    (int)($productData['rating_4_count'] ?? 0) +
                                    (int)($productData['rating_5_count'] ?? 0)
                                );
                                $ratings = [
                                    5 => (int)($productData['rating_5_count'] ?? 0),
                                    4 => (int)($productData['rating_4_count'] ?? 0),
                                    3 => (int)($productData['rating_3_count'] ?? 0),
                                    2 => (int)($productData['rating_2_count'] ?? 0),
                                    1 => (int)($productData['rating_1_count'] ?? 0),
                                ];
                                foreach ($ratings as $star => $count):
                                    $percent = $totalRatings > 0 ? round(($count / $totalRatings) * 100) : 0;
                            ?>
                                <div class="mb-1 progress-d-flex">
                                    <pre class="ratings"><?= $star ?> Star </pre>
                                    <div class="progress" role="progressbar" aria-label="Basic example" aria-valuenow="<?= $percent ?>"
                                        aria-valuemin="0" aria-valuemax="100">
                                        <div class="progress-bar" style="width: <?= $percent ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-7 review-product d-flex align-items-center justify-content-center">
                    <div>
                        <h3 class="">Review this Product</h3>
                        <div class="fs-1 star-rating">★★★★★</div>
                        <p>Help other customers make their decision</p>
                    </div>
                </div>
            </div>

            <hr class="my-5">


            <div class="d-flex justify-content-between mb-3">
                <h4 class="customer-reviews-title">Customer Reviews</h4>
                <div>
                    <select id="reviewSortBy" class="form-select form-select-sm" style="width:auto;display:inline-block;">
                        <option value="newest">Newest First</option>
                        <option value="oldest">Oldest First</option>
                        <option value="highest">Highest Rated</option>
                        <option value="lowest">Lowest Rated</option>
                    </select>
                </div>
            </div>

            <!-- AJAX Reviews Container -->
            <div id="ajaxReviewsContainer"></div>
            <!-- No more Load More Button -->
            <div class="text-center mt-5 loadmore">
                <div id="reviewsLoadingSpinner" style="display:none;">
                    <span class="spinner-border spinner-border-sm"></span> Loading...
                </div>
                <div id="noMoreReviewsMsg" class="text-muted mt-3" style="display:none;">No more reviews found.</div>
                <div id="noReviewsMsg" class="alert alert-warning mt-3" style="display:none;">No reviews found.</div>
            </div>
        </div>
    </section>

    <section class="need-help" id="help">
        <div class="container py-5">
            <h2 class="sec-title fw-bold">Need help?</h2>
            <p class="text-success">We’re here to provide all the help you need.</p>

            <div class="row g-4 mt-4">
                <!-- Card 1 -->
                <div class="col-md-6 col-lg-3">
                    <div class="p-4 help-card h-100">
                        <h5 class="fw-bold mb-4">Download<br>Product Manual</h5>
                        <p class="mb-5">Download product manuals and latest software for your product.</p>
                        <div class="arrow-circle mt-3"><img src="../../img/ozone/arrow-left.svg" class="img-fluid" />
                        </div>
                    </div>
                </div>

                <!-- Card 2 -->
                <div class="col-md-6 col-lg-3">
                    <div class="p-4 help-card h-100">
                        <h5 class="fw-bold mb-4">Troubleshoot</h5>
                        <p class="mb-5">Find helpful how-to videos for your product.</p>
                        <div class="arrow-circle mt-3"><img src="../../img/ozone/arrow-left.svg" class="img-fluid" />
                        </div>
                    </div>
                </div>

                <!-- Card 3 -->
                <div class="col-md-6 col-lg-3">
                    <div class="p-4 help-card h-100">
                        <h5 class="fw-bold mb-4">Warranty</h5>
                        <p class="mb-5">Check your product warranty information here.</p>
                        <div class="arrow-circle mt-3"><img src="../../img/ozone/arrow-left.svg" class="img-fluid" />
                        </div>
                    </div>
                </div>

                <!-- Card 4 -->
                <div class="col-md-6 col-lg-3">
                    <div class="p-4 help-card h-100">
                        <h5 class="fw-bold mb-4">Repair request</h5>
                        <p class="mb-5">Repair request service conveniently online.</p>
                        <div class="arrow-circle mt-3"><img src="../../img/ozone/arrow-left.svg" class="img-fluid" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="modal fade " id="loginmodal" aria-hidden="true" aria-labelledby="loginmodalLabel" tabindex="-1">
        <div class="modal-dialog modal-fullscreen modal-dialog-right">
            <div class="modal-content login">
                <div class="login-container">
                    <div class="position-relative">
                        <span class="substract"></span>
                        <div class="container">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                                    src="../../img/ozone/close-circle.png" width="40" height="40" /></button>
                            <div class="form-login my-5">
                                <div class="logo">
                                    <img src="../../img/ozone/logo.png" class="img-fluid">
                                    <span style="font-weight: 600;">Log in</span>
                                </div>
                                <!-- Social Icons -->
                                <div class="social-icons my-5">
                                    <button><img src="https://img.icons8.com/color/24/google-logo.png" /><span> Sign in
                                            with
                                            Google</span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/facebook-new.png" /><span>
                                            Sign
                                            in with Google</span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/mac-os.png" /><span> Sign in
                                            with
                                            Google</span></button>
                                </div>

                                <!-- Divider -->
                                <div class="divider">OR</div>

                                <!-- Email Field -->
                                <div class="input-group email mb-4">
                                    <label>Enter your email address</label>
                                    <input type="text" placeholder="Username or email address" />
                                </div>

                                <!-- Password Field -->
                                <div class="input-group">
                                    <label>Enter your Password</label>
                                    <input type="password" placeholder="Password" />
                                </div>

                                <div class="forgot-password">Forgot Password</div>

                                <!-- Login Button -->
                                <button class="btn-glow btn-login my-5">Log in</button>

                                <!-- Register Button -->
                                <button class="btn-glow btn-register" data-bs-target="#loginmodal2"
                                    data-bs-toggle="modal" data-bs-dismiss="modal" aria-label="Close">Register here
                                    !</button>

                                <!-- Footer -->
                                <div class="footer-text">
                                    if you don't have an account register, You can
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="loginmodal2" aria-hidden="true" aria-labelledby="loginmodalLabel2" tabindex="-1">
        <div class="modal-dialog modal-fullscreen modal-dialog-right">
            <div class="modal-content login">

                <div class="login-container">
                    <div class="position-relative">
                        <span class="substract"></span>
                        <div class="container">
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><img
                                    src="../../img/ozone/close-circle.png" width="40" height="40" /></button>
                            <div class="form-login my-5">
                                <div class="logo">
                                    <img src="../../img/ozone/logo.png" class="img-fluid">
                                    <span style="font-weight: 600;">Register</span>
                                </div>
                                <!-- Social Icons -->
                                <div class="social-icons my-5">
                                    <button><img src="https://img.icons8.com/color/24/google-logo.png" /><span> Sign in
                                            with
                                            Google</span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/facebook-new.png" /><span>
                                            Sign
                                            in with Google</span></button>
                                    <button><img src="https://img.icons8.com/ios-filled/24/mac-os.png" /><span> Sign in
                                            with
                                            Google</span></button>
                                </div>

                                <!-- Divider -->
                                <div class="divider">OR</div>
                                <label>Enter your email address</label>
                                <div class="d-flex my-4 ">
                                    <!-- Email Field -->
                                    <div class="input-group email ">

                                        <input type="text" placeholder="Username or email address" />
                                    </div>
                                    <button class="verify-btn">
                                        Verify Email
                                    </button>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <!-- Email Field -->
                                    <div class="input-group email ">
                                        <input type="text" placeholder="Verification Code" />
                                        <span class="timer-inside" id="countdown">07:00</span>
                                    </div>
                                    <button class="verify-btn ms-2" onclick="restartTimer()">Resend Code</button>
                                </div>
                                <div class="error-text mb-5">Verification Code is required</div>
                                <!-- Email -->


                                <!-- Full Name -->
                                <div class="mb-4 input-group ">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" class="form-control rounded-pill"
                                        placeholder="Enter your full Name" />
                                </div>

                                <!-- Password -->
                                <div class="mb-4 input-group ">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control rounded-pill"
                                        placeholder="Enter Password" />
                                </div>

                                <!-- Confirm Password -->
                                <div class="mb-4 input-group ">
                                    <label class="form-label">Confirm Password</label>
                                    <input type="password" class="form-control rounded-pill"
                                        placeholder="Confirm Password" />
                                </div>

                                <!-- Terms -->
                                <div class="form-check mb-4 ">
                                    <input class="form-check-input" type="checkbox" id="terms" />
                                    <label class="form-check-label terms-condition" for="terms">
                                        By signing up you agree to our <a href="" class="terms">Terms &
                                            Conditions</span>
                                    </label>
                                </div>



                                <!-- Register Button -->
                                <button class="btn-glow btn-login my-5" data-bs-target="#loginmodal"
                                    data-bs-toggle="modal" data-bs-dismiss="modal" aria-label="Close">Register
                                    Account</button>


                                <!-- Login Button -->
                                <button class="btn-glow btn-register">Log In</button>

                                <div class="footer-text">Already have an account?</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php $this->append('add_js'); ?>


<script>
        const thumbSwiper = new Swiper(".thumb-swiper", {
            spaceBetween: 10,
            slidesPerView: 5,
            freeMode: true,
            watchSlidesProgress: true,
        });

        const mainSwiper = new Swiper(".main-swiper", {
            spaceBetween: 10,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
            thumbs: {
                swiper: thumbSwiper,
            },
            loop: false,
            on: {
                init: function () {
                    document.querySelector('.total-slides').textContent = String(this.slides.length).padStart(2, '0');
                    document.querySelector('.current-slide').textContent = String(this.realIndex + 1).padStart(2, '0');
                },
                slideChange: function () {
                    document.querySelector('.current-slide').textContent = String(this.realIndex + 1).padStart(2, '0');
                }
            }
        });
    </script>
    
<script>
// --- AJAX Reviews with Infinite Scroll ---
(function() {
    const productId = <?= json_encode($productData['id']) ?>;
    let currentPage = 1;
    let currentSort = 'newest';
    let lastPage = 1;
    let loading = false;
    let reviewsLoaded = 0;
    const reviewsPerPage = 1; // Change as needed

    // Get CSRF token from meta tag (CakePHP default)
    const csrfToken = $('meta[name="csrfToken"]').attr('content');

    function renderReviewCard(review) {
        const stars = '★'.repeat(review.rating) + '☆'.repeat(5 - review.rating);
        const profilePhoto = review.Customers && review.Customers.profile_photo
            ? review.Customers.profile_photo
            : 'https://i.pravatar.cc/40';
        const reviewer = review.Users && review.Users.first_name && review.Users.last_name
            ? `${review.Users.first_name} ${review.Users.last_name}`
            : 'Anonymous';
        const reviewerRole = review.Users && review.Users.role
            ? review.Users.role
            : '';
        const date = review.created ? (new Date(review.created)).toLocaleDateString() : '';
        return `
            <div class="mb-4 p-4 bg-white review-card">
                <div class="review-header d-flex justify-content-between">
                    <div class="star-rating">${stars}</div>
                    <div class="review-footer">${date}</div>
                </div>
                <p class="mt-3">${review.review || ''}</p>
                <div class="d-flex align-items-center mt-3">
                    <img src="${profilePhoto}" class="rounded-circle me-2" alt="User">
                    <div>
                        <strong>${reviewer}</strong><br>
                        <small>${reviewerRole}</small>
                    </div>
                </div>
            </div>
        `;
    }

    function showLoading(show) {
        $('#reviewsLoadingSpinner').toggle(show);
    }

    function showNoMoreReviews(show) {
        $('#noMoreReviewsMsg').toggle(show);
    }

    function showNoReviews(show) {
        $('#noReviewsMsg').toggle(show);
    }

    function loadReviews(page = 1, sortBy = 'newest', append = false) {
        if (loading) return;
        loading = true;
        showLoading(true);
        showNoMoreReviews(false);
        showNoReviews(false);

        $.ajax({
            url: '/home/<USER>',
            method: 'POST',
            data: {
                product_id: productId,
                sortBy: sortBy,
                page: page,
                limit: reviewsPerPage
            },
            beforeSend: function(xhr) {
                if (csrfToken) {
                    xhr.setRequestHeader('X-CSRF-Token', csrfToken);
                }
            },
            success: function(res) {
                showLoading(false);
                loading = false;
                const reviews = Array.isArray(res.data) ? res.data : [];
                // Read new pagination keys
                let totalPages = 1;
                let hasMore = false;
                let currentPageResp = 1;
                if (res.pagination) {
                    totalPages = parseInt(res.pagination.total_pages, 10) || 1;
                    hasMore = !!res.pagination.has_more;
                    currentPageResp = parseInt(res.pagination.current_page, 10) || 1;
                }
                lastPage = totalPages;
                currentPage = currentPageResp;

                if (!append) {
                    $('#ajaxReviewsContainer').empty();
                    reviewsLoaded = 0;
                }

                reviews.forEach(function(review) {
                    $('#ajaxReviewsContainer').append(renderReviewCard(review));
                    reviewsLoaded++;
                });

                if (reviewsLoaded === 0) {
                    showNoReviews(true);
                    showNoMoreReviews(false);
                    return;
                }

                // Show "No more reviews" if no more pages
                if (!hasMore || currentPage >= totalPages) {
                    showNoMoreReviews(true);
                } else {
                    showNoMoreReviews(false);
                }
            },
            error: function() {
                showLoading(false);
                loading = false;
                if (!append) {
                    $('#ajaxReviewsContainer').empty();
                }
                showNoReviews(true);
                showNoMoreReviews(false);
            }
        });
    }

    // Initial load
    $(document).ready(function() {
        loadReviews(1, currentSort, false);
    });

    // Sort change
    $('#reviewSortBy').on('change', function() {
        currentSort = $(this).val();
        currentPage = 1;
        reviewsLoaded = 0;
        loadReviews(currentPage, currentSort, false);
    });

    // Infinite scroll for reviews
    $(window).on('scroll', function() {
        if (loading || currentPage >= lastPage) return;
        const reviewsBottom = $('#ajaxReviewsContainer').offset().top + $('#ajaxReviewsContainer').outerHeight();
        const windowBottom = $(window).scrollTop() + $(window).height();
        if (windowBottom + 100 >= reviewsBottom) {
            currentPage++;
            loadReviews(currentPage, currentSort, true);
        }
    });
})();

// Add to Cart functionality for product page
$(document).ready(function() {
    $('#addToCartBtn').on('click', function(e) {
        e.preventDefault();

        const productId = $(this).data('product-id');
        const quantity = parseInt($('#unitInput2').val()) || 1;
        const installationCharge = $('#installationChargeCheckbox').is(':checked') ? 1 : 0;
        const button = $(this);
        const originalContent = button.html();

        // Show loading state
        button.prop('disabled', true).html(`
            <i class="fas fa-spinner fa-spin me-2"></i> Adding...
        `);

        // Call the same addToCart function as list.php
        addToCartProduct(productId, quantity, installationCharge)
            .then(response => {
                if (response.status === 'success') {
                    // Update cart count if provided
                    if (response.cartCount !== undefined && window.CartUpdater) {
                        window.CartUpdater.updateCartCount(response.cartCount);
                    }

                    // Show success state
                    button.html(`
                        <i class="fas fa-check me-2"></i> Added!
                    `);

                    // Show success message
                    showToastMessage(response.message || 'Product added to cart successfully!', 'success');

                    // Update the page to show "Go to Cart" button after 2 seconds
                    setTimeout(() => {
                        // Reload page to update cart status
                        location.reload();
                    }, 2000);
                } else {
                    button.prop('disabled', false).html(originalContent);
                    showToastMessage(response.message || 'Failed to add product to cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error adding to cart:', error);
                button.prop('disabled', false).html(originalContent);
                showToastMessage('Failed to add product to cart', 'error');
            });
    });
});

// Helper function for add to cart (same as list.php)
function addToCartProduct(productId, quantity = 1, installationCharge = 0) {
    return new Promise((resolve, reject) => {
        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                         document.querySelector('input[name="_csrfToken"]')?.value ||
                         '<?= $this->request->getAttribute('csrfToken') ?>';

        $.ajax({
            headers: {
                'X-CSRF-Token': csrfToken
            },
            url: "<?= $this->Url->build(['controller' => 'Cart', 'action' => 'addToCart']) ?>/" + productId,
            type: 'POST',
            data: {
                quantity: quantity,
                installation_charge: installationCharge
            },
            success: function (response) {
                resolve(response);
            },
            error: function (xhr, status, error) {
                reject('An error occurred: ' + error);
            }
        });
    });
}

// Wishlist functionality is now handled by website.php layout
</script>

<?php $this->end(); ?>
